const path = require("path");
const webpack = require("webpack");
const CompressionPlugin = require("compression-webpack-plugin");
const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
// const fs = require("fs");
const proMode = process.env.NODE_ENV == "production";

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  lintOnSave: false,
  publicPath: process.env.NODE_ENV != "development" ? "./" : "/",
  configureWebpack: {
    output: proMode
      ? {
          filename: `[name].[contenthash].js`,
          chunkFilename: `[name].[contenthash].js`,
        }
      : {
          filename: `[name].[hash].js`,
          chunkFilename: `[name].[hash].js`,
        },
    plugins: proMode
      ? [
          // new BundleAnalyzerPlugin(),
          // Ignore all locale files of moment.js
          new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
          new CleanWebpackPlugin(),
          new webpack.HashedModuleIdsPlugin(),
          // ����compression-webpack-pluginѹ��
          new CompressionPlugin({
            algorithm: "gzip",
            test: /\.(js|css|less)$/,
            threshold: 10240,
            minRatio: 0.8,
          }),
          new webpack.optimize.LimitChunkCountPlugin({
            maxChunks: 20,
            minChunkSize: 5 * 100 * 1024,
          }),
        ]
      : [],
    optimization: proMode
      ? {
          minimizer: [
            new UglifyJsPlugin({
              uglifyOptions: {
                warnings: false,
                // ɾ��console debugger
                compress: {
                  drop_console: true, // console
                  drop_debugger: true,
                  collapse_vars: true,
                  reduce_vars: true,
                  pure_funcs: ["console.log", "console.info", "console.warn"], // �Ƴ�console
                },
                // ɾ��ע��
                output: {
                  comments: true, // 删除注释
                  beautify: false, // 删除空格等
                },
              },
              parallel: true,
              cache: true,
              extractComments: false,
            }),
          ],
          runtimeChunk: {
            name: "manifest",
          },
          splitChunks: {
            maxInitialRequests: 3,
            maxAsyncRequests: 5,
            minSize: 100 * 1024,
            maxSize: 5 * 100 * 1024,
            cacheGroups: {
              vendors: {
                test: /[\\/]node_modules[\\/]/,
                priority: -10,
              },
              default: {
                minChunks: 2,
                priority: -20,
                reuseExistingChunk: true,
              },
              commons: {
                name: "commons",
                chunks: "all",
                minSize: 100 * 1024,
                maxSize: 5 * 100 * 1024,
                reuseExistingChunk: true,
              },
            },
          },
        }
      : {},
  },
  chainWebpack: (config) => {
    config.resolve.alias.set("@$", resolve("src"));
    config.module
      .rule("vxe")
      .test(/\.js$/)
      .include.add(resolve("node_modules/vxe-table"))
      .add(resolve("node_modules/vxe-table-plugin-antd"))
      .end()
      .use()
      .loader("babel-loader")
      .options({
        cacheDirectory: true,
      })
      .end();
    config.module.rule("svg").exclude.add(resolve("src/assets/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();
  },
  devServer: {
    port: 8888,
    hot: true,
    proxy: {
      "/isolar-openplatform": {
        target: "http://*********",
        ws: false,
        changeOrigin: true,
        pathRewrite: { "^/isolar-openplatform": "" },
      },
    },
  },
  css: {
    loaderOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          "primary-color": "#3DABFF",
        },
      },
    },
  },
  pluginOptions: {
    "style-resources-loader": {
      preProcessor: "less",
      patterns: [path.resolve(__dirname, "./src/assets/variables.less")],
    },
  },
};
