// 健康分析图表配置
export const numLevelEnum = [
  {
    name: "<75分",
    key: "lower75",
    color: "#E48C8F",
    type: 1,
    gradient: ["#F97475", "#7575A0"],
    tip: "急需重点关注设备问题",
    level: "紧急",
  },
  {
    name: "75-80分",
    key: "lower80",
    color: "#FFB690",
    type: 2,
    gradient: ["#F89D69", "#6F565C"],
    tip: "频繁故障或关键部件损坏，存在安全隐患",
    level: "严重",
  },
  {
    name: "80-85分",
    key: "lower85",
    color: "#FFCC80",
    type: 3,
    gradient: ["#FDC675", "#777169"],
    tip: "性能明显下降，可能出现间歇性故障",
    level: "预警",
  },
  {
    name: "85-90分",
    key: "lower90",
    color: "#A7BDD0",
    type: 4,
    gradient: ["#A8BED1", "#375889"],
    tip: "效率降低，需监控关键参数",
    level: "一般",
  },
  {
    name: "90-95分",
    key: "lower95",
    color: "#B7AEFC",
    type: 5,
    gradient: ["#B6ADFC", "#598EE8"],
    tip: "性能略有下降，但不影响正常功能",
    level: "良好",
  },
  {
    name: ">95分",
    key: "upper95",
    color: "#85CAFF",
    range: [95, 100],
    type: 6,
    gradient: ["#40AAFF", "#266FB9"],
    tip: "设备处于最佳运行状态，无异常",
    level: "优",
  },
];
export const itemStyleColor = {
  type: "linear",
  x: 0,
  y: 0,
  x2: 0,
  y2: 1,
  colorStops: [
    {
      offset: 0,
      color: "rgba(64, 170, 255, 0)", // 0% 处的颜色
    },
    {
      offset: 1,
      color: "rgba(64, 170, 255, 0.53)", // 100% 处的颜色
    },
  ],
  global: false, // 缺省为 false
};
export const itemStyleColorFn = (
  start = "rgba(64, 170, 255, 0)",
  end = "rgba(64, 170, 255, 0.4)"
) => ({
  type: "linear",
  x: 0,
  y: 0,
  x2: 0,
  y2: 1,
  colorStops: [
    {
      offset: 0,
      color: start, // 0% 处的颜色
    },
    {
      offset: 1,
      color: end, // 100% 处的颜色
    },
  ],
  global: false, // 缺省为 false
});
// 设备健康分析图表配置
export const healthAnalysisChartConfig = (data = {}) => {
  return {
    grid: {
      left: "2%",
      right: "2%",
      top: "10%",
      bottom: "8%",
    },
    tooltip: {
      trigger: "axis",
      triggerOn: "mousemove|click",
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: itemStyleColor,
        },
      },
      className: "di-chart-tooltip",
      borderColor: "rgba(189, 226, 255, 0.4)",
      borderWidth: 1,
      formatter: (params) =>
        `
            <div class="title font-14">${params[0]?.data?.name}</div>
            <div class="content">
              <div  class="content-item">
                <div class="flex-start">
                  ${params[0].marker}
                  <span class="tooltip-item-label">设备台数：</span>
                </div>
                <span>
                  <span class="color-text-white num-font-500">${
                    params[0].value ?? "--"
                  }</span>
                  <span class="content-unit">台</span>
                </span>
              </div>
            </div>
        `,
    },
    xAxis: [
      {
        type: "category",
        axisLabel: {
          color: "rgba(255, 255, 255, 0.7)",
        },
        data: numLevelEnum.map((item) => ({
          value: `${item.name}(${item.level})`,
          level: item.level,
          name: item.name,
        })),
        axisTick: {
          show: false,
        },
      },
      {
        type: "category",
        show: false,
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: { color: "rgba(255, 255, 255, 0.7)" }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
        splitLine: {
          lineStyle: {
            color: "rgba(97, 161, 213, 0.19)",
          },
        },
        name: "台",
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.7)",
          padding: [0, 0, 0, -25],
        },
      },
    ],
    series: [
      {
        name: "设备台数",
        data: numLevelEnum.reduce((acc, cur) => {
          return acc.concat({
            name: cur?.name,
            value: data?.[cur.key],
            label: {
              show: true,
              position: "top",
              color: "#fff",
            },
            itemStyle: {
              color: itemStyleColorFn(cur?.gradient[0], cur?.gradient[1]),
            },
          });
        }, []),
        type: "bar",
        barWidth: "25%",
        xAxisIndex: 0,
        yAxisIndex: 0,
      },
      {
        name: "背景",
        type: "bar",
        barWidth: "50%",
        xAxisIndex: 1,
        yAxisIndex: 0,
        data: [0, 0, 0, 0, Math.max(...Object.values(data)), 0],
        itemStyle: {
          color: itemStyleColor,
        },
        selectedMode: "single",
        select: {
          itemStyle: {
            borderColor: "none",
          },
        },
        z: 10,
      },
    ],
  };
};

export const deviceAnalysisColumns = [
  {
    field: "time",
    title: "日期",
    width: 80,
    sortable: false,
    fixed: "left",
    align: "left",
  },
  {
    field: "deviceName",
    title: "逆变器编号",
    align: "left",
  },
  {
    field: "healthLevel",
    title: "健康度",
    width: 60,
  },
  {
    field: "faultIndex",
    title: "故障指数",
  },
  {
    field: "warningPotential",
    title: "安全指数",
  },
  {
    field: "powerLevel",
    title: "稳定性指数",
  },
  {
    field: "runPerformance",
    title: "性能指数",
  },
  {
    field: "stopDuration",
    title: "停机小时数",
    multiple: 1 / 3600,
  },
  {
    field: "stopFrequency",
    title: "停机次数",
  },
  {
    field: "Hindfrequency",
    title: "隐患次数",
  },
  {
    field: "sbpjdxxss",
    title: "平均等效利用小时数",
    width: 160,
  },
  {
    field: "sbdxxss",
    title: "等效利用小时数",
    width: 160,
  },
  {
    field: "sbzhxl",
    title: "转换效率 (%)",
    width: 90,
    multiple: 100,
  },
];
// 生成健康度趋势折线图配置
export const healthTrendChartConfig = (dataArr = []) => {
  const xData = dataArr.map((o) => o.day);
  return {
    grid: {
      bottom: "20%",
    },
    dataZoom: [
      {
        type: "slider", // slider表示有滑动块的，
        show: true,
        showDetail: false,
        bottom: 27,
        xAxisIndex: xData.map((_, index) => index), // 表示x轴折叠
        start: 0, // 数据窗口范围的起始百分比
        end: 100, // 数据窗口范围的结束百分比
      },
      {
        type: "inside",
        xAxisIndex: xData.map((_, index) => index),
      },
    ],
    tooltip: {
      trigger: "axis",
      triggerOn: "mousemove|click",
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: itemStyleColor,
        },
      },
      className: "di-chart-tooltip",
      borderColor: "rgba(189, 226, 255, 0.4)",
      borderWidth: 1,
      formatter: (params) => {
        let html = `
               <div class="title font-14">${params[0].axisValue}</div>
               <div class="content">
                  <div  class="content-item flex-space-between">
                    <div class="flex-start">
                      <span class="tooltip-item-label">健康度：</span>
                    </div>
                    <span>
                      <span class="color-text-white num-font-500">${
                        params[0].value || "--"
                      } </span>
                      <span class="content-unit">分</span>
                    </span>
                  </div>
                </div>
                `;
        return html;
      },
    },
    xAxis: {
      type: "category",
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
      },
      data: xData,
    },
    yAxis: {
      type: "value",
      axisLabel: { color: "rgba(255, 255, 255, 0.7)" }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
      splitLine: {
        lineStyle: {
          color: "rgba(97, 161, 213, 0.19)",
        },
      },
      name: "健康度(分)",
      nameTextStyle: {
        color: "#8DCBFB",
        padding: [0, 0, 0, -25],
      },
    },
    series: [
      {
        name: "健康度",
        type: "line",
        data: dataArr.map((o) => o.healthLevel?.toFixed(2)),
      },
    ],
  };
};
