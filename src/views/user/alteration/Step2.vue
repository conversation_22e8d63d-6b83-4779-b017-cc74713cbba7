<template>
  <div>
    <a-form
      :form="form"
      style="max-width: 500px; margin: 40px auto 0"
      @keyup.enter.native="nextStep"
    >
      <get-code
        smsMode="3"
        @ok="getCode"
        ref="getCode"
        :isForget="true"
      ></get-code>
      <a-form-item
        :wrapperCol="{ span: 24, offset: 0 }"
        style="margin-top: 80px"
      >
        <router-link
          style="float: left; line-height: 44px"
          :to="{ name: 'login' }"
          >使用已有账户登录
        </router-link>
        <a-button @click="nextStep" class="next-step">下一步</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import GetCode from "@/components/com/GetCode";

export default {
  name: "Step2",
  props: ["userList"],
  components: { GetCode },
  data() {
    return {};
  },
  computed: {},
  methods: {
    getCode(userList) {
      this.loading = false;
      if (userList) {
        setTimeout(() => {
          this.$emit("nextStep", userList);
        }, 0);
      }
    },
    nextStep() {
      let that = this;
      that.loading = true;
      this.$refs.getCode.validateForm();
    },
  },
};
</script>

<style lang="less" scoped>
.stepFormText {
  margin-bottom: 24px;
}

.ant-form-item-label,
.ant-form-item-control {
  line-height: 22px;
}

.getCaptcha {
  display: block;
  width: 100%;
  height: 40px;
}

.next-step {
  width: 185px;
  height: 44px;
  border-radius: 6px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px 24px;
  background: #0077d1;
  float: right;
  color: white;
  border-color: transparent;

  &:hover {
    color: white;
  }
}
</style>
