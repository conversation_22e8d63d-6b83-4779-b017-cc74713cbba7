<template>
  <div class="alteration" style="padding: 48px 0 28px">
    <a-steps class="steps" :current="currentTab">
      <a-step title="手机验证" />
      <a-step title="密码" />
      <a-step title="完成" />
    </a-steps>
    <div class="content">
      <step2 v-if="currentTab === 0" @nextStep="nextStep" />
      <step3
        v-if="currentTab === 1"
        @nextStep="nextStep"
        @prevStep="prevStep"
        :userList="userList"
      />
      <step4
        v-if="currentTab === 2"
        @prevStep="prevStep"
        @finish="finish"
        :userList="userList"
      />
    </div>
  </div>
</template>

<script>
import Step2 from "./Step2";
import Step3 from "./Step3";
import Step4 from "./Step4";

export default {
  name: "Alteration",
  components: {
    Step2,
    Step3,
    Step4,
  },
  data() {
    return {
      description:
        "将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。",
      currentTab: 0,
      userList: {},
      // form
      form: null,
    };
  },
  methods: {
    // handler
    nextStep(data) {
      this.userList = data;
      if (this.currentTab < 4) {
        this.currentTab += 1;
      }
    },
    prevStep(data) {
      this.userList = data;
      if (this.currentTab > 0) {
        this.currentTab -= 1;
      }
    },
    finish() {
      this.currentTab = 0;
    },
  },
};
</script>

<style lang="less" scoped>
@grey-color: #ccc;
.steps {
  max-width: 750px;
  margin: 16px auto;
}

:deep(.ant-steps-item-icon) {
  height: 24px;
  width: 24px;
  border-radius: 24px;
  line-height: 24px;
  margin-top: 4px;

  .anticon-check.ant-steps-finish-icon {
    font-size: 12px;
  }
}

:deep(.ant-steps-item) {
  font-size: 14px;

  .ant-steps-item-title {
    padding-right: 8px;

    &:after {
      height: 2px;
      background-color: #dcdcdc !important;
    }
  }
}

:deep(.ant-steps-item-wait) {
  .ant-steps-item-icon {
    color: @grey-color;
    border-color: @grey-color;
    background: transparent;

    .ant-steps-icon {
      color: @grey-color !important;
    }
  }

  .ant-steps-item-title {
    color: @grey-color !important;
  }
}

:deep(.ant-steps-item-active),
:deep(.ant-steps-item-finish) {
  .ant-steps-item-title {
    &::after {
      background-color: #40aaff !important;
    }
  }
}

:deep(.ant-steps-item-finish) {
  .ant-steps-item-icon {
    background: transparent;
  }

  .ant-steps-item-title {
    color: #40aaff !important;
    opacity: 0.7;
  }
}

:deep(.ant-steps-item-active) {
  .ant-steps-item-title {
    color: #40aaff !important;
  }
}

:deep(.ant-input),
:deep(.ant-input[disabled]) {
  border-radius: 3px;
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.12);

  &:hover,
  &:focus {
    color: white !important;
    caret-color: white !important;
    background: rgba(255, 255, 255, 0.1);
    border-color: #61a1d5 !important;
    box-shadow: 0 0 0 2px rgba(192, 93, 27, 0.2);
    color: white;
  }
}

:deep(.has-error .ant-input),
:deep(.has-error .ant-input:hover) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #f07072;
}

:deep(.has-error .ant-form-explain),
:deep(.has-error .ant-form-split) {
  color: #f07072;
  border-color: #f07072;
}
</style>
