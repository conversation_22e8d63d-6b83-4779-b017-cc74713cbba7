<template>
  <div>
    <a-form :form="form" style="max-width: 500px; margin: 40px auto 0">
      <a-form-item
        label="账号名"
        :labelCol="{ span: 0 }"
        :wrapperCol="{ span: 24 }"
      >
        <a-input
          type="text"
          name="username"
          size="large"
          autocomplete="off"
          :value="accountName"
          disabled
        >
        </a-input>
      </a-form-item>
      <a-form-item class="stepFormText">
        <a-input-password
          placeholder="请输入密码"
          size="large"
          v-decorator="['password', validatorRules.password]"
          autocomplete="new-password"
        >
        </a-input-password>
      </a-form-item>
      <a-form-item class="stepFormText">
        <a-input-password
          placeholder="请再次输入密码"
          size="large"
          v-decorator="['confirmPassword', validatorRules.confirmPassword]"
          autocomplete="new-password"
        >
        </a-input-password>
      </a-form-item>
      <a-form-item style="margin-top: 48px">
        <router-link
          style="float: left; line-height: 44px"
          :to="{ name: 'login' }"
          >使用已有账户登录
        </router-link>
        <a-button
          style="margin-left: 38px"
          class="preStep"
          @click="prevStep"
          size="large"
          >上一步
        </a-button>
        <a-button
          :loading="loading"
          type="primary"
          @click="nextStep"
          size="large"
          class="next-step"
          style="margin-left: 16px; width: 114px"
          >提交
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { postAction } from "@/api/manage";
import { aesEncrypt } from "@/utils/verify";

export default {
  name: "Step3",
  //    components: {
  //      Result
  //    },
  props: ["userList"],
  data() {
    return {
      loading: false,
      form: this.$form.createForm(this),
      accountName: this.userList.username,
      validatorRules: {
        username: { rules: [{ required: true, message: "用户名不能为空!" }] },
        password: {
          rules: [
            { required: true, message: "请输入新密码！" },
            { validator: this.validateToNextPassword },
          ],
        },
        confirmPassword: {
          rules: [
            { required: true, message: "请确认密码！" },
            { validator: this.handlePasswordCheck },
          ],
        },
      },
    };
  },
  methods: {
    nextStep() {
      let that = this;
      that.loading = true;
      this.form.validateFields((err, values) => {
        if (!err) {
          var params = {};
          params.username = this.userList.username;
          params.password = aesEncrypt(values.password);
          params.code = this.userList.code;
          params.mobile = this.userList.mobile;
          postAction("/sys/passwordChange", params)
            .then((res) => {
              if (res.success) {
                var userList = {
                  username: this.userList.username,
                };
                // console.log(userList);
                setTimeout(function () {
                  that.$emit("nextStep", userList);
                }, 1500);
              } else {
                this.passwordFailed(res.message);
                that.loading = false;
              }
            })
            .catch(() => {
              that.loading = false;
            });
        } else {
          that.loading = false;
        }
      });
    },
    prevStep() {
      this.$emit("prevStep", this.userList);
    },
    validateToNextPassword(rule, value, callback) {
      if (value) {
        let reg =
          /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F])[\da-zA-Z\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]{8,14}$/;
        if (!reg.test(value)) {
          callback(
            new Error(
              "密码必须包含数字、字母及特殊符号，长度8-14位，不允许有空格！"
            )
          );
        } else {
          const form = this.form;
          const confirmpassword = form.getFieldValue("confirmPassword");
          if (confirmpassword && value !== confirmpassword) {
            callback(new Error("两次输入的密码不一致！"));
          } else if (value == confirmpassword) {
            form.validateFields(["confirmPassword"], { force: true });
          }
          callback();
        }
      }
      callback();
    },
    handlePasswordCheck(rule, value, callback) {
      const form = this.form;
      let password = form.getFieldValue("password");
      if (value && password && value !== password) {
        callback(new Error("两次密码不一致！"));
      } else if (value && password && value == password) {
        form.validateFields(["password"], { force: true });
      }
      callback();
    },
    passwordFailed(err) {
      this.$notification["error"]({
        message: "更改密码失败",
        description: err,
        duration: 4,
      });
    },
  },
};
</script>
<style lang="less" scoped>
.stepFormText {
  margin-bottom: 24px;
}

.ant-form-item-label,
.ant-form-item-control {
  line-height: 22px;
}

:deep(.ant-form-explain) {
  text-align: left;
}

:deep(.ant-btn-lg) {
  border-radius: 6px;
}

:deep(.ant-btn-lg),
:deep(.ant-input-lg) {
  height: 44px;
}

:deep(.ant-input-password-icon) {
  color: #ccc;
}

.next-step {
  background: #0077d1;
}

.preStep {
  border-radius: 3px;
  opacity: 1;
  background: rgba(61, 171, 255, 0.16);
  box-sizing: border-box;
  border: 1px solid #3dabff;
  color: white;
}
</style>
