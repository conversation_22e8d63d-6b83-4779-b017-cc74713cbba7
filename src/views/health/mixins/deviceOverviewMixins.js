export const DeviceOverviewMixins = {
  methods: {
    getPng (name) {
      return require(`@/assets/images/health/overview/${name}.png`)
    },
    gotoHealthPage (data, tabName) {
      if(!data.value) {
        return
      }
      window.comeFromOverview = true;
      this.$router.push({
        name: '/health/safe',
        params: { 
          tabName: tabName,
          handleStatus: this.dataParams.type == '1' ? "01,03" : null,
          alarmReason: data.alarmReason || '',
          deviceType: data.deviceType,
          reasonName: data.reasonName || '',
          alarmRemark: data.alarmRemark,
          isFromRealtime: this.dataParams.type == '1'
        }
      });
    },
    setDeviceType (value) {
      this.deviceType = this.deviceType == value ? '0' : value;
      this.getDataList();
    }
  }
}