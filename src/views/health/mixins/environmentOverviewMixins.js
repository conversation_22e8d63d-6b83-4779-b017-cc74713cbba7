export const EnvironmentOverviewMixins = {
  methods: {
    getPng (name) {
      return require(`@/assets/images/health/overview/${name}.png`)
    },
    gotoHealthPage (data, routeName, {tabName, alarmReason}) {
      if(!data.value) {
        return
      }
      window.comeFromOverview = true;
      this.$router.push({
        name: routeName,
        params: {
          tabName: tabName,
          alarmReason: alarmReason ?? "",
          handleStatus: this.dataParams.type == '1' ? '01' : '03',
        }
      });
    },
  }
}
