<template>
  <a-row :gutter="24" class="search-content">
    <a-col class="margin-b-12" :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">电站名称</span>
        <a-input :value="psaInfo.psaNameList[0]" disabled />
      </div>
    </a-col>
    <a-col class="margin-b-12" :xxl="5" :xl="6" :md="12">
      <div class="search-item">
        <span class="search-label">发生时间</span>
        <a-range-picker v-model="searchTime" format="YYYY-MM-DD" @change="searchTimeChange"/>
      </div>
    </a-col>
    <a-col class="margin-b-12" :xxl="5" :xl="8" :md="12">
      <div class="search-item">
        <span class="search-label">诊断原因</span>
        <a-select v-model="alarmReason" @change="alarmReasonChange" placeholder="请选择" show-search
                  :filter-option="filterOption" style="width: 100%;">
          <a-select-option value="">全部</a-select-option>
          <a-select-option :value="item.reasonName+'-di-'+item.reason" v-for="item in alarmReasonOptions" :key="item.reasonName+'-di-'+item.reason">
            {{ item.reasonName }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col class="margin-b-12" :xxl="5" :xl="8" :md="12" >
      <div class="search-item">
        <span class="search-label">处理状态</span>
        <a-select v-model="searchData.alarmStatus" @change="alarmStatusChange" style="width: 100%;" showArrow mode="multiple" placeholder="请选择">
<!--          <a-select-option value="">全部</a-select-option>-->
          <a-select-option value="01">待处理</a-select-option>
          <a-select-option value="03">已派发</a-select-option>
          <a-select-option value="04">已闭环</a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col class="margin-b-12" :xxl="4" :xl="8" :md="12">
      <div class="search-item">
        <di-throttle-button label="查询" @click="doSearch"></di-throttle-button>
        <di-throttle-button label="重置" class="di-cancel-btn" @click="doReset"></di-throttle-button>
      </div>
    </a-col>
  </a-row>
</template>
<script>
import { getReasonByRemarkAndDeviceType } from '@/api/health/AlarmEvents.js';
import moment from 'moment';
export default {
  props: {
    psaInfo: {
      type: Object,
      default: () => {},
    },
    deviceType: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      allAlarmReasonOptions: [],
      alarmReasonOptions: [],
      alarmReason: '',
      searchTime: [],
      searchData: {
        startTime: null,
        endTime: null,
        alarmReason: '',
        alarmStatus: [],
      }
    }
  },
  methods: {
    moment,
    // 发生时间选择框变化事件
    searchTimeChange(val) {
      this.searchData.startTime = val[0] ? val[0].format('YYYY-MM-DD') : null; 
      this.searchData.endTime = val[1] ? val[1].format('YYYY-MM-DD') : ''; 
      this.$emit('searchParamsChange', this.searchData);
    },
    // 获取诊断原因下拉选项
    getAlarmReasionOptions (alarmReasonName) {
      let deviceType = this.deviceType.toString() || undefined;
      if (deviceType == '3') {
        deviceType = '3,12';
      }
      if (deviceType == '17') {
        deviceType = '17,6';
      }
      getReasonByRemarkAndDeviceType({ tabCode: this.type, deviceType: deviceType }).then(res => {
        this.allAlarmReasonOptions = res.result;
        this.alarmReasonOptions = this.arrUnique(this.allAlarmReasonOptions, 'reasonName');
        if(alarmReasonName) {
          let data = this.alarmReasonOptions.find(item => item.reasonName == alarmReasonName);
          this.alarmReason = data ? data.reasonName + '-di-' + data.reason : ''
          this.alarmReasonChange(this.alarmReason);
          this.doSearch();
        }
      }).catch(() => {
        if(alarmReasonName) {
           this.$emit('cancelLoading')
        }
      })
    },
    // 诊断原因下拉框变化事件
    alarmReasonChange (value) {
      if (value) {
        let sameNameArr = this.allAlarmReasonOptions.filter(item => {
          return item.reasonName == value.split('-di-')[0];
        });
        this.searchData.alarmReason = Array.from(new Set(sameNameArr.map(item => {
          return item.reason;
        }))).toString();
      } else {
        this.searchData.alarmReason = '';
      }
      let reasonName = value ? value.split('-di-')[0] : '';
      this.$emit('reasonNameChange', reasonName)
      this.$emit('searchParamsChange', this.searchData, reasonName);
    },
    // 处理状态下拉框变化事件
    alarmStatusChange() {
      this.$emit('searchParamsChange', this.searchData);
    },
    // 查询
    doSearch() {
      this.$emit('doSearch', 1, true);
    },
    // 重置
    doReset() {
      this.alarmReason = '';
      this.searchTime = [];
      this.searchData = {
        startTime: null,
        endTime: null,
        alarmReasonString: '',
        alarmStatus: [],
      }
      this.$emit('searchParamsChange', this.searchData);
      this.doSearch();
    },
    // 下拉选择框-按名称模糊搜索
    filterOption (input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    // 对象数组去重
    arrUnique (arr, key) {
      let returnArr = [];
      let obj = {};
      returnArr = arr && arr.length > 0 && arr.reduce((cur, next) => {
        // eslint-disable-next-line no-unused-expressions
        obj[next[key]] ? '' : obj[next[key]] = true && cur.push(next);
        return cur;
      }, []);
      return returnArr;
    }
   
  }
}
</script>
<style lang="less" scoped>
.ant-btn + .ant-btn {
  margin-left: 10px;
}
</style>