<template>
  <!-- <a-drawer :title="title" :visible.sync="open" width="100%" @close="handleClose()" wrap-class-name="drawer-detial-chart">
   <a-table :data-source="inverterInfo" :pagination="showPage" bordered :scroll="{x:1800,y:'40vh'}" :loading = 'inverterLoading'>
          <a-table-column
          :key="item.secondName"
          :title="item.secondName"
          :data-index="item.secondTypeCode"
          v-for="item in cols['0018']"
          align="center" width="180px" >
          </a-table-column>
    </a-table>
    <a-table :data-source="stringBoxInfo" :pagination="showPage" bordered :scroll="{x:1800,y:'40vh'}" :loading = 'stringBoxLoading'>
          <a-table-column
          :key="item.secondName"
          :title="item.secondName"
          :data-index="item.secondTypeCode"
          v-for="item in cols['0019']"
          align="center" width="180px" >
          </a-table-column>
    </a-table>
  </a-drawer> -->
  <a-modal :visible.sync="open" :title="title" @cancel="handleClose()" :footer="null" width=70%  :centered="true">
    <a-radio-group style="margin-bottom:10px">
      <a-radio-button @click="changeDev(1)" >
        逆变器
      </a-radio-button>
      <a-radio-button @click="changeDev(4)" value="default">
        汇流箱
      </a-radio-button>
    </a-radio-group>
      <a-table @change="handleTableChangeInvert" v-if="showInvert" :data-source="inverterInfo" :pagination = "pagination" size="small"  bordered :scroll="{x:800,y:'60vh'}" :loading = 'inverterLoading'>
          <a-table-column
          :key="item.secondName"
          :title="item.secondName"
          :data-index="item.secondTypeCode"
          v-for=" item in cols['0018']"
          align="center" width="160px" >
          </a-table-column>
    </a-table>
    <a-table @change="handleTableChangeBox" v-if="showBox" :data-source="stringBoxInfo" :pagination = "pagination" size="small" bordered :scroll="{x:800,y:'60vh'}" :loading = 'stringBoxLoading'>
          <a-table-column
          :key="item.secondName"
          :title="item.secondName"
          :data-index="item.secondTypeCode"
          v-for="item in cols['0019']"
          align="center" width="160px" >
          </a-table-column>
    </a-table>
    </a-modal>
</template>

<script>
import {
  getSystemCodeList,
  getStringBoxDeviceInfo,
  getInverterDeviceInfo
} from '@/api/health/healthapi.js';
export default {
  props: {
    title: {
      type: String
    },
    time: {
      type: String
    },
    psId: {
      type: [Number, String]
    },
    deviceDetailShow: {
      type: Boolean
    }
  },
  data () {
    return {
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        defaultPageSize: 10,
        showSizeChanger: true,
        defaultCurrent: 1,
        showQuickJumper: true,
        size: 'small',
        showTotal: (total) => `共 ${total} 条`
      },
      showInvert: false,
      showBox: false,
      stringBoxLoading: true,
      inverterLoading: true,
      showPage: false,
      open: true,
      cols: [],
      inverterInfo: [],
      stringBoxInfo: [],

      chartInfo: {},
      detailInfo: {}
    };
  },
  created () {
    this.getDict();
    this.showInvert = true;
  },
  methods: {
    handleTableChangeBox (pagination, filters, sorter) {
      const pager = { ...this.pagination };
      pager.current = pagination.current;
      pager.pageSize = pagination.pageSize;
      this.pagination = pager;
      this.getStringBoxDeviceInfo();
    },
    handleTableChangeInvert (pagination, filters, sorter) {
      const pager = { ...this.pagination };
      pager.current = pagination.current;
      pager.pageSize = pagination.pageSize;
      this.pagination = pager;
      this.getInverterDeviceInfo();
    },
    changeDev (type) {
      if (type == 1) {
        this.showInvert = true;
        this.showBox = false;
        this.pagination.current = 1;
        this.getInverterDeviceInfo();
      } else if (type == 4) {
        this.showInvert = false;
        this.showBox = true;
        this.pagination.current = 1;
        this.getStringBoxDeviceInfo();
      }
    },
    // true汇流箱
    getStringBoxDeviceInfo () {
      this.stringBoxLoading = true;
      this.stringBoxInfo = [];
      let obj = {
        psId: this.psId,
        time: this.time
      };
      getStringBoxDeviceInfo(obj).then((res) => {
        let data = res.result_data.pageBean;
        this.stringBoxInfo = data;
        this.stringBoxLoading = false;
      });
    },
    // false逆变器
    getInverterDeviceInfo () {
      this.inverterLoading = true;
      this.inverterInfo = [];
      let obj = {
        psId: this.psId,
        time: this.time
      };
      getInverterDeviceInfo(obj).then((res) => {
        let data = res.result_data.pageBean;
        this.inverterInfo = data;
        this.inverterLoading = false;
      });
    },
    getDict () {
      // 0018 逆变器,0019 汇流箱
      getSystemCodeList({ firstTypeCode: '0018,0019' }).then((res) => {
        this.cols = [];
        this.cols = res.result_data;
      });
    },
    handleClose () {
      this.open = false;
      this.$parent.deviceDetailShow = false;
      this.stringBoxLoading = true;
      this.inverterLoading = true;
    },
    // 父组件调用打开drawer
    openDrawer () {
      this.open = true;
      this.getDict();
      this.showInvert = true;
      this.getInverterDeviceInfo();
    }
  }
};
</script>

<style lang="less" scoped>
  .drawer-detial-chart {
    .drawer-main {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .toggle-detail-button{
        height: 80vh;
        border:dashed 1px transparent;
        &:hover {
          border:dashed 1px #1890FF;
        }
      }
    }
  }
</style>
