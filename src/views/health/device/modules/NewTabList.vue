<template>
    <div class="tab-list">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="tab-item cursor-pointer"
        @click="handleChange(index)"
      >
        <div class="left-img-box">
          <img
            class="width-100 height-100"
            :src="item.imgSrc"
            :alt="item.remark"
          />
        </div>
        <div class="right-text-box">
          <div class="text-box flex-space-between" style="line-height: 26px">
            <span class="text">{{ item.remark }}</span>
            <span class="total num-font-700">{{ total[item.keyValue] }}</span>
          </div>
        </div>
      </div>
      <div v-if="activeKey !== ''" class="active-tab" :style="{ left: left + 'px' }">
        <img
          class="width-100"
          src="@/assets/images/tab-active.png"
          alt="当前激活的导航栏"
        />
      </div>
    </div>
  </template>
<script>
export default {
  name: "NewTabList",
  props: {
    list: {
      type: Array,
      default: () => [],
      required: true,
    },
    total: {
      type: Object,
      default: () => {
        return {
          1: 0,
          2: 0,
          3: 0,
          4: 0
        }
      }
    }
  },
  data() {
    return {
      activeKey: '',
      left: 0,
    };
  },
  mounted() {
    window.addEventListener("resize", this.setLeft);
  },
  methods: {
    handleChange(index) {
      let { remark, keyValue } = this.list[index]
      if(this.activeKey === index) {
        this.activeKey = '';
        keyValue = '';
      } else {
        this.activeKey = index;
      }
      this.$emit("change", keyValue, remark);
    },
    async setLeft() {
      await this.$nextTick();
      const tabItemEl = $(".tab-item");
      this.left =
        this.activeKey * (tabItemEl.innerWidth() + 16) +
        (tabItemEl.innerWidth() - 152) / 2;
    },
  },
  watch: {
    activeKey: {
      immediate: true,
      handler(val) {
        if(val !== '') {
          this.setLeft();
        }
      },
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setLeft);
  },
};
  </script>
  <style scoped lang="less">
  .tab-list {
    display: flex;
    position: relative;
  
    .tab-item {
      display: flex;
      align-items: center;
      padding: 2px 17px 2px 8px;
      border-radius: 4px;
      width: 176px;
      height: 44px;
      background: linear-gradient(180deg, #18488b 0%, #0e4085 100%);
  
      &:hover {
        background: linear-gradient(180deg, #2860af 0%, #2259a4 100%);
      }
  
      &:not(:last-child) {
        margin-right: 16px;
      }
  
      .left-img-box {
        width: 40px;
        height: 40px;
  
        img {
          object-fit: cover;
        }
      }
  
      .right-text-box {
        margin-left: 4px;
        flex: 1;
  
        .text-box {
          .text {
            font-size: 16px;
            font-weight: 600;
            color: #85caff;
          }
  
          .total {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
          }
        }
      }
    }
  
    .active-tab {
      position: absolute;
      width: 152px;
      top: 75%;
      transition: 0.5s;
    }
  }
  </style>
  