<template>
  <a-drawer
    width="100%"
    :header-style="{ height: '55px', background: 'transparent' }"
    :visible="visible"
    @close="close"
    :destroyOnClose="true"
    :get-container="getContainer"
    :maskClosable="false"
    :wrap-style="{ position: 'absolute' }"
    class="drawer-box"
    v-bind:class="{ 'no-parentId': true }"
    title="详情"
  >
    <div class="drawer-form-com">
      <div class="drawer-form-content">
        <a-spin size="small" :spinning="loading" style="height: 100%">
          <detail-layout class="margin-t-16" :labelList="orderBaseInfoList" :form="detailData.baseInfo" title="基本信息" />
          <detail-layout class="margin-t-16" :labelList="orderDeviceInfoList" :form="detailData.deviceInfo" title="设备信息" />
          <detail-layout class="margin-t-16" :labelList="orderTicketInfoList"  title="两票信息">
            <template v-slot:ticketNo>
              <a-col v-for="item in detailData.ticketList" :key="item.ticketNo" :span="24" class="detail_layout_content">
                <span class="left">关联两票</span>
                <span class="right" >{{ item.ticketNo || '--' }}</span>
              </a-col>
            </template>
          </detail-layout> 
          <detail-layout class="margin-t-16" :labelList="orderExecuteInfoList" :form="detailData.execute" title="执行信息" />
          <detail-layout class="margin-t-16" :labelList="[]" :form="{}" title="作业步骤" />
          <time-line v-if="stepsList.length > 0" :activities="stepsList" />
        </a-spin>
      </div>
      <div class="drawer-form-foot">
        <DiThrottleButton label="返回" class="di-cancel-btn" @click="close"/>
      </div>
    </div>
  </a-drawer>
</template>
<script>
import TimeLine from './TimeLine';
import { orderBaseInfoList, orderDeviceInfoList, orderTicketInfoList, orderExecuteInfoList } from "../../config";
export default {
  components: { TimeLine },
  data () {
    return {
      visible: false,
      orderBaseInfoList,
      orderDeviceInfoList,
      orderTicketInfoList,
      orderExecuteInfoList,
      loading: false, 
      stepsList: [],
      detailData: {
        baseInfo: {},
        deviceInfo: {},
        ticketList: {},
        execute: {}
      }
    }
  },
  methods: {
    getContainer() {
      return document.querySelector(".main-parent");
    },
    init (detailData) {
      this.visible = true;
      this.detailData = detailData;
      let faultFileList = detailData.baseInfo.faultFileList;
      if(faultFileList && faultFileList.length > 0) {
        this.detailData.baseInfo.faultFileList = faultFileList.map(item => {return { path: item }}) 
      } else {
        this.detailData.baseInfo.faultFileList = [];
      }
      let stepsList = detailData.stepList;
      if(stepsList && stepsList.length > 0) {
        this.stepsList = stepsList.map(item => {
          return {
            stepName: item.stepName,
            workDesc: item.workDesc,
            stepPictureList: item.stepPictureList.map(ele => {return { path: ele }})
          }
        })
      } else {
        this.stepsList = [];
      } 
    },
    close() {
      this.visible = false;
      Object.assign(this.$data, this.$options.data());
      this.$emit('close');
    }
  }
}
</script>
