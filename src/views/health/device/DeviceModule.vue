<template>
  <div class="height-100">
    <a-spin :spinning="loading" class="width-100 height-100">
      <a-tabs v-model="deviceType" @change="deviceTypeChange"	:animated="false">
        <a-tab-pane v-for="item in deviceTabList" :key="item.keyValue" :tab="item.remark +'('+ item.total + ')'">
        </a-tab-pane>
      </a-tabs>
      <NewTabList ref="tabList" class="margin-b-16" :list="activeDiaList" :total="typeTotal" @change="typeChange"/>
      <div class="table-container" id="AlarmCenter">
        <search-module ref="searchModule" :psaInfo="psaInfo" :deviceType="deviceType" :type="type" 
          @searchParamsChange="searchParamsChange" @doSearch="pageChange" @reasonNameChange="reasonNameChange"
          @cancelLoading="loading = false"/>
        <div class="margin-t-4 margin-b-16 divide-line"></div>
        <div class="operation" style="height: 32px">
           <div class="operation-btn" style="justify-content: flex-end;">
            <div> 
              <a-checkbox ref="faultStatus" :defaultChecked="true" :checked="faultStatus == '1'"
                          @change="faultStatusChange" class="faultStatusBox">只看持续</a-checkbox>
              <a-dropdown :disabled="isShowBatchBtn || isIncludeStorage" v-if="(type == '1' || type == '2') && dealStatus == '01'">
                <a-menu slot="overlay" @click="handleImportMenuClick">
                  <a-menu-item key="1">添加挂牌</a-menu-item>
                  <a-menu-item key="2">取消挂牌</a-menu-item>
                </a-menu>
                <a-button class='di-ant-btn-cncel'  v-has="'alarmCenter:batchEdit'">批量挂牌<a-icon type="down" /></a-button>
              </a-dropdown>
              <di-throttle-button label="导出" @click="doExport" v-has="'alarmCenter:export'" 
                class="di-cancel-btn export-btn margin-l-12"/>
            </div>
          </div>
        </div>
        <vxe-table :tree-config="treeObj" :data="data" :height="tableHeight + 24" ref="multipleTable"
          resizable show-overflow size="small" :row-class-name="rowClassName" highlight-hover-row
          :seq-config="{ startIndex: (page - 1) * size }" @scroll="onScroll" @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange" class="my-table" border="none">
          <vxe-table-column type="checkbox" v-if="(type == '1' || type == '2') && dealStatus == '01'" :width="60">
          </vxe-table-column>
          <vxe-table-column type="seq" :width="80" title="序号">
            <template v-slot="{ row, rowIndex }">   
            <span :class="{ 'expand-index': rowIndex == '-1' }">{{ rowIndex != '-1' ? (((page - 1) * size) + rowIndex +
                1) : row.index }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :tree-node="isShowTree" field="alarmGrade" title="诊断等级" width="110">
            <template #header="{ column }">
              <table-sort :isUp="isUp" :column="column" filed="alarmGrade" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row }">
              <div class="alarm-grade align-center">
                <img v-if="row.alarmGrade && ['1', '2', '3', '4'].includes(row.alarmGrade)"
                  :src="getGradePng(row.alarmGrade)">
                <span :title="row.alarmGradeName || '--'">{{ row.alarmGradeName || '--' }}</span>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column v-for="item in columns" :key="item.field" :title="item.title" :field="item.field" 
            :min-width="item.width" :visible="!item.status || item.status.includes(dealStatus)">
            <template v-if="item.hasSort" #header="{ column }">
              <table-sort :isUp="isUp" :column="column" :filed="item.field" @sortChange="mySortChange" :downSort="downSort"
                :upSort="upSort" :sortFiled="sortFiled" />
            </template>
            <template v-slot="{ row, rowIndex }">
              <span v-if="item.field == 'deviceNameShow'" @click="expandTree(row, rowIndex)" :class="{ 'change-color': !row.id }">
                {{ row.deviceNameShow || '--'}}
                </span>
              <template v-else-if="item.field == 'alarmReasonName'">
                <div style="display: inline-block; align-items: center">
                  <div v-if="row.tagName" class="tag-name">
                    <div class="div-center">
                      <span >{{ row.tagName }}</span>
                      <template v-if="dealStatus != '03'">
                        <a-icon @click="delReason(row)" class="close-icon" type="close" />
                      </template>
                    </div>
                  </div>
                </div>
                <span>{{ row.alarmReasonName }}</span>
              </template>
              <span v-else-if="item.field == 'powerLoss'">{{ row.powerLoss === 0 ? 0 : row.powerLoss || '--' }}</span>
              <span v-else>{{ row[item.field] || '--' }}</span>
            </template>
          </vxe-table-column>
          <!-- <vxe-table-column title="处理状态" field="alarmStatusName" min-width="110" fixed="right">
            <template v-slot="{ row }">
              <div class="align-center">
                <div class="status-div" :class="getStatusClass(row.alarmStatus)"></div>
                <span :title="row.alarmStatusName || '--'">{{ row.alarmStatusName || '--' }}</span>
              </div>
            </template>
          </vxe-table-column> -->
          <vxe-table-column fixed="right" title="操作" min-width="160">
            <template v-slot="{ row }">
              <span title="详情" v-if="row.id" v-has="'910108101101'">
                <svg-icon @click="doAnalysis(row)" iconClass="zw-detail" class="operation-icon"></svg-icon>
              </span>
              <span v-if="(type == '1' || type == '2') && row.alarmStatus == '01' " title="挂牌" v-has="'alarmCenter:hangtag'">
                <svg-icon v-show="row.alarmStatus == '01'" class="operation-icon" :class="{disabled: !isDisplayBtn(row)}" style="font-size: 16px;"
                          @click="showSelecteTag(row)" iconClass="add-tag"></svg-icon>
              </span>
                <span title="删除" v-has="'910108101106'">
                <svg-icon v-show="row.alarmStatus == '01'" class="operation-icon" @click="doDelete(row, '1')"
                          iconClass="health-delete"></svg-icon>
              </span>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <div class="no-data"></div>
          </template>
        </vxe-table>
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <order-detail ref="orderForm" @close="drawerClose"/>
    <alarm-analysis ref="alarmAnalysis" @alarmAnalysisClose="alarmAnalysisClose" :type="type"
                    :deviceType="deviceType" />
    <tag-modal ref="tagMadal" :selectedRows="selectedRows" :selectedRowData="selectedRowData" :isBatchTag="isBatchTag"
      @addTag="addTag" :isDisableBtn="isDisableBtn"/>
    <DrawerView ref="newAnalysis"/>
  </div>
</template>
<script>
import AlarmAnalysis from './modules/AlarmAnalysis';
import NewTabList from './modules/NewTabList.vue';
import SearchModule from './modules/SearchModule.vue';
import TagModal from './modules/TagModal.vue';
import OrderDetail from './modules/OrderDetail';
import { PSA_INFO, USER_INFO } from '@/store/mutation-types';
import Vue from 'vue';
import {mapGetters} from "vuex";
import { LeftMixin } from '@/mixins/LeftMixin';
import { AlarmEventMixins } from '../mixins/alarmEventMixins';
import {
  getAlarmEvents,
  exportAlarmEvents,
  tagging,
  taggingList,
  getRemarkDynamic,
  getDetailForSensor,
  getAlarm,
  diagnosisStatistics
} from '@/api/health/AlarmEvents.js';
export default {
  name: "DeviceModule",
  mixins: [LeftMixin, AlarmEventMixins],
  components: {
    AlarmAnalysis,
    NewTabList,
    TagModal,
    SearchModule,
    OrderDetail
  },
  data() {
    return {
      loading: false,
      deviceType: '',
      type: '',
      deviceTabList: [
        { remark: "全部", keyValue:'', optList: ['1','2','3', '4'], total: 0},
        { remark: "电站", keyValue:'11', optList: ['1','2'], total: 0},
        { remark: "升压站", keyValue:'66', optList: ['1','3'], total: 0},
        { remark: "集电线", keyValue:'3', optList: ['1','2','3'], total: 0},
        { remark: "箱变", keyValue:'17', optList: ['1','2','3'], total: 0},
        { remark: "逆变器", keyValue:'1', optList: ['1','2','4','3'], total: 0},
        // { remark: "汇流箱 ", keyValue:'4', optList: ['1','2','3'], total: 0},
        { remark: "组串", keyValue:'10', optList: ['1','4'], total: 0},
        // { remark: "组件", keyValue:'58', optList: ['1','4'], total: 0}
      ],
      diaList: [ // 设备类型列表
        { imgSrc: require('@/assets/images/health/alarmEvents/boot.png'), remark: "故障停机", keyValue: '1' },
        { imgSrc: require('@/assets/images/health/alarmEvents/circut.png'), remark: "通讯中断", keyValue: '2' },
        { imgSrc: require('@/assets/images/health/alarmEvents/boxChange.png'), remark: "隐患运行", keyValue: '3' },
        { imgSrc: require('@/assets/images/health/alarmEvents/inverter.png'), remark: "低效缺陷", keyValue: '4' },
      ],
      activeDiaList:[],
      typeTotal: {
        1: 0,
        2: 0,
        3: 0,
        4: 0
      },
      tableHeight: 300,
      psaInfo: Vue.ls.get(PSA_INFO),
      isShowBatchBtn: true, // 批量按钮
      faultStatus: undefined,
      dealStatus: '01',
      selectedRows: [], // 选中列表
      selectedRowData: [], // 打标签 选中的行数据
      isBatchTag: false, // 添加标签时候区分是否是批量  true 批量  false 单个
      // 添加标签弹窗
      columns: [
        // { title: '诊断等级', field: 'alarmGrade', width: 110, hasSort: true},
        { title: '诊断原因', field: 'alarmReasonName', width: 160, hasSort: false},
        { title: '风险提示', field: 'riskWarning', width: 120, hasSort: false},
        { title: '设备名称', field: 'deviceNameShow', width: 120, hasSort: true},
        { title: '设备类型', field: 'deviceTypeName', width: 100, hasSort: false},
        { title: '电站名称', field: 'psName', width: 200, hasSort: true},
        { title: '发生时间', field: 'happenTime', width: 120, hasSort: true},
        { title: ' 更新时间 ', field: 'lastHappenTime', width: 140, hasSort: true},
        { title: '次数', field: 'updateTimes', width: 120, hasSort: true},
        { title: '诊断状态', field: 'faultStatusName', width: 100, hasSort: true},
        { title: '电量损失值(kWh)', field: 'powerLoss', width: 140, hasSort: true},
        // { title: '派发时间', field: 'distributeTime', width: 110, hasSort: true, status: ['', '03', '04']},
        // { title: '闭环时间', field: 'finishedTime', width: 110, hasSort: true, status: ['', '04']},
        // { title: '关联工单', field: 'workCode', width: 120, hasSort: false, status: ['', '03', '04']},
        { title: '处理建议', field: 'opinion', width: 110, hasSort: false},
      ], // 表格列
      isUp: true,
      treeObj: null,
      data: [],
      isShowTree: true,
      isDisableBtn: false, // 行打标签时判断是否是组串未接 组串未接只限制在组串类型
      timeInterval: null,
      clickedIds: [],
      lastClickId: null,
      userInfo: Vue.ls.get(USER_INFO),
      scrollHeight: 0,
      remarkList: [],
      comeFromAlarm: false,
      searchParams: {
        startTime: null,
        endTime: null,
        alarmReason: '',
        alarmStatus: '',
      },
      searchData: {},
      alarmRemarkList: undefined,
      alarmReasonName: ''
    } 
  },
  created () {
    this.dealRouterEvent();
    this.timeInterval = setInterval(
        this.getList,
        (this.configDataFrequency || 5) * 60 * 1000
    );
  },
  mounted () {
    // 设置表格高度
    this.getTableHeight();
    window.onresize = () => {
      this.getTableHeight();
    };
  },
  deactivated () {
    this.cleanIcon();
    this.clearUp();
  },
  beforeDestroy () {
    this.$nextTick(() => {
      this.cleanIcon();
    });
    this.clearUp();
  },
  watch: {
    type (val) {
      this.isShowTree = val == '3';
    }
  },
  computed: {
    isIncludeStorage () {
      return this.selectedRows.filter(item => {
        return item.psCategory && item.psCategory.includes('Storage');
      }).length != 0;
    },
    ...mapGetters(['configDataFrequency'])
  },
  methods: {
    // 设置表格高度
    getTableHeight () {
      this.$nextTick(() => {
         const h = $(window).height();
        const heightScale = h / (1080-64);
        this.tableHeight = document.body.offsetHeight - document.getElementsByClassName('search-content')[0].offsetHeight - 300 - 128 * heightScale;
      });
    },
    // 处理告警列表、诊断概览跳转过来的情况
    async dealRouterEvent () {
      this.activeDiaList = this.diaList.filter(item=>{
        return ['1', '2', '3' , '4'].includes(item.keyValue)
      });
      this.loading = true;
      let res = await getRemarkDynamic();
      this.remarkList = res.result_data;
      if (window.comeFromAlarm) {
        window.comeFromAlarm = false;
        this.comeFromAlarm = true;
        let params = this.$route.params;
        this.setRouteData(params);
        this.deviceType = params.deviceType || '';
        requestAnimationFrame(() => { this.pageChange(1, true); });
        this.$nextTick(() => {
          let searchModule = this.$refs.searchModule;
          searchModule.searchData.alarmStatus = (params.handleStatus?.split(",") ?? []);
          getAlarm({ id: params.id }).then(res => {
            this.loading = false;
            if (res.result_data) {
              this.doAnalysis(res.result_data);
            } else {
              this.$message.warning('获取故障详情失败！');
              this.pageChange(1, true);
              this.comeFromAlarm = false;
            }
          }).catch(() => { this.loading = false;})
        })
      } else if (window.comeFromOverview) {
        let params = this.$route.params;
        this.setRouteData(params);
        this.alarmReasonName = params.reasonName;
        this.$nextTick(() => {
          let searchModule = this.$refs.searchModule;
          searchModule.searchData.alarmStatus = (params.handleStatus?.split(",") ?? []);
        })
        this.searchParams.alarmReason = params.alarmReason;
        this.alarmRemarkList = params.alarmRemark;
        this.deviceType = params.deviceType || '';
        this.pageChange(1, true);
      } else {
        this.$nextTick(() => {
          this.$refs.tabList.activeKey = '';
        });
        this.pageChange(1, true);
      }
      this.getAlarmReasionOptions(this.alarmReasonName); 
    },
    // 处理路由跳转数据，设置查询条件
    setRouteData (params) {
      this.searchParams.alarmStatus = params.handleStatus;
      if(params.isFromRealtime && window.comeFromOverview) this.faultStatus = '1';
      else this.faultStatus = undefined;
      let index = this.diaList.findIndex(item => { return item.remark == params.tabName; })
      this.type = this.diaList[index].keyValue;
      if (this.$refs.alarmAnalysis) {
        this.$refs.alarmAnalysis.alarmAnalysisVisable = false;
      }
      if (this.$refs.newAnalysis) {
        this.$refs.newAnalysis.visible = false;
      }
      if (this.$refs.orderForm) {
        this.$refs.orderForm.visible = false;
      }
      this.$nextTick(() => {
        this.$refs.searchModule.searchData.alarmStatus = params.handleStatus;
        let typeIndex = this.deviceTabList.findIndex(item => { return item.keyValue == params.deviceType; })
        let optList = typeIndex == -1 ?['1', '2', '3', '4']: this.deviceTabList[typeIndex].optList;
        if(!optList.includes(this.type)) {
          this.type = optList[0];
        }
        this.activeDiaList = this.diaList.filter(item=>{
          return optList.includes(item.keyValue)
        });
        let key = this.activeDiaList.findIndex(item => { return item.keyValue == this.type; });
        this.$refs.tabList.activeKey = key == -1 ? '': key;
      });
    },
    // 获取诊断原因下拉选项
    getAlarmReasionOptions (alarmReasonName) {
      this.$nextTick(() => {
        this.$refs.searchModule.getAlarmReasionOptions(alarmReasonName);
      })
    },
    // 设备类型变化事件
    async deviceTypeChange (val) { 
      let data = this.deviceTabList.find(item => { return item.keyValue == val; });
      this.activeDiaList = this.diaList.filter(item=>{
        return data.optList.includes(item.keyValue)
      });
      this.typeChange('');
      this.$nextTick(() => {
        this.$refs.tabList.activeKey = '';
      })
    },
    // 诊断类型变化事件
    typeChange (val) {
      this.type = val;
      this.sortFiled = '';
      this.sortKind = '';
      this.getAlarmReasionOptions(this.alarmReasonName);
      if(this.alarmReasonName) {
        this.loading = true;
      } else {
        this.pageChange(1, true);
      }
  
    },
    // 只看持续勾选事件
    faultStatusChange (e) {
      this.$refs.faultStatus.checked = e.target.checked;
      this.faultStatus = e.target.checked ? '1' : undefined;
      this.pageChange(1, true);
    },
    handleImportMenuClick (e) {
      const { key } = e; // 1 添加标签  2  删除标签
      if (key === '1') {
        this.isBatchTag = true;
        this.$refs.tagMadal.init('');
      } else if (key === '2') {
        let params = {
          tag: '0',
          list: []
        };
        this.selectedRows.forEach(item => {
          params.list.push({
            id: item.id,
            psKey: item.psKey,
            alarmReason: item.alarmReason
          });
        });
        this.$confirm({
          title: '提示',
          content: '是否确认批量删除？',
          okText: '确定',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            taggingList(params).then(() => {
              this.$message.success(`操作成功`);
              this.isShowBatchBtn = true;
              this.getList(false);
            })
          }
        });
      }
    },
    addTag() {
      if(this.isBatchTag) {
        this.isShowBatchBtn = true;
      }
      this.getList(false);
    },
     // 自定义表格排序事件
    mySortChange (sortKind, sortFiled, downSort, upSort) {
      this.sortKind = sortKind;
      this.sortFiled = sortFiled;
      this.downSort = downSort;
      this.upSort = upSort;
      this.page = 1;
      this.getList(true);
    },
    // 导出告警列表
    doExport () {
      this.fomartSearchData();
      this.loading = true;
      exportAlarmEvents(this.searchData).then(res => {
        this.$downloadFile({ fileBase64Code: res.result_data.strBase64, fileName: res.result_data.fileName });
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 待派发-点击时序分析后表格行置灰
    rowClassName ({ row }) {
      let { clickedIds, dealStatus, lastClickId } = this;
      if(lastClickId && lastClickId == row.id) {
        return 'health-row-last-clicked';
      } else if (clickedIds.includes(row.id) && ['01', '03', '04'].includes(dealStatus)) {
        return 'health-row-clicked';
      }
    },
    onScroll () {
      this.scrollHeight = this.$refs.multipleTable.$el.querySelector('.vxe-table--body-wrapper').scrollTop;
    },
     // 行选中事件
    handleSelectionChange ({ records }) {
      this.selectedRows = records;
      this.isShowBatchBtn = this.selectedRows.length === 0;
    },
    getGradePng (grade) {
      if (grade == '4') {
        grade = '3';
      }
      return require('@/assets/images/health/alarmEvents/grade' + grade + '.png');
    },
    // 显示选择标签
    showSelecteTag (row) {
      this.selectedRowData = [];
      this.selectedRowData.push(row);
      // 判断组串未接
      this.isDisableBtn = !(row && row.isString && row.isString === '1');
      let tagReason = row.tag ? row.tag.toString(): '';
      this.isBatchTag = false;
      this.$refs.tagMadal.init(tagReason);
    },
    clearUp () {
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
        this.timeInterval = null;
      }
      window.removeEventListener('resize', this.getTableHeight);
      window.onresize = null;
    },
    cleanIcon() {
      let self = document; let healthClean = self.getElementsByClassName('health-clean-icon');
      if (healthClean && healthClean.length > 0) {
        self.body.removeChild(healthClean[0]);
      }
    },
    // 展开树
    expandTree (row, index) {
      if (row.isExpand) {
        this.$refs.multipleTable.setTreeExpand(this.data[index], false);
        this.$set(row, 'isExpand', false);
      } else {
        this.$refs.multipleTable.setTreeExpand(this.data[index], true);
        this.$set(row, 'isExpand', true);
      }
    },
    isDisplayBtn (row) {
      return row.psCategory ? !row.psCategory.includes('Storage') : true;
    },
     // 格式化查询、导出条件数据
    fomartSearchData () {
      let obj = {
        sortFiled: this.sortFiled,
        sortKind: this.sortKind,
        size: this.size,
        curPage: this.page,
        alarmRemark: this.remarkFilterByTab(this.type),
        deviceType: this.deviceType,
        elecEnsureType: '0',
        isClosed: this.searchParams.alarmStatus == '04' ? '1' : '0',
        closedType: '',
        depCodes: this.psaInfo.departCode,
        faultStatus: this.faultStatus,
        tabCode: this.type,
        treePsId: this.psaInfo.psId,
        treePsaId: this.psaInfo.psaList[0],
        ...this.searchParams
      };
      this.searchData = obj;
    },
    remarkFilterByTab (type) {
      let arr = [];
      this.remarkList.forEach(item => {
        if (item.grade == type) {
          arr.push(item.remark);
        }
      });
      return arr.join(',');
    },
    // 获取告警列表
    getList (refreshScroll) {
      this.loading = true;
      this.treeObj = null;
      this.fomartSearchData();
      this.getTabTotal(this.searchData);
      if (this.$refs.multipleTable && refreshScroll) {
        this.$refs.multipleTable.clearScroll();
      }
      this.dealStatus = this.searchParams.alarmStatus;
      getAlarmEvents(this.searchData).then(res => {
        if (res.result_code == '1') {
          if (res.result_data.rowCount == 0 && this.page != 1) {
            this.pageChange(1, true);
          } else {
            this.data = res.result_data.pageList;
            // 合并项加表示符 低效&&设备类型为组串时，列表有展开项
            if (this.data && this.data.length && this.type == '4' && this.deviceType == 10) {
              this.treeObj = { children: 'displayList' };
              this.data.forEach(item => {
                this.$set(item, 'isExpand', false);
                item.isShow = false;
                if (item.displayList && item.displayList.length) {
                  item.displayList.forEach((list, index) => {
                    this.$set(list, 'mergeRow', true);
                    this.$set(list, 'index', index + 1);
                  });
                }
              });
            }
            this.total = res.result_data.rowCount;
            this.$refs.multipleTable.loadData(this.data);
            this.$refs.multipleTable.refreshColumn();
          }
        }
        this.loading = false;
        window.comeFromOverview = false;
      }).catch(() => {
        this.loading = false;
        window.comeFromOverview = false;
      });
    },
    // 获取设备类型统计数据
    getTabTotal () {
      let params = { 
        treePsId: this.psaInfo.psId, 
        dataRoles: this.userInfo.dataRoles.toString(),
        deviceTypeList: this.deviceType,
        faultStatus: this.faultStatus,
        ...this.searchParams
      };
      params.alarmReasonList = params.alarmReason;
      delete params.alarmReason;
      if(window.comeFromOverview) {
        params.alarmRemarkList = this.alarmRemarkList;
      } 
      diagnosisStatistics(params).then(res => {
        const { devcieTypeCount, alarmCount } = res;
        this.deviceTabList.forEach(item => {
          item.total = devcieTypeCount[item.keyValue] || 0;
        });
        let total = 0;
        for (let key in devcieTypeCount) {
          total += devcieTypeCount[key];
        }
        this.deviceTabList[0].total = total;
        let typeTotal = { 1: 0, 2: 0, 3: 0, 4: 0 };
        if(this.deviceType) {
          let data = alarmCount[this.deviceType];
          typeTotal = {
            1: data && data[1] ? data[1] : 0,
            2: data && data[2] ? data[2] : 0,
            3: data && data[3] ? data[3] : 0,
            4: data && data[4] ? data[4] : 0,
          }
        } else {
          for (let key in alarmCount) {
            for (let k in alarmCount[key]) {
              typeTotal[k] += alarmCount[key][k];
            }
          }
        }
        this.typeTotal = typeTotal;

      })
    },
    // 打开分析页面
    doAnalysis (row) {
      let { clickedIds, dealStatus } = this;
      // 待处理、已派发、已闭环 状态的数据 点击需要置灰当前行
      if (clickedIds.indexOf(row.id) == -1 && ['01', '03', '04'].includes(dealStatus)) {
        this.clickedIds.push(row.id);
      }
      this.lastClickId = row.id;
      if (row.analyseType == 'new') {
        this.$refs.newAnalysis.init(row.alarmRemark, row, '/health/device/modules/NewAnalysis');
      } else {
        this.$refs.alarmAnalysis.init(row, dealStatus);
      }
    },
    // 时序分析页面关闭回调事件
    alarmAnalysisClose (flag) {
      if(this.comeFromAlarm) {
        this.comeFromAlarm = false;
        this.pageChange(1, true);
      } else if (flag) {
        this.getList(false);
      }
    },
    // 删除原因
    delReason (row) {
      this.$confirm({
        title: '提示',
        content: '是否确认取消挂牌？',
        okText: '确定',
        cancelText: '取消',
        centered: true,
        onOk: () => {
          let params = {
            id: row.id,
            tag: '0',
            psKey: row.psKey,
            alarmReason: row.alarmReason
          };
          tagging(params).then(() => {
            this.$message.success(`操作成功`);
            this.tagVisible = false;
            this.getList(false);
          }).catch(() => {});
        }
      });
    },
    getStatusClass(status) {
      switch(status) {
        case '01':
          return 'status1';
        case '03':
          return 'status2';
        case '04':
          return 'status3';
        default:
          return '';
      }
    },
    // 查询条件变化
    searchParamsChange(searchParams) {
      this.searchParams = Object.assign({}, {
        ...searchParams,
        alarmStatus: searchParams.alarmStatus.join(",")
      });
    },
    drawerClose () {
      if(this.comeFromAlarm) {
        this.comeFromAlarm = false;
        this.pageChange(1, true);
      }
    },
    // 诊断原因变化
    reasonNameChange(alarmReasonName) {
      this.alarmReasonName = alarmReasonName;
    }
  }

}
</script>
<style scoped lang="less">
@import "../less/CardCommon.less";
.operation-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
}

.operation {
  margin-bottom: 12px
}
.faultStatusBox {
  color: #fff;
  margin-right: 10px;
}
.tag-name {
  .div-center {
    color: #ff4d4f; 
    border: 1px solid #ff4d4f;
    padding:0 4px;
    border-radius: 3px; 
    margin-right: 3px; 
    font-size: 12px; 
    background: hsla(27.05882353, 100%, 60%, 0.1) !important;
  }
  margin: 4px;
  .close-icon {
    display: none;
    cursor: pointer;
    padding: 4px;
  }
}
.tag-name:hover {
  .close-icon {
    display: inline;
  }
}
.alarm-grade {
  width: 80px;
  img {
    margin-right: 3px;
    width: 12px;
    height: 11px;
  }
}
:deep(.vxe-table--empty-content) {
  width: auto;
}
.expand-index {
  padding-left: 20px;
}
.table-container {
  height: calc(100% - 110px);
}
:deep(.ant-tabs .ant-tabs-bar) {
  border-bottom-color: #376DA2;
}
:deep(.ant-tabs-nav .ant-tabs-tab) {
  width: 92px;
  text-align: center;
  margin-right: 16px;
  padding: 0 0 12px;
}
.status-div {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 6px;
}
.status1 {
  background: #FF8100;
}
.status2 {
  background: #1366EC;
}
.status3 {
  background: #2BA471;
}
</style>