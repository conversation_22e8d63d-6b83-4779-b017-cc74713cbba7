<template>
  <a-row :gutter="24">
    <a-col :xxl="4" :xl="6" :md="8">
      <div class="search-item">
        <span class="search-label">电站名称</span>
        <a-input :value="psaName" disabled />
      </div>
    </a-col>
    <a-col :xxl="4" :xl="6" :md="8">
      <div class="search-item">
        <span class="search-label">发生日期</span>
        <a-range-picker
          v-model="searchParams.dateRange"
          :placeholder="['选择日期', '选择日期']"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </div>
    </a-col>
    <a-col :xxl="4" :xl="4" :md="8" v-if="Object.hasOwn(dictEnum, alarmRemark)">
      <div class="search-item">
        <span class="search-label">诊断原因</span>
        <a-select
          placeholder="请选择"
          class="width-100"
          v-model="searchParams.alarmReason"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option
            v-for="(item, index) in dictEnum[alarmRemark]"
            :key="index"
            :value="item.alarmReason"
            :title="item.alarmReasonName"
          >
            {{ item.alarmReasonName }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="4" :md="8">
      <div class="search-item">
        <span class="search-label">处理状态</span>
        <a-select
          placeholder="请选择"
          class="width-100"
          v-model="searchParams.handleStatus"
          allowClear
        >
          <a-select-option value="01">未确认</a-select-option>
          <a-select-option value="03">已确认</a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :xxl="4" :xl="4" :md="8">
      <div class="search-item">
        <DiThrottleButton label="查询" @click="$emit('query')" />
        <DiThrottleButton
          label="重置"
          class="di-cancel-btn margin-l-12"
          @click="$emit('reset')"
        />
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { PSA_INFO } from "@/store/mutation-types";
import PropTypes from "ant-design-vue/es/_util/vue-types";
import { alarmRemarkAndReasonApi } from "@/api/health/AlarmEvents";
const INPUT_DICT = { 82: [], 86: [], 87: [] };
export default {
  name: "SearchCommon",
  props: {
    searchParams: PropTypes.object.def({
      dateRange: [], //时间区间
      handleStatus: undefined, // 处理状态
      alarmStatus: "01",
      alarmReason: "",
    }),
    alarmRemark: PropTypes.string.def(""),
  },
  data() {
    return {
      dictEnum: INPUT_DICT,
      dataSource: []
    };
  },
  created() {
    alarmRemarkAndReasonApi({ alarmType: "0310" }).then((res) => {
      this.dictEnum = (res ?? []).reduce((acc, cur) => {
        if (Object.hasOwn(acc, cur.alarmRemark)) {
          acc[cur.alarmRemark].push(cur);
        }
        return acc;
      }, this.dictEnum);
    });
  },
  computed: {
    psaName() {
      const psaInfo = this.$ls.get(PSA_INFO);
      return psaInfo.psaNameList && psaInfo.psaNameList[0];
    },
  },
  beforeDestroy() {
    Object.keys(this.dictEnum).forEach((key) => {
      this.dictEnum[key] = [];
    });
  },
};
</script>

<style scoped lang="less"></style>
