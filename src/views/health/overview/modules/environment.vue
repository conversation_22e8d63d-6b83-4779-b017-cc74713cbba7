<template>
  <div class="environment">
    <div class="div-center">
      <img :src="getPng(dataParams.imgName)">
    </div>
    <div class="content-div div-center">
      <div class="content-box div-center" :class="{ 'link-text' : value > 0 }" 
        @click="gotoHealthPage({value: value }, '/health/environment', dataParams)">
        <span class="label">{{ dataParams.label }}</span><span class="num-font-500">{{ value }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { EnvironmentOverviewMixins } from '../../mixins/environmentOverviewMixins';
export default {
  mixins: [EnvironmentOverviewMixins],
  props: {
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
  },
  data () {
    return {
      value: 0
    }
  },
  methods: {
    refreshData (data) {
      data.forEach(item => {
        if (item.typeId == this.dataParams.typeId) {
          this.value = item.value;
        }
      })
    },
    getPng (name) {
      return require(`@/assets/images/health/overview/${name}.png`)
    }
  }
}
</script>
<style lang="less" scoped>
.environment {
  img {
    max-width: 172px;
    width: 100%;
  }
  .content-div {
    padding: 0 8px;
    height: calc(100% - 90px);
    
    .content-box {
      width: 100%;
      height: 50px;
      background: linear-gradient(270deg, rgba(30, 156, 246, 0) 0%, rgba(30, 156, 246, 0.4) 50%, rgba(30, 156, 246, 0) 100%);
      box-sizing: border-box;
      border: 2px solid ;
      border-image: linear-gradient(270deg, rgba(24, 120, 201, 0) 0%, #72BEFF 49%, rgba(24, 120, 201, 0) 100%) 2;
      .label {
        color: #C8E4FF;
      }
      .num-font-500 {
        font-size: 20px;
        margin-left: 8px;
      }
    }
    .link-text:hover {
      cursor: pointer;
      .label, .num-font-500 {
        color: #1890ff;
      }
    }
  }
}
</style>