<template>
  <div class="appearance">
    <div class="div-center margin-t-32">
      <div class="left-label">
        <div class="align-center" v-for="(item, key) in leftItems" :key="key" :class="{'link-text' : item.value > 0 }"
          @click="gotoHealthPage(item, '/health/environment', {tabName: '站容站貌', alarmReason: item.alarmReason})">
          <div class="label">{{ item.label }}</div>
          <div class="num-font-700" :style="{ color: item.color }">{{ item.value }}</div>
        </div>
      </div>
      <div>
        <div class="align-center" v-for="(item, key) in rightItems" :key="key" :class="{'link-text' : item.value > 0 }"
          @click="gotoHealthPage(item, '/health/environment', {tabName: '站容站貌', alarmReason: item.alarmReason})">
          <div class="label">{{ item.label }}</div>
          <div class="num-font-700" :style="{ color: item.color }">{{ item.value }}</div>
        </div>
      </div>

    </div>
    <div class="chart-area margin-t-20" id="appearanceChartArea">
      <div id="appearanceChart" class="width-100 chart height-100"></div>
      <div class="chart-bg" ></div>
    </div>
  </div>
</template>
<script>
import echarts from '@/utils/enquireEchart'
import { EnvironmentOverviewMixins } from '../../mixins/environmentOverviewMixins';
export default {
  mixins: [EnvironmentOverviewMixins],
  props: {
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
  },
  data () {
    return {
      leftItems: [
        { label: '座椅未摆放在指定区域', value: 0, color: '#948EFF', typeId: 2, alarmReason: "1191"},
        { label: '地面上无黄色线条', value: 0, color: '#45D2E2', typeId: 1, alarmReason: "1189"  }
      ],
      rightItems: [
        { label: '有杂物、垃圾', value: 0, color: '#FF7F6B', typeId: 5 , alarmReason: "1190"},
        { label: '上班期间人员数量不够', value: 0, color: '#FFD168', typeId: 3, alarmReason: "1188" }
      ],
      myChart: null
    }
  },
   mounted() {
    // 初始化高度监听
    window.addEventListener('resize',this.windowResize)
    this.windowResize();
  },
  methods: {
    windowResize () {
      this.$nextTick(() => {
        let height = document.documentElement.clientHeight
        let heightScale = (height - 64 * this.$util.getZoom()) / (1080 - 64 * this.$util.getZoom())
        $("#appearanceChartArea").css({
            transform: `scale(${heightScale},${heightScale})`,
            "-ms-transform": `scale(${heightScale},${heightScale})`,
            "-webkit-transform": `scale(${heightScale},${heightScale})`,
            "marginTop": 132 * (heightScale - 1) + 'px'
          });
      })
    },
    refreshData (data) {
      let total = 0;
      let obj = {};
      data.forEach(item => {
        if([1,2,3,5].includes(item.typeId)) {
          obj[item.typeId] = item.value;
          total += item.value;
        }
      });
      this.leftItems.forEach(item => {
        item.value = obj[item.typeId];
      })
      this.rightItems.forEach(item => {
        item.value = obj[item.typeId];
      })
      let option = {
        tooltip: {
          trigger: 'item',
          padding:8,
          borderRadius: 4, // 设置边框圆角半径
          backgroundColor: '#2665AA',
          borderColor: 'transparent',
          formatter: params => {
            return `<span style="color: #D9D9FF;" class="margin-r-8">${params.seriesName}</span>
                    <span style="color: #fff;">${ params.data.value }</span>`;
          },
        },
        series: this.getSeries(total, obj),
        title: [
          {
            text: total,
            x: 'center',
            bottom: 16,
            textStyle: {
              color: '#FFFFFF',
              fontSize: 20,
              fontWeight: '500',
            },
          },
          {
            text: '总计',
            x: 'center',
            bottom: 0,
            textStyle: {
              color: '#FFFFFF',
              fontSize: 12,
              fontWeight: '500',
            },
          },
        ]
      };
      if (!this.myChart) {
        this.myChart = echarts.init(document.getElementById('appearanceChart'));
      }
      option && this.myChart.setOption(option);
    },
    getSeries (total, obj) {
      let series = [];
      if (total > 0) {
        series = [
          {
            name: '座椅未摆放在指定区域',
            type: 'pie',
            radius: [68, 94],
            center: ['50%', '100%'],
            startAngle: 180,
            endAngle: 180 - obj[2]/total*180,
            data: [{ value: obj[2], itemStyle: { color: '#948EFF' } }],
            labelLine: {
                show: false // 隐藏连接线
            }
          },
          {
            name: '地面上无黄色线条',
            type: 'pie',
            radius: [68, 90],
            center: ['50%', '100%'],
            // adjust the start and end angle
            startAngle: 180 - obj[2]/total*180,
            endAngle: 180 - (obj[2] + obj[1]) / total * 180,
            data: [{ value: obj[1], itemStyle: { color: '#45D2E2' } }],
            labelLine: {
                show: false // 隐藏连接线
            }
          },
          {
            name: '有杂物、垃圾',
            type: 'pie',
            radius: [68, 86],
            center: ['50%', '100%'],
            // adjust the start and end angle
            startAngle: 180 - (obj[2] + obj[1]) / total * 180,
            endAngle: 180 - (obj[2] + obj[1] + obj[5]) / total * 180,
            data: [{ value: obj[5], itemStyle: { color: '#FF7F6B' } }],
            labelLine: {
                show: false // 隐藏连接线
            }
          },
          {
            name: '上班期间人员数量不够',
            type: 'pie',
            radius: [68, 82],
            center: ['50%', '100%'],
            // adjust the start and end angle
            startAngle: 180 - (obj[2] + obj[1] + obj[5]) / total * 180,
            endAngle: 0,
            data: [{ value: obj[3], itemStyle: { color: '#FFD168' } }],
            labelLine: {
                show: false // 隐藏连接线
            }
          }
        ]
      } else {
        series = {
          name: '',
          type: 'pie',
          radius: [68, 84],
          center: ['50%', '100%'],
          startAngle: 180,
          endAngle: 0,
          data: [{ value: 1, itemStyle: { color: '#0069C8' } }],
          labelLine: {
              show: false // 隐藏连接线
          },
          tooltip: {
            show: false
          }
        }
        
      }
      return series;
    }
  }
}
</script>
<style lang="less" scoped>
.appearance {
  .left-label {
    margin-right: 40px;
  }
  .label {
    width: 122px;
    color: #BDE2FF;
    font-size: 12px;
    margin-right: 8px;
  }
  .num-font-700{
    font-size: 20px;
    // width: 36px;
    text-align: right;
  }
  .link-text:hover {
    cursor: pointer;
    .label, .num-font-700 {
      color: #1890ff !important;
    }
  }
  .chart-area{
      width: 240px;
      height: 120px;
      position: relative;
      margin: 12px auto 0;
      .chart {
        z-index: 1;
        position: relative;
      }
      .chart-bg {
        width: 240px;
        height: 63px;
        background: url("../../../../assets/images/health/overview/apperance-chart-bg.png");
        background-size: 240px 63px;
        position: absolute;
        bottom: 0;
        left: -5px;
      }
  }

}
</style>