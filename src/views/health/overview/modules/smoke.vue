<template>
  <div class="smoke div-center">
    <div class="width-100">
      <div class="div-center margin-b-32">
        <div class="content-div temperature margin-r-12" :class="{'link-text' : data[6] && data[6] > 0 }"
          @click="gotoHealthPage({ value: data[6] || 0 }, '/health/environment', {tabName: '烟火识别', alarmReason: '1178'})">
          <div class="label">温度异常</div>
          <div class="num-font-500">{{ data[6] || 0 }}</div>
        </div>
        <div class="content-div fog" :class="{'link-text' : data[7] && data[7] > 0 }"
          @click="gotoHealthPage({ value: data[7] || 0 }, '/health/environment', {tabName: '烟火识别', alarmReason: '1179'})">
          <div class="label">烟雾异常</div>
          <div class="num-font-500">{{ data[7] || 0 }}</div>
        </div>
      </div>
      <div class="div-center">
        <div class="content-div fire margin-r-12" :class="{'link-text' : data[8] && data[8] > 0 }"
          @click="gotoHealthPage({ value: data[8] || 0 }, '/health/environment', {tabName: '烟火识别', alarmReason: '1180'})">
          <div class="label">火点异常</div>
          <div class="num-font-500">{{ data[8] || 0 }}</div>
        </div>
        <div class="content-div smoke" :class="{'link-text' : data[9] && data[9] > 0 }"
          @click="gotoHealthPage({ value: data[9] || 0 }, '/health/environment', {tabName: '烟火识别', alarmReason: '1181'})">
          <div class="label">烟火异常</div>
          <div class="num-font-500">{{ data[9] || 0 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { EnvironmentOverviewMixins } from '../../mixins/environmentOverviewMixins';
export default {
  mixins: [EnvironmentOverviewMixins],
  props: {
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
  },
  data () {
    return {
      data: {}
    }
  },
  methods: {
    refreshData (data) {
      this.data = {};
      data.forEach(item => {
        this.data[item.typeId] = item.value;
      });
    },
    getPng (name) {
      return require(`@/assets/images/health/overview/${name}.png`)
    }
  }
}
</script>
<style lang="less" scoped>
.smoke {
  height: 100%;
  .content-div {
    height: 51px;
    width: calc((100% - 12px)/2);
    max-width: 183px;
    background-size: 183px 51px;
    padding: 3px 0 3px 68px;
    line-height: 22px;
    .label {
      color: #C8E4FF;
    }
    .num-font-500 {
      font-size: 20px;
    }
  }
  .link-text:hover {
    cursor: pointer;
    .label, .num-font-500 {
      color: #1890ff;
    }
  }
  .temperature {
    background-image: url("../../../../assets/images/health/overview/temperature-alarm.png");
  }
  .fog {
    background-image: url("../../../../assets/images/health/overview/fog-alarm.png");
  }
  .fire {
    background-image: url("../../../../assets/images/health/overview/fire-alarm.png");
  }
  .smoke {
    background-image: url("../../../../assets/images/health/overview/smoke-alarm.png");
  }
}
</style>