<template>
  <a-row class="hidden-div align-center">
    <a-col :xxl="6" :xl="8" :md="10" class="left-chart">
      <div class="chart-area" id="hiddenChartArea">
        <div id="hiddenChart" class="width-100 chart height-100"></div>
        <div class="chart-bg"></div>
      </div>
      <div class="type-div">
        <div class="type-items margin-b-8">
          <div class="align-center type-box" v-for="item in firstItems" :key="item.value" @click="setDeviceType(item.value)"
            :class="{'active-box': deviceType == item.value}">
            <div class="bg-box" :style="{ background: item.color }"></div>
            <span>{{ item.label }}</span>
          </div>
        </div>
        <div class="type-items">
          <div class="align-center type-box" v-for="item in secondItems" :key="item.value" @click="setDeviceType(item.value)"
            :class="{'active-box': deviceType == item.value}">
            <div class="bg-box" :style="{ background: item.color }"></div>
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :xxl="18" :xl="16" :md="14" class="right-content">
      <div class="data-item" v-for="(item, key) in dataList" :key="key"
        :style="{ background: Math.floor(key/5)%2 == 1 && key%5 != 0 ? 'rgba(19, 60, 118, 0.6)' : 'transparent'}">
        <div class="item-div" :class="{ 'link-text' : item.value > 0 }">
          <div class="label over-flow" :title="item.reasonName" @click="gotoHealthPage(item, '隐患运行')"
            :style="{ background: key/5%2 == 1 ? 'rgba(19, 60, 118, 0.6)' : 'transparent'}">
            {{ item.reasonName }}
          </div>
          <div class="value num-font-500"  @click="gotoHealthPage(item, '隐患运行')"
            :style="{ background: key/5%2 == 1 ? 'rgba(19, 60, 118, 0.6)' : 'transparent'}">
            {{ item.value }}
          </div>
        </div>
      </div>
    </a-col>
  </a-row>
</template>
<script>
import echarts from '@/utils/enquireEchart'
import { getModelData } from "@/api/2dMap/psOverview";
import { DeviceOverviewMixins } from '../../mixins/deviceOverviewMixins';
export default {
  mixins: [DeviceOverviewMixins],
  props: {
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
  },
  data () {
    return {
      firstItems: [
        { label: '升压站', color: '#49C384', value: '1'},
        { label: '集电线', color: '#2B8EF3', value: '2'},
        { label: '箱变', color: '#BFBEF3', value: '3'},
       
      ],
      secondItems: [
        { label: '逆变器', color: '#FF7F6B', value: '4'},
        { label: '汇流箱', color: '#FFA364', value: '5'}
      ],
      deviceType: '0',
      dataList: [],
      sourceData: [
          { name: '升压站', value: 0, prop: 'syzNum', itemStyle: { color: '#49C384' } },
          { name: '集电线', value: 0, prop: 'jdxNum', itemStyle: { color: '#2B8EF3' } },
          { name: '箱变', value: 0, prop: 'unitNum', itemStyle: { color: '#BFBEF3' } },
          { name: '逆变器', value: 0, prop: 'inverterNum', itemStyle: { color: '#FF7F6B' } },
          { name: '汇流箱', value: 0, prop: 'combinerBoxNum', itemStyle: { color: '#FFA364' } }
      ],
      myChart:null
    }
  },
  mounted() {
    // 初始化高度监听
    window.addEventListener('resize',this.windowResize)
    this.windowResize();
  },
  methods: {
    windowResize () {
      this.$nextTick(() => {
        let height = document.documentElement.clientHeight
        let heightScale = (height - 64 * this.$util.getZoom()) / (1080 - 64 * this.$util.getZoom())
        $("#hiddenChartArea").css({
            transform: `scale(${heightScale},${heightScale})`,
            "-ms-transform": `scale(${heightScale},${heightScale})`,
            "-webkit-transform": `scale(${heightScale},${heightScale})`,
            "top": 150 *  (heightScale - 1) + 24 + 'px'
          });
      })
    },
    refreshData () {
      getModelData({...this.dataParams, modelName: 'hiddenDangerDeviceProp'}).then(res => {
        let data = res.result_data.dataResult;
        this.sourceData.forEach(item => {
          item.value = data[item.prop];
        })
        this.renderChart()
      })
      this.getDataList();
    },
    renderChart () {
      let total = this.sourceData.reduce((num, item) => {
          num += item.value
          return num
      }, 0)
      let hasData = total > 0;
      this.sourceData.forEach(item => {
        item.percent = (item.value / total * 100).toFixed(1);
        item.value = item.value > 0 ? (item.value > total * 0.01 ? item.value : total * 0.01) : 0
      })
      let data = this.sourceData.filter(item => {
        return item.value > 0
      })
      let emptyData = [ {name: '', value: 1, itemStyle: { color: '#0069C8' }} ]
      let option = {
        tooltip: {
          padding: 8,
          borderRadius: 4, // 设置边框圆角半径
          backgroundColor: '#2665AA',
          borderColor: 'transparent',
          formatter: params => {
            let temp = this.sourceData.find(e => e.name == params.data.name)
            if(temp) {
              return `<span style="color: #D9D9FF;" class="margin-r-8">${temp.name}</span>
                      <span style="color: #fff;">${ temp.percent }%</span>`;
            }
          }
        },
        series: [
          {
            type: 'pie',
            data: hasData ? data : emptyData,
            padAngle: hasData ? 2 : 0,
            label: {
              show: false,
            },
            labelLine: {
              show: false
            },
            emphasis: {
              scale: true,
              scaleSize: 5
            },
            radius: ['55%', '68%'], 
            tooltip: {
              show: total > 0 
            }
          },
          {
            type: 'pie',
            emphasis: {
                scale: false,
            },
            padAngle: hasData ? 2 : 0,
            radius: ['68%', '90%'],
            itemStyle: {
                opacity: 0.1,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false
            },
            data: hasData ? data : emptyData,
            tooltip: {
              show: false 
            }
          }
        ],
        title: [
          {
            text: total,
            x: 'center',
            top: 'middle',
            textStyle: {
              color: '#FFFFFF',
              fontSize: 24,
              fontWeight: '500',
            },
          }
        ]
      };
      if(this.myChart == null){
        this.myChart = echarts.init(document.getElementById('hiddenChart'));
      }
      //将配置项设置进去
      this.myChart.setOption(option, true);
    },
    getDataList () {
      this.$emit('setLoading', true);
      getModelData({...this.dataParams, modelName: 'hiddenDangerReasonProp', deviceType: this.deviceType}).then(res => {
        this.dataList = res.result_data.dataResult.hiddenDangerReasonProp;
        this.$emit('setLoading', false);
      }).catch(() => { this.$emit('setLoading', false); })
    },
    getTextAngle(currentAngle, angle) {
        currentAngle = currentAngle + angle
        if (currentAngle <= 90) {
            return -currentAngle;
        } else if (currentAngle <= 180 && currentAngle > 90) {
            return 180 - currentAngle;
        } else if (currentAngle < 270 && currentAngle > 180) {
            return 180 - currentAngle;
        } else if (currentAngle < 360 && currentAngle >= 270) {
            return 360 - currentAngle;
        }
    }
  }
}
</script>
<style lang="less" scoped>
.hidden-div {
  .left-chart {
    //width: 310px;
    height: 100%;
    position: relative;
    .chart-area{
      width: 150px;
      height: 150px;
      left: 27%;
      position: absolute;
      top: 24px;
      .chart {
        z-index: 1;
        position: relative;
      }
      .chart-bg {
        width: 150px;
        height: 150px;
        background: url("../../../../assets/images/health/overview/hidden-chart-bg.png");
        background-size: 150px 150px;
        position: absolute;
        top: 0
      }
    }
    .type-div {
      position: absolute;
      bottom: 8px;
      //width: 310px;
      width: 100%;
      .type-items {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .type-box {
        color: #BDE2FF;
        font-size: 12px;
        cursor: pointer;
        background: #1A4E98;
        border: 1px solid #407DD3;
        border-radius: 4px;
        padding: 2px 4px;
        &:hover {
          background: #095FAB;
          border-color: @di-color-text-main;
        }
        .bg-box {
          width: 10px;
          height: 10px;
          margin-right: 4px;
          border-radius: 2px;
        }
      }
      .active-box {
        background: #095FAB;
        border-color: @di-color-text-main;
      }
      .type-box + .type-box {
        margin-left: 16px;
      }
    }
  }
  .right-content {
    //width: calc(100% - 310px);
    height: 100%;
    overflow: auto;
    padding: 8px 16px  0 0;
    .data-item {
      display: inline-block;
      margin-bottom: 4px;
      width: 20%;
      height: 30px;
      .item-div {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      .label {
        width: calc(100% - 44px);
        padding: 0 8px;
        max-width: 102px;
        line-height: 30px;
        color: #C8E4FF;
      }
      .value {
        width: 44px;
        text-align: right;
        line-height: 30px;
        font-size: 16px;
        padding-right: 8px;
      }
      .link-text:hover {
        .label, .value {
          color: #1890ff;
          cursor: pointer;
        }
      }
    }
  }
}
</style>