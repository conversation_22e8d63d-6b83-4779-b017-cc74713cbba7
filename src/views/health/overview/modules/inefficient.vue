<template>
  <div class="inefficient">
    <div class="align-center">
      <div class="head-data div-center">
        <img :src="getPng('interver')" alt="">
        <div>
          <div class="num-font-500">{{ dataResult.inverterNum || 0 }}</div>
          <div>逆变器</div>
        </div>
      </div>
      <div class="head-data div-center">
        <img :src="getPng('string')" alt="">
        <div>
          <div class="num-font-500">{{ dataResult.zcNum || 0 }}</div>
          <div>组串</div>
        </div>
      </div>
      <div class="head-data div-center">
        <img :src="getPng('component')" alt="">
        <div>
          <div class="num-font-500">{{ dataResult.zjNum || 0 }}</div>
          <div>组件</div>
        </div>
      </div>
    </div>
    <div class="content-box">
      <div class="content-div align-center" v-for="item in faultList" :key="item.name" @click="gotoHealthPage(item, '低效缺陷')"
        :class="{ 'link-text' : item.value > 0 }">
        <div class="align-center">
          <svg-icon :icon-class="item.iconClass" class="icon" />
          <div class="label">{{item.name }}</div>
        </div>
        <div class="num-div">
          <div class="num-width" :style="{ width: item.width + 'px' }"></div>
        </div>
        <div class="value">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getModelData } from "@/api/2dMap/psOverview";
import { DeviceOverviewMixins } from '../../mixins/deviceOverviewMixins';
export default {
  mixins: [DeviceOverviewMixins],
  props: {
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
  },
  data () {
    return {
      faultList: [],
      dataResult: {},
      maxValue: 0
    }
  },
  mounted() {
    window.addEventListener('resize',this.setDataWidth)
  },
  methods: {
    refreshData () {
      getModelData({...this.dataParams, modelName: 'inefDefectDeviceProp'}).then(res => {
        this.dataResult = res.result_data.dataResult;
      })
      this.$emit('setLoading', true);
      getModelData({...this.dataParams, modelName: 'inefDefectReasonProp'}).then(res => {
        this.maxValue = 0;
        res.result_data.dataResult.inefDefectReasonProp.forEach(item => {
          if(item.value > this.maxValue) {
            this.maxValue = item.value;
          }
          item.name = item.reasonName;
          let obj = this.getDataByName(item.reasonName);
          item.reasonName = obj.reasonName;
          item.iconClass = obj.iconClass
          item.width = 0;
        })
        this.faultList = res.result_data.dataResult.inefDefectReasonProp;
        this.setDataWidth();
        this.$emit('setLoading', false);
      }).catch(() => { this.$emit('setLoading', false); })
    },
    getDataByName (name) {
      let arr = name.split('-');
      let obj = { reasonName: arr[1] };
      switch (arr[0]) {
        case '逆变器':
          obj.iconClass = 'interver';
          break;
        case '组串':
          obj.iconClass = 'string';
          break;
        case '组件':
          obj.iconClass = 'component';
          break;
      }
      return obj;
    },
    setDataWidth () {
      this.$nextTick(() => {
        let baseWidth = document.getElementsByClassName('num-div')[0]?.offsetWidth;
        this.faultList.forEach(item => {
          item.width = item.value / this.maxValue * baseWidth;
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.inefficient {
  height: 100%;
  .head-data {
    margin: 20px 0;
    width: 33.33%;
    line-height: 20px;
    color: #BDE2FF;
    .num-font-500 {
      font-size: 20px;
      color: #fff;
    }
    img {
      width: 43px;
      margin-right: 8px;
    }
  }
  .content-box {
    height: calc(100% - 84px);
    overflow: auto;
    margin-top: -10px;
  }
  .content-div {
    padding: 0 16px;
    height: calc(100% / 9);
    min-height: 22px;
    line-height: 22px;
  }
  .label {
    width: 140px;
    color: #C8E4FF;
    font-size: 12px;
    margin-right: 8px;
  }
  .icon {
    font-size: 20px;
    margin-right: 8px;
  }
  .value {
    font-size: 12px;
    width: 40px;
    margin-left: 8px;
  }
  .num-div {
    width: calc(100% - 224px);
    background: rgba(255, 255, 255, 0.13);
    height: 12px;
  }
  .num-width {
    background: linear-gradient(90deg, #0084C1 0%, #2CFFF8 100%);
    transition: 0.75s;
    height: 12px;
    width: 1px;
    transition-property: width;
  }
  .link-text:hover {
    cursor: pointer;
    .label, .value {
      color: #1890ff;
    }
  }
  
  
}
</style>