<template>
  <div class="behavioral div-center">
    <div class="align-center">
      <img :src="getPng('behavioral')" />
      <div class="label-div">
        <div v-for="(item, key) in dataList" :key="key" class="label-box align-center"
          @mouseenter="setHoverName(item.name)" @mouseleave="setHoverName('')"
          :class="{'link-text': hoverName == item.name && data[item.prop] && data[item.prop] > 0,'margin-b-24': key != 2}"
          @click="gotoHealthPage({ value: data[item.prop] || 0 }, '/health/behavioral', {tabName: item.name, alarmReason: ''})">
          <svg-icon :icon-class="item.iconClass" class="title-icon" />{{ item.name }}
        </div>
      </div>
      <div class="value-div">
        <div v-for="(item, key) in dataList" :key="key" class="value-box margin-b-24 align-center"
          @mouseenter="setHoverName(item.name)" @mouseleave="setHoverName('')"
          :class="{'link-text': hoverName == item.name && data[item.prop] && data[item.prop] > 0,'margin-b-24': key != 2}"
          @click="gotoHealthPage({ value: data[item.prop] || 0 }, '/health/behavioral', {tabName: item.name, alarmReason: ''})">
          <span class="num-font-500">{{ data[item.prop] || 0 }}</span><span>{{item.unit}}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { EnvironmentOverviewMixins } from '../../mixins/environmentOverviewMixins';
export default {
  mixins: [EnvironmentOverviewMixins],
  props: {
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
  },
  data () {
    return {
      data: {},
      hoverName: '',
      dataList: [
        { name: '日常行为', prop: 10, iconClass: 'daily-behavior', unit: '次', alarmReason: '1194'},
        { name: '作业规范', prop: 11, iconClass: 'job-specifications', unit: '次', alarmReason: '1540'},
        { name: '人员安全', prop: 12, iconClass: 'personnel-safety', unit: '次', alarmReason: '1541'},
      ]
    }
  },
  methods: {
    refreshData (data) {
      this.data = {};
      data.forEach(item => {
        this.data[item.typeId] = item.value;
      });
    },
    setHoverName (name) {
      this.hoverName = name;
    }
  }
}
</script>
<style lang="less" scoped>
.behavioral {
  height: 100%;
  img, .label-div,  .value-div{
    height: 124px;
  }
  .label-div {
    margin: 0 0 0 32px;
    font-size: 16px;
    .title-icon {
      font-size: 24px;
      margin-right: 4px;
    }
    .label-box {
      height: 24px;
      color: #C8E4FF;
      padding-right: 16px;
    }
  }
  .value-box {
    height: 24px;
    color: rgba(255, 255, 255, 0.7);
    justify-content: flex-end;
    span {
      line-height: 20px;
    }
    .num-font-500 {
      font-size: 20px;
      margin-right: 4px;
      color: #FFFFFF;
      line-height: 24px;
    }
  }
  .link-text {
    color: #1890ff !important;
    cursor: pointer;
    .num-font-500 {
      color: #1890ff;
    } 
  }
}
</style>