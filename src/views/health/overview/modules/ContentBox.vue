<template>
  <div class="content-box" :style="{height: height, width: width}">
    <a-spin :spinning="loading" class="width-100 height-100">
      <div class="head-div align-center">
        <div class="head-marker"></div>
        <svg-icon :icon-class="iconClass" class="title-icon" />
        <div>{{ title }}</div>
      </div>
      <component class="content" ref="component"
        :dataParams="dataParams"
        @setLoading="setLoading"
        @callBack="callBack"
        :is="componentName"/>
    </a-spin>
  </div>
</template>
<script>
import Environment from "./environment";
import Behavioral from "./behavioral";
import Smoke from "./smoke";
import Inefficient from "./inefficient";
import Stop from "./stop";
import Hidden from "./hidden";
import Appearance from "./appearance";
export default {
  components: {
    Environment,
    Behavioral,
    Smoke,
    Inefficient,
    Stop,
    Hidden,
    Appearance
  },
  props: {
    // 标题
    title: {
      type: String,
      default: '',
      required: true
    },
    // 标题图标
    iconClass: {
      type: String,
      default: ''
    },
    // 组件名
    componentName: {
      type: String,
      default: ''
    },
    // 页面查询参数
    dataParams:{
      type:Object,
      default:()=>{
        return {
        }
      }
    },
    // 高度
    height: {
      type: Number | String,
      default: 0,
    },
    // 宽度
    width: {
      type: Number | String,
      default: '100%',
    }
  },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    callBack(type,param){
      this.$emit('callBack',type,param)
    },
    setLoading(loading){
      this.loading = loading
    },
    refreshData (data) {
      this.$refs.component.refreshData(data);
    }
  }
}
</script>
<style scoped lang="less">
.content-box {
  // display: inline-block;
  // width: 100%;
  background: linear-gradient(180deg, #1A4A8E 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
  border-radius: 8px;
  box-sizing: border-box;
  border: 1px solid #255DAE;
  color: #fff;
  .head-div {
    background: linear-gradient(90deg, #0661AE 0%, rgba(5, 97, 175, 0) 100%);
    border-radius: 8px 8px 0 0;
    height: 32px;
    .head-marker {
      background: #BDE2FF;
      height: 16px;
      width: 2px;
    }
    .title-icon {
      font-size: 20px;
      margin: 0 6px 0 4px;
    }
  }
  .content {
    height: calc(100% - 32px);
  }
}
</style>