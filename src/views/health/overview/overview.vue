<template>
  <div class="height-100">
    <div v-show="!smartDetectVisible" class="time-tab" id="timeTab">
      <a-radio-group
        v-model="params.type"
        @change="refreshData"
        size="default"
        style="height: 30px"
      >
        <a-radio-button value="1">实时数据</a-radio-button>
        <a-radio-button value="2">历史数据</a-radio-button>
      </a-radio-group>
    </div>
    <div v-show="!smartDetectVisible" class="overview height-100">
      <div class="left-content height-100">
        <content-box
          ref="Stop"
          title="故障停机"
          iconClass="health-stop"
          :height="height1"
          componentName="Stop"
          :dataParams="params"
        />
        <content-box
          ref="Hidden"
          title="隐患运行"
          iconClass="health-hidden"
          :height="height1"
          componentName="Hidden"
          :dataParams="params"
          class="margin-t-16"
        />
        <div class="align-center margin-t-16" :style="{ height: height1 }">
          <content-box
            ref="Appearance"
            title="站容站貌"
            iconClass="health-appearance"
            :width="width1"
            height="100%"
            componentName="Appearance"
            class="margin-r-16"
            :dataParams="params"
          />
          <content-box
            ref="Smoke"
            title="烟火识别"
            iconClass="health-smoke"
            :width="width1"
            height="100%"
            componentName="Smoke"
            class="margin-r-16"
            :dataParams="params"
          />
          <content-box
            ref="Behavioral"
            title="行为分析"
            iconClass="behavior-recognition"
            :width="width1"
            height="100%"
            componentName="Behavioral"
            :dataParams="params"
          />
        </div>
      </div>
      <div class="right-content height-100">
        <content-box
          ref="Inefficient"
          title="低效缺陷"
          iconClass="health-inefficient"
          :height="height2"
          componentName="Inefficient"
          :dataParams="params"
        />
        <div
          class="align-center margin-t-16 width-100"
          :style="{ height: height1 }"
        >
          <content-box
            ref="Intrusion"
            title="周界入侵"
            iconClass="health-perimeter"
            :width="width2"
            height="100%"
            class="margin-r-8"
            componentName="Environment"
            :dataParams="{
              label: '人员入侵',
              imgName: 'personnel-intrusion',
              typeId: 13,
              tabName: '周界入侵',
              type: params.type,
            }"
          />
          <content-box
            ref="Weather"
            title="天气预警"
            iconClass="health-cloud"
            :width="width2"
            height="100%"
            class="margin-r-8"
            componentName="Environment"
            :dataParams="{
              label: '恶劣天气',
              imgName: 'bad-weather',
              typeId: 14,
              tabName: '天气预警',
              type: params.type,
            }"
          />
          <content-box
            ref="Water"
            title="水情识别"
            iconClass="health-water"
            :width="width2"
            height="100%"
            componentName="Environment"
            :dataParams="{
              label: '水位异常',
              imgName: 'water-abnormal',
              typeId: 15,
              tabName: '水情识别',
              type: params.type,
            }"
          />
        </div>
      </div>
    </div>
<!--  之前的智能检测入口，现在不再使用  -->
<!--    <quality-inspection v-if="smartDetectVisible" @goBack="goBack" />-->
<!--    <div v-show="!smartDetectVisible" @click.stop.prevent="openSmartDetectDialog"-->
<!--         class="smart-check-entry" :style="{left,top}">-->
<!--      <div class="bg"></div>-->
<!--      <div class="border"></div>-->
<!--      <div class="shadow"></div>-->
<!--    </div>-->
  </div>
</template>
<script>
import Vue from "vue";
import ContentBox from "./modules/ContentBox";
import { getModelData } from "@/api/2dMap/psOverview";
import { PSA_INFO } from "@/store/mutation-types";
import {mapGetters} from "vuex";
// import QualityInspection from "@/views/health/inspection/QualityInspection";
// import {setElementDraggable} from "@/utils/draggableWindow";
// import { gsap } from 'gsap';

export default {
  name: "overview",
  components: {
    ContentBox,
    // QualityInspection,
  },
  data() {
    return {
      timeType: "1",
      compontentsNames1: ["Stop", "Hidden", "Inefficient"],
      compontentsNames2: [
        "Appearance",
        "Smoke",
        "Behavioral",
        "Intrusion",
        "Weather",
        "Water",
      ],
      timeInterval: null,
      enviromentData: {},
      params: {
        psId: Vue.ls.get(PSA_INFO).psId,
        type: "1",
      },
      height1: "calc((100% - 32px)/3)",
      height2: "calc((100% - 32px)/3*2 + 16px)",
      width1: "calc((100% - 32px)/3)",
      width2: "calc((100% - 16px)/3)",
      smartDetectVisible: false,
      left:"96%",
      top:"80%"
    };
  },
  computed:{
    ...mapGetters(['systemConfigMap'])
  },
  beforeDestroy () {
    this.clearUp();
  },
  mounted() {
    // 初始化高度监听
    window.addEventListener("resize", this.windowResize);
    this.windowResize();
    this.timeInterval = setInterval(
        this.refreshData,
        (this.systemConfigMap?.configDataFrequency?.setValue  || 5) * 60 * 1000
    );
    this.$nextTick(() => {
      this.refreshData();
    });
    // let el = document.querySelector('.smart-check-entry')
    // setElementDraggable(el,'handle',(e)=>{
    //   this.left = e.left
    //   this.top = e.top
    // })
    // this.setSmartCheckAnimation();
  },
  methods: {
    // 初始化高度监听
    windowResize() {
      const h = $(window).height();
      const heightScale = h / (1080 - 64);
      // let height = document.documentElement.clientHeight;
      // let heightScale = (height - 64 * this.$util.getZoom()) / (1080 - 64 * this.$util.getZoom());
      $("#timeTab").css({
        transform: `scale(${heightScale},${heightScale})`,
        "-ms-transform": `scale(${heightScale},${heightScale})`,
        "-webkit-transform": `scale(${heightScale},${heightScale})`,
        top: (32 * (heightScale - 1)) / 2 + 80 * heightScale + "px",
        right: (176 * (heightScale - 1)) / 2 + 32 + "px",
      });
    },
    // 刷新数据
    refreshData() {
      this.compontentsNames1.forEach((item) => {
        this.$refs[item].refreshData();
      });
      this.compontentsNames2.forEach((item) => {
        this.$refs[item].setLoading(true);
      });
      let params = { ...this.params, modelName: "envDetectReasonProp" };
      getModelData(params)
        .then((res) => {
          this.compontentsNames2.forEach((item) => {
            this.$refs[item].refreshData(
              res.result_data.dataResult.envDetectReasonProp
            );
            this.$refs[item].setLoading(false);
          });
        })
        .catch(() => {
          this.compontentsNames2.forEach((item) => {
            this.$refs[item].setLoading(false);
          });
        });
    },
    openSmartDetectDialog() {
      // 获取该元素dragFlag属性
      let el = document.querySelector('.smart-check-entry');
      if(el.getAttribute('dragFlag') === 'true') return;
      this.smartDetectVisible = true;
    },
    goBack() {
      this.smartDetectVisible = false;
    },
    clearUp() {
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
        this.timeInterval = null;
      }
      window.removeEventListener("resize", this.windowResize);
    }
    // setSmartCheckAnimation(){
    //   const shadow = document.querySelector('.smart-check-entry .shadow');
    //   const border = document.querySelector('.smart-check-entry .border');
    //   gsap.fromTo(shadow,{opacity: 0}, { opacity: 1, duration: 3, repeat: -1, yoyo: true });
    //   gsap.fromTo(border,{opacity: 0}, { opacity: 1, duration: 3, repeat: -1, yoyo: true });
    // }
  },
};
</script>
<style scoped lang="less">
.time-tab {
  position: absolute;
  right: 32px;
  top: 80px;
  z-index: 3;
}

.smart-check-entry{
  position: absolute;
  width: 74px;
  height: 80px;

  .bg{
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background-image: url("../../../assets/images/inspection/smart-check-entry.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    z-index: 2;
  }
  .border{
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background-image: url("../../../assets/images/inspection/smart-check-border.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    z-index: 3;
  }
  .shadow{
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    background-image: url("../../../assets/images/inspection/smart-check-shadow.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    z-index: 1;
  }
}

.overview {
  display: flex;
  margin-top: -8px;

  .left-content {
    width: 70.474%;
    margin-right: 0.862%;
  }

  .right-content {
    width: 28.664%;
  }
}
</style>
