import { clone } from "xe-utils";
import echarts from "@/utils/enquireEchart";

export const positionsMapping = [
  "LeftBottom",
  "LeftTop",
  "RightTop",
  "RightBottom",
];

const indicatorMapping = {
  finalScore: "电站检测得分",
  failureIndexScore: "故障指数",
  safetyIndexScore: "安全指数",
  inefficiencyIndexScore: "低效指数",
  assessmentIndexScore: "考核指数",
};

const clonedData = clone(indicatorMapping);
delete clonedData.finalScore;
export const excludeFinalScoreMapping = clonedData;

export const levelMapping = {
  1: {
    level: "优",
    msg: "电站处于健康状态，继续保持",
  },
  2: {
    level: "良",
    msg: "电站处于亚健康状态，注意防范",
  },
  3: {
    level: "差",
    msg: "电站处于异常状态，尽快处理",
  },
};

export const formatProduceNewReportData = (dataResult = {}) => {
  let LeftBottom = {},
    LeftTop = {},
    RightTop = {},
    RightBottom = {};
  const [
    {
      failureIndexScore,
      inefficiencyIndexScore,
      assessmentIndexScore,
      safetyIndexScore,
      finalScore,
    },
  ] = dataResult.znzdScore || [{}];
  LeftTop.finalScore = failureIndexScore ?? 0;
  RightTop.finalScore = inefficiencyIndexScore ?? 0;
  RightBottom.finalScore = assessmentIndexScore ?? 0;
  LeftBottom.finalScore = safetyIndexScore ?? 0;
  // alarmType={1是停机故障，2是通讯故障，3是设备安全，4是设备低效}
  // 故障指数
  LeftTop.alarmList = dataResult.znzdFdsb
    ?.sort((a, b) => a.alarmType - b.alarmType)
    ?.filter((item) => ["1", "2"].includes(item.alarmType));
  // 低效指数
  (RightTop.alarmList ??= []).push(
    ...(dataResult.znzdXtdx ?? []),
    ...(dataResult.deviceInefficiencyFreq ?? [])
  );
  // 考核指数
  (RightBottom.alarmList ??= []).push(
    ...(dataResult.znzdXwglyskh ?? []),
    ...(dataResult.znzdSxzkh ?? [])
  );
  // 安全指数
  const deviceSafe = dataResult.znzdFdsb?.filter((item) => item.alarmType == 3);
  const personSafe = dataResult.znzdHj?.filter((item) => item.alarmType == 5);
  const environmentSafe = dataResult.znzdHj?.filter(
    (item) => item.alarmType == 6
  );
  (LeftBottom.alarmList ??= []).push(
    ...(deviceSafe ?? []),
    ...(personSafe ?? []),
    ...(environmentSafe ?? [])
  );

  return { finalScore, LeftBottom, LeftTop, RightTop, RightBottom };
};

export const renderChart = (data) => {
  const dataResult =
    data?.result_data?.dataResult.historyReportScoreGraph ?? [];
  const xData = dataResult.reduce((acc, cur) => {
    acc.push(cur.detectDate);
    return acc;
  }, [])
  return {
    grid:{
      bottom: "20%"
    },
    dataZoom: [{
      type: 'slider', // slider表示有滑动块的，
      show: true,
      showDetail: false,
      bottom: 27,
      xAxisIndex: xData.map((_, index) => index), // 表示x轴折叠
      start: 0, // 数据窗口范围的起始百分比
      end: 100// 数据窗口范围的结束百分比
    }, {
      type: 'inside',
      xAxisIndex: xData.map((_, index) => index),
    }],
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: Object.values(indicatorMapping),
      textStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    xAxis: {
      type: "category",
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
      },
      data: xData,
    },
    yAxis: {
      type: "value",
      axisLabel: { color: "rgba(255, 255, 255, 0.7)" }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
      splitLine: {
        lineStyle: {
          color: "rgba(97, 161, 213, 0.19)",
        },
      },
    },
    series: [
      {
        name: indicatorMapping.finalScore,
        type: "bar",
        // label: {
        //   show: true,
        //   position: "top",
        //   color: "#fff",
        // },
        data: dataResult.reduce((acc, cur) => {
          acc.push(cur.finalScore);
          return acc;
        }, []),
      },
      ...Object.keys(indicatorMapping)
        .filter((item) => item != "finalScore")
        .map((item) => {
          return {
            name: indicatorMapping[item],
            type: "line",
            data: dataResult.reduce((acc, cur) => {
              acc.push(cur[item]);
              return acc;
            }, []),
          };
        }),
    ],
  };
};
console.log("=>(config.js:160) ", );
// 报告详情雷达图
/**
 * @param {Object} [data]
 */
export const formatRadarOption = (data = {}) => {
  return {
    grid: {
      containLabel: true,
    },
    radar: {
      center: ["50%", "50%"],
      radius: "45%",
      startAngle: 90, // 起始角度
      splitNumber: 2,
      shape: "circle",
      splitArea: {
        areaStyle: {
          color: "transparent",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
        },
      },
      indicator: Object.keys(excludeFinalScoreMapping).map((item) => ({
        name: excludeFinalScoreMapping[item],
        max: 100,
        color: "#fff",
      })),
    },
    series: [
      {
        type: "radar",
        data: [
          {
            value: Object.keys(excludeFinalScoreMapping).map(
              (item) => data[item] ?? 0
            ),
            symbol: "none",
            lineStyle: {
              normal: {
                color: "#4EA7F9",
                width: 2,
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  1,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(78, 167, 249, 0.4)",
                    },
                    {
                      offset: 1,
                      color: "rgba(78, 167, 249, 0.8)",
                    },
                  ],
                  false
                ),
              },
            },
          },
        ],
      },
    ],
  };
};

// 根据分数，返回对应的颜色
/**
 * @param {Number} score
 * @returns {string}
 */
export function selectScoreColor(score) {
  if (score >= 0 && score < 60) {
    return "#aa254c";
  } else if (score >= 60 && score < 80) {
    return "#F0BD00";
  } else {
    return "#1A99FF";
  }
}

/**
 * @param {Number} [score]
 * @returns {Object}
 */
export const formatLiquidOption = (score) => {
  return {
    series: [
      {
        type: "liquidFill",
        data: [(score ?? 0) / 100],
        radius: "70%",
        outline: {
          show: false,
        },
        label: {
          formatter: (score ?? 0) + "分",
          fontSize: 20,
          color:"#fff"
        },
        backgroundStyle: {
          color: "rgba(255,255,255,0.1)", // 水球背景色
        },
        itemStyle: {
          opacity: 0.95, // 波浪颜色透明度
          color: selectScoreColor(score),
        },
      },
    ],
  };
};
export const powerLineParams = {
  timeInterval: 30,
  list: [
    {
      deviceType: "7",
      psName: "肥东金阳100MW光伏电站",
      psId: 107353,
      pointList: [
        {
          unit: "Varh",
          point: "p8073",
          pointName: "第一象限无功电量",
          dataSort: 1,
          deviceType: "7",
          key: "p8073",
          psKeys: "107353_7_10005574_1",
        },
        {
          unit: "Varh",
          point: "p8074",
          pointName: "第二象限无功电量",
          dataSort: 2,
          deviceType: "7",
          key: "p8074",
          psKeys: "107353_7_10005574_1",
        },
        {
          unit: "Varh",
          point: "p8075",
          pointName: "第三象限无功电量",
          dataSort: 3,
          deviceType: "7",
          key: "p8075",
          psKeys: "107353_7_10005574_1",
        },
        {
          unit: "Varh",
          point: "p8076",
          pointName: "第四象限无功电量",
          dataSort: 4,
          deviceType: "7",
          key: "p8076",
          psKeys: "107353_7_10005574_1",
        },
      ],
      psKeys: ["107353_7_10005574_1"],
    },
  ],
};
/**
 * @param {Object} [data]
 * @returns {Object}
 */
export const formatPowerLineOption = (data) => {
  const pointsData = data?.yData?.[0]?.data?.[0] ?? {};
  return {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: pointsData?.pointName ?? [],
      textStyle: {
        color: "rgba(255, 255, 255, 1)",
      },
    },
    xAxis: {
      type: "category",
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
      },
      data: data?.xData ?? [],
    },
    yAxis: {
      type: "value",
      name: pointsData?.unit?.[0],
      nameTextStyle: {
        color: "#fff",
        alias: "left",
      },
      axisLabel: {
        color: "rgba(255, 255, 255, 0.7)",
      }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
      splitLine: {
        lineStyle: {
          color: "rgba(97, 161, 213, 0.19)",
        },
      },
    },
    series: pointsData?.pointName?.map((item, index) => ({
      name: item,
      type: "line",
      data: pointsData?.data[index] ?? [],
    })),
  };
};
