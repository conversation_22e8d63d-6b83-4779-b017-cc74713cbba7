<template>
  <div class="container width-height-100">
    <div class="wrapper width-height-100">
      <div class="top-linear-gradient"></div>
      <div class="bottom-linear-gradient"></div>
      <div class="left"></div>
      <div class="right"></div>
      <a-row type="flex" align="middle" class="left-top-menu">
        <a-col :span="4">
          <div class="search-item">
            <span class="search-label">电站名称</span>
            <a-input :value="psaName" disabled />
          </div>
        </a-col>
        <di-throttle-button
          v-has="'inspection:historyReport'"
          label="历史检测报告"
          class="di-cancel-btn history-report-btn"
          @click="openHistoryReport"
          v-if="!loading && !$isEmpty(reportInfo)"
        />
      </a-row>
      <div
        class="content flex-center width-height-100"
        :style="{
          transform: `scale(${this.indicatorScale}, ${this.indicatorScale})`,
        }"
      >
        <div class="radar flex-center width-height-100">
          <div class="inner-scanning" />
        </div>
        <indicator-card-item
          v-for="item in positions"
          :key="reloadKey + item"
          :indicator-card-animation-state="indicatorCardAnimationState"
          :indicator-card-animation-duration="indicatorCardAnimationDuration"
          :report-info="reportInfo"
          :position="item"
        />
        <final-score
          v-if="!loading && !$isEmpty(reportInfo)"
          :step="reportInfo.finalScore"
          @handleViewReport="openHistoryReport"
        />
        <template v-else-if="loading && $isEmpty(reportInfo)">
          <div class="random-icon-gif"></div>
          <div class="wave-line"></div>
          <div class="water-ripple"></div>
        </template>
        <div
          v-has="'inspection:smartCheck'"
          v-if="!loading && $isEmpty(reportInfo)"
          class="flex-center cursor-pointer"
          @click="startSmartCheck"
          :class="{
            'smart-check': !loading && $isEmpty(reportInfo),
          }"
        >
          智能检测
        </div>
      </div>
      <div
        v-has="'inspection:smartCheck'"
        v-if="!loading && !$isEmpty(reportInfo)"
        class="flex-center cursor-pointer"
        @click="startSmartCheck"
        :class="{
          'finished-smart-check': !loading && !$isEmpty(reportInfo),
        }"
      >
        智能检测
      </div>
      <DrawerView ref="drawerView" />
    </div>
  </div>
</template>

<script>
import IndicatorCardItem from "./modules/IndicatorCardItem";
import FinalScore from "./modules/FinalScore";
import { PSA_INFO } from "@/store/mutation-types";
import { getModelData } from "@/api/2dMap/psOverview";
import { formatProduceNewReportData, positionsMapping } from "./config";
import utils from "@/utils";
import { random } from "lodash";
import { gsap } from 'gsap';

const baseOption = { width: 1920, height: 1080 };
const tl = gsap.timeline({
  repeat: -1,
  ease: "none",
});
export default {
  name: "QualityInspection",
  components: { IndicatorCardItem, FinalScore },
  data() {
    return {
      reportInfo: null,
      indicatorCardAnimationState: "paused",
      indicatorCardAnimationDuration: 1.5,
      reloadKey: Math.random().toString(),
      positions: positionsMapping,
      scanningDuration: 120,
      loading: false,
      indicatorScale: 1,
    };
  },
  computed: {
    psaName() {
      const psaInfo = this.$ls.get(PSA_INFO);
      return psaInfo.psaNameList && psaInfo.psaNameList[0];
    },
    psId() {
      return this.$ls.get(PSA_INFO).psId;
    },
  },
  async mounted() {
    // 初始化高度监听
    window.addEventListener("resize", this.resizeIndicatorSize);
    this.resizeIndicatorSize();
    this.startScanAnimation();
    // 初始化查询是否存在最近检测的报告
    await this.queryLatestReport();
    if (!this.$isEmpty(this.reportInfo)) {
      setTimeout(() => {
        this.indicatorCardAnimationState = "running";
        this.reloadKey = Math.random().toString();
      }, 500);
    }
  },
  methods: {
    resizeIndicatorSize() {
      const el = document.querySelector(".global-header");
      const height = el.getBoundingClientRect().height;
      this.indicatorScale = $(window).height() / (baseOption.height - height);
      this.setWrapperStyle();
    },
    async produceNewReport() {
      // const startTime = new Date().getTime();
      const res = await getModelData({
        modelName: "produceNewReport",
        psId: this.psId,
      });
      // const endTime = new Date().getTime();
      // 至少保证5~10s时间
      // const duration = endTime - startTime;
      // if (duration < 5000) {
      //   const max = 10 * 1000 - duration;
      //   const num = random(5 * 1000, max);
      //   await utils.sleep(num);
      // }
      await utils.sleep(5 * 1000);
      return formatProduceNewReportData(res?.result_data?.dataResult ?? {});
    },
    async startSmartCheck() {
      this.loading = true;
      this.speedUpScan();
      this.reportInfo = await this.produceNewReport();
      this.loading = false;
      tl.timeScale(1);
      setTimeout(() => {
        this.indicatorCardAnimationState = "running";
        this.reloadKey = Math.random().toString();
      }, 500);
    },
    startScanAnimation() {
      const scanElement = $(".wrapper .radar .inner-scanning");
      tl.to(scanElement, {
        duration: this.scanningDuration,
        rotation: 360,
        ease: "none",
      });
    },
    speedUpScan() {
      tl.timeScale(10);
      this.indicatorCardAnimationState = "paused";
      this.reloadKey = Math.random().toString();
      this.reportInfo = null;
    },
    // 获取最新已经生成的报告信息
    async queryLatestReport() {
      this.loading = true;
      const res = await getModelData({
        modelName: "historyReport",
        psId: this.psId,
      });
      this.loading = false;
      const reportFullInfo = res?.result_data?.dataResult?.reportFullInfo;
      this.reportInfo =
        reportFullInfo &&
        formatProduceNewReportData(
          JSON.parse(res?.result_data?.dataResult?.reportFullInfo)
        );
    },
    openHistoryReport() {
      this.$refs.drawerView.init(
        "3",
        this.reportInfo,
        "/health/inspection/modules/HistoryReport"
      );
    },
    setWrapperStyle() {
      const el = document.querySelector(".global-header");
      const height = el.getBoundingClientRect().height;
      const container = document.querySelector(".container");
      container.style.marginTop = `${height}px`;
      container.style.height = `calc(100% - ${height}px)`;
      const wrapper = document.querySelector(".wrapper");
      wrapper.style.marginTop = `${height}px`;
      wrapper.style.height = `calc(100% - ${height}px)`;
      const secondHeight = document
        .querySelector(".second-menu")
        .getBoundingClientRect().height;
      const topMenu = document.querySelector(".left-top-menu");
      topMenu.style.top = `${secondHeight + 6}px`;
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeIndicatorSize);
    tl.kill();
    tl.clear();
  },
};
</script>

<style scoped lang="less">
.container {
  position: fixed;
  left: 0;
  top: 0;
  height: calc(100% - 64px);
  background-color: rgba(23, 39, 56, 0.6);
  background-image: image-set(
      url("../../../assets/images/inspection/map-bg.webp") type("image/webp"),
      url("../../../assets/images/inspection/map-bg.png") type("image/png")
  );
}

.wrapper {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: @di-color-text-white;
  margin-top: 64px;

  .top-linear-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 105px;
    background: linear-gradient(0deg, rgba(5, 32, 68, 0) 0%, #052145 100%);
  }

  .bottom-linear-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 105px;
    background-image: url("../../../assets/images/inspection/bottom-shadow-bg.png");
    background-size: cover;
  }

  .left {
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    background-image: url("../../../assets/images/inspection/left.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100%;

    //.corner-1 {
    //  position: absolute;
    //  top: 16px;
    //  left: 30px;
    //}
    //
    //.corner-2 {
    //  position: absolute;
    //  bottom: 16px;
    //  left: 30px;
    //}
  }

  .right {
    position: absolute;
    top: 0;
    right: 0;
    width: 32px;
    background-image: url("../../../assets/images/inspection/left.png");
    background-repeat: no-repeat;
    transform: rotate(180deg);
    background-size: 100% 100%;
    height: 100%;

    //.corner-3 {
    //  position: absolute;
    //  top: 16px;
    //  right: -30px;
    //}
    //
    //.corner-4 {
    //  position: absolute;
    //  bottom: 16px;
    //  right: -30px;
    //}
  }

  .left-top-menu {
    position: absolute;
    width: 100%;
    padding: 0 35px;
    top: 60px;
    z-index: 2;

    .history-report-btn {
      position: absolute;
      right: 30px;
      background: rgba(133, 202, 255, 0.1);
      color: @di-color-text-main;
    }
  }

  .content {
    position: relative;

    .radar {
      background-image: image-set(
          url("../../../assets/images/inspection/radar-bg.webp") type("image/webp"),
          url("../../../assets/images/inspection/radar-bg.png") type("image/png")
      );
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      width: 820px;
      height: 820px;

      .inner-scanning {
        background-image: image-set(
            url("../../../assets/images/inspection/scanning.webp") type("image/webp"),
            url("../../../assets/images/inspection/scanning.png") type("image/png")
        );
        background-position: center center;
        width: 490px;
        height: 490px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        /*animation: scanning var(--speed)  infinite linear;*/
        transform-origin: center center;
      }
    }

    .random-icon-gif {
      background-image: url("../../../assets/images/inspection/gifs/random-icon.gif");
      width: 470px;
      height: 470px;
      background-size: contain;
      position: absolute;
    }

    .wave-line {
      background-image: url("../../../assets/images/inspection/gifs/wave-line.gif");
      width: 207px;
      height: 207px;
      background-size: contain;
      background-position: center;
      position: absolute;
    }

    .water-ripple {
      width: 800px;
      height: 800px;
      position: absolute;
      background-image: url("../../../assets/images/inspection/water-ripple.png");
      background-size: contain;
      background-position: center;
      animation: ripple 2s linear infinite;
    }

    .smart-check {
      position: absolute;
      background-image: url("../../../assets/images/inspection/inspection-btn-bg.png");
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      width: 194px;
      height: 63px;

      &:hover {
        background-image: url("../../../assets/images/inspection/inspection-btn-hover.png");
        transition: 0.2s;
      }
    }
  }

  .finished-smart-check {
    position: absolute;
    bottom: 38px;
    background-image: url("../../../assets/images/inspection/finished-btn-bg.png");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    width: 160px;
    height: 40px;
    left: 50%;
    transform: translateX(-50%);

    &:hover {
      background-image: url("../../../assets/images/inspection/finished-btn-bg-hover.png");
      transition: 0.2s;
    }
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(2);
  }
  100% {
    transform: scale(3);
  }
}
</style>
