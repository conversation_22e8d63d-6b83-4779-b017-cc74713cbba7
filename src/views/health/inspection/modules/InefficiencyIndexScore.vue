<!--低效指数-->
<template>
  <div>
    <div class="drawer-content-title">设备低效</div>
    <div class="fault-box" v-if="reportFaultDetailData.equInefficiency?.length">
      <FaultCardItem
        v-for="(item, index) in reportFaultDetailData.equInefficiency"
        :key="index"
        :faultCardItem="item"
        :is-fault="false"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
    <div class="drawer-content-title margin-t-16">系统低效</div>
    <div
      class="fault-box"
      v-if="reportFaultDetailData.systemInefficiencyFault?.length"
    >
      <FaultCardItem
        v-for="(item, index) in reportFaultDetailData.systemInefficiencyFault"
        :key="index"
        :faultCardItem="item"
        :show-alarm-happen-time="false"
        :is-fault="false"
        :show-fault-number="true"
        :show-alarm-opinion-content="false"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import FaultCardItem from "@/views/health/inspection/modules/FaultCardItem.vue";

export default {
  name: "InefficiencyIndexScore",
  components: { FaultCardItem },
  props: {
    reportFaultDetailData: PropTypes.object.def({}),
  },
};
</script>

<style scoped lang="less">
.fault-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(352px, 1fr));
  grid-gap: 16px;
  padding: 0 16px;
}
</style>
