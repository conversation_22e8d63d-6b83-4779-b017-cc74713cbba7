<template>
  <div class="container">
    <div class="head flex-space-between">
      <div class="title flex-center">
        <svg-icon icon-class="inspection-tip" class="font-size-20 margin-r-8" />
        <span class="text">故障指数</span>
      </div>
      <CardItemChart
        chart-id="left-top-chart"
        :score="cardItemData.finalScore"
      />
    </div>
    <div class="content">
      <div
        class="content-item flex-space-between"
        v-for="(item, index) in dataList"
        :key="index"
        :style="{ flex: item.flex }"
      >
        <div class="left flex-space-between margin-r-32">
          <svg-icon icon-class="device-stop" class="font-size-35" />
          <div class="type flex-center">
            <span class="first-name font-14">{{ item.firstName }}</span>
            <span class="second-name">{{ item.secondName }}</span>
          </div>
        </div>
        <div class="right">
          <div class="alarm-details">
            <div
              class="alarm-item flex-space-between"
              v-for="(k, kIndex) in item.alarmList"
              :key="kIndex"
            >
              <span class="key opacity-70">{{ k?.name }}</span>
              <span
                :class="{
                  'value': true,
                  'num-font-400 font-22': kIndex != item.alarmList?.length - 1,
                  'font-14': kIndex == item.alarmList?.length - 1,
                }"
                :title="cardItemData.alarmList?.[index]?.[k.key]"
                >{{
                  cardItemData.alarmList?.[index]?.[k.key]?.toString() || "--"
                }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import CardItemChart from "@/views/health/inspection/modules/CardItemChart.vue";

export default {
  name: "LeftTop",
  components: { CardItemChart },
  props: {
    cardItemData: PropTypes.object.def({
      finalScore: 0,
      alarmList: [],
    }),
  },
  data() {
    return {
      dataList: [
        {
          icon: "device-safety-alarm",
          firstName: "设备",
          secondName: "停机故障",
          flex: 1.5,
          alarmList: [
            {
              name: "停机故障(次)",
              key: "faultNumber",
            },
            {
              name: "损失发电量(kWh)",
              key: "powerLoss",
            },
            {
              name: "告警频次最高设备",
              key: "deviceName",
            },
          ],
        },
        {
          icon: "device-phone-fault",
          firstName: "设备",
          secondName: "通讯故障",
          flex: 1,
          alarmList: [
            {
              name: "通讯故障(次)",
              key: "faultNumber",
            },
            {
              name: "告警频次最高设备",
              key: "deviceName",
            },
          ],
        },
      ],
    };
  },
};
</script>
<style scoped lang="less"></style>
