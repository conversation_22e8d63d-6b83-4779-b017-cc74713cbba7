<!--安全指数-->
<template>
  <div>
    <div class="drawer-content-title">隐患运行</div>
    <div
      class="fault-box"
      v-if="reportFaultDetailData.hiddenDangerOperation?.length"
    >
      <FaultCardItem
        v-for="(item, index) in reportFaultDetailData.hiddenDangerOperation"
        :key="index"
        :faultCardItem="item"
        :show-alarm-power-loss="false"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
    <div class="drawer-content-title margin-t-16">人员安全</div>
    <div class="fault-box" v-if="reportFaultDetailData.personSafety?.length">
      <SafetyCardItem
        v-for="(item, index) in reportFaultDetailData.personSafety"
        :key="index"
        :safety-item="item"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
    <div class="drawer-content-title margin-t-16">环境安全</div>
    <div class="fault-box" v-if="reportFaultDetailData.envSafety?.length">
      <SafetyCardItem
        v-for="(item, index) in reportFaultDetailData.envSafety"
        :key="index"
        :safety-item="item"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import FaultCardItem from "@/views/health/inspection/modules/FaultCardItem.vue";
import SafetyCardItem from "@/views/health/inspection/modules/SafetyCardItem.vue";

export default {
  name: "SafetyIndexScore",
  components: { FaultCardItem, SafetyCardItem },
  props: {
    reportFaultDetailData: PropTypes.object.def({}),
  },
};
</script>

<style scoped lang="less">
.fault-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(352px, 1fr));
  grid-gap: 16px;
  padding: 0 16px;
}
</style>
