<template>
  <div class="safety-card-item">
    <div class="image-box margin-b-24">
      <img :src="getImageFn()" class="width-height-100" />
    </div>
    <div class="safety-box">
      <div class="safety-item">
        <span class="label">
          <svg-icon
            icon-class="2d-alarm-monitor"
            class="font-size-20 margin-r-4"
          />设备名称：
        </span>
        <span class="value">{{ safetyItem.deviceName }}</span>
      </div>
      <div class="safety-item text-ellipsis">
        <span class="label">
          <svg-icon
            icon-class="2d-alarm-warning"
            class="font-size-20 margin-r-4"
          />诊断原因：</span
        >
        <span>{{ safetyItem.reasonName }}</span>
      </div>
      <div class="safety-item">
        <span class="label">
          <svg-icon
            icon-class="2d-alarm-time"
            class="font-size-20 margin-r-4"
          />发生时间：</span
        >
        <span class="value">{{
          moment(safetyItem.alarmHappenTime).format("YYYY-MM-DD HH:mm:ss")
        }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import moment from "moment";

export default {
  name: "SafetyCardItem",
  props: {
    safetyItem: PropTypes.object.def({}),
  },
  data() {
    return {
      moment,
    };
  },
  methods: {
    getImageFn() {
      const path = this.safetyItem.fileUrlThumbnail;
      if (this.$isEmpty(path))
        return require("@/assets/images/error-image.png");
      return path;
    },
  },
};
</script>

<style scoped lang="less">
.safety-card-item {
  //width: 352px;
  height: 382px;
  padding: 8px;
  border-radius: 4px;
  background: #1f559e;
  border: 1px solid;
  border-image: linear-gradient(
      180deg,
      rgba(64, 170, 255, 0.53) 0%,
      rgba(64, 170, 255, 0) 32%,
      rgba(64, 170, 255, 0.0631) 79%,
      rgba(64, 170, 255, 0.12) 100%
    )
    1;
  box-sizing: border-box;
  box-shadow: inset 0px 7px 10px 0px rgba(64, 170, 255, 0.14);

  .image-box {
    width: 100%;
    height: 240px;
  }

  .safety-box {
    .safety-item {
      display: flex;
      align-items: center;
      line-height: 22px;

      &:not(:last-child) {
        margin-bottom: 16px;
      }

      .label {
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
