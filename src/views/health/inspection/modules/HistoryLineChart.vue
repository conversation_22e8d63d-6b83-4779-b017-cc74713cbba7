<template>
  <a-spin :spinning="loading" class="height-100 width-100">
    <div class="history-line-chart-box detail-modal-bg-2d">
      <modal-title title="历史趋势" class="modal-title">
        <div slot="right" class="flex-end">
          <svg-icon
            icon-class="close"
            class="font-10 margin-r-8 cursor-pointer color-text-white"
            @click="$emit('close')"
          />
        </div>
      </modal-title>
      <a-row style="margin: 16px 24px">
        <a-col :span="8">
          <div class="search-item">
            <span class="search-label">时间选择</span>
            <a-range-picker
              v-model="rangeDate"
              class="width-100"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              @change="handleChange"
              :allow-clear="false"
              :disabled-date="disabledDate"
              @calendarChange="calendarChange"
            />
          </div>
        </a-col>
      </a-row>
      <div class="history-line-chart"></div>
    </div>
  </a-spin>
</template>

<script>
import echarts from "@/utils/enquireEchart";
import ModalTitle from "@/views/2dMap/modules/ModalTitle.vue";
import { getModelData } from "@/api/2dMap/psOverview";
import PropTypes from "ant-design-vue/es/_util/vue-types";
import moment from "moment";
import { renderChart } from "../config";

export default {
  name: "HistoryLineChart",
  components: { ModalTitle },
  props: {
    psId: PropTypes.string.def(""),
  },
  data() {
    return {
      myChart: null,
      loading: false,
      rangeDate: [
        moment().add(-30, "days").format("YYYY-MM-DD"),
        moment().format("YYYY-MM-DD"),
      ],
      startDate: null,
    };
  },
  mounted() {
    this.initLineChartData();
  },
  methods: {
    async initLineChartData() {
      this.loading = true;
      let chartDom = document.querySelector(".history-line-chart");
      this.myChart = echarts.init(chartDom);
      const [recordStartDate, recordEndDate] = this.rangeDate;
      const res = await getModelData({
        modelName: "historyReportScoreGraph",
        psId: this.psId,
        recordStartDate,
        recordEndDate,
      });
      this.loading = false;
      const option = renderChart(res);
      this.myChart.setOption(option, true);
    },
    handleChange() {
      this.initLineChartData();
    },
    calendarChange(dates) {
      this.startDate = dates?.[0];
      if(dates?.[1]) this.startDate = null;
    },
    disabledDate(current) {
      return this.startDate
        ? moment(current).valueOf() <=
            moment(this.startDate).add(-30, "days").valueOf() ||
            moment(current).valueOf() >=
              moment(this.startDate).add(30, "days").valueOf()
        : false;
    },
  },
  beforeDestroy() {
    echarts.dispose(this.myChart);
  },
};
</script>

<style scoped lang="less">
.history-line-chart-box {
  width: 1040px;
  height: 520px;

  .history-line-chart {
    //padding: 0 32px 16px;
    width: 100%;
    height: calc(100% - 94px);
  }
}
</style>
