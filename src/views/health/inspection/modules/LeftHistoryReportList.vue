<template>
  <div class="left-history-report">
    <div class="top-box flex-space-between margin-r-32">
      <div class="relative flex-space-between" style="width: calc(100% - 24px)">
        <template v-if="!isShowSearch">
          <span class="title">报告名称</span>
          <a-icon
            type="search"
            class="cursor-pointer font-size-16"
            @click="showSearch(true)"
          />
        </template>
        <a-input
          v-else
          v-model="searchValue"
          class="search-input"
          placeholder="输入关键字查询"
          @pressEnter="$emit('handleSearch', searchValue)"
        >
          <template #addonAfter>
            <a-icon
              type="search"
              class="cursor-pointer"
              @click="$emit('handleSearch', searchValue)"
            />
            <div class="split-line"></div>
            <svg-icon
              icon-class="close"
              class="cursor-pointer"
              @click="handleBack"
            />
          </template>
        </a-input>
      </div>
      <a-icon
        type="line-chart"
        class="cursor-pointer font-size-16 margin-l-16"
        @click="$emit('showHistoryLine')"
      />
    </div>
    <div class="content margin-t-8">
      <div class="tree">
        <a-icon type="caret-down" class="margin-r-8" />
        <span>{{ psName }}</span>
      </div>
      <div class="report-list">
        <div
          v-for="(item, index) in reportList"
          :key="index"
          :class="`report-item psHealthLeve-${item.psHealthLeve} ${
            currentReportId && currentReportId === item.repotId
              ? 'active-' + item.psHealthLeve
              : ''
          }`"
          @click="$emit('selectReport', item)"
        >
          <div class="font-14 opacity-100">{{ item.reportName || "--" }}</div>
          <div class="font-14 opacity-50">{{ item.detectDate }}</div>
          <div
            class="score flex-center num-font-500"
            :style="{
              'background-image': `url(${getBgByLevel(item.psHealthLeve)})`,
            }"
          >
            {{ item.finalScore }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import { PSA_INFO } from "@/store/mutation-types";

export default {
  name: "LeftHistoryReportList",
  props: {
    reportList: PropTypes.array.def([]),
    currentReportId: PropTypes.number.def(null),
  },
  data() {
    return {
      isShowSearch: false,
      searchValue: undefined,
    };
  },
  computed: {
    psName() {
      return this.$ls.get(PSA_INFO)?.psaNameList?.[0];
    },
  },
  methods: {
    getBgByLevel(psHealthLeve) {
      return require("../../../../assets/images/inspection/psHealthLeve-" +
        psHealthLeve +
        ".png");
    },
    showSearch(isShow) {
      this.isShowSearch = isShow;
    },
    handleBack() {
      this.searchValue = undefined;
      this.showSearch(false);
      this.$emit("handleSearch", this.searchValue);
    },
  },
};
</script>

<style scoped lang="less">
.left-history-report {
  color: @di-color-text-white;
  padding: 16px 16px 16px 24px;

  .top-box {
    height: 32px;

    :deep(.search-input) {
      background: linear-gradient(
        180deg,
        #164588 0%,
        #16478c 57%,
        #15529d 100%
      );

      .ant-input-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .ant-input {
          border-right: none;
        }

        .ant-input-group-addon {
          width: 62px;
          display: flex;
          height: 32px;
          align-items: center;
          background-color: transparent;
          border-color: @di-border-color-second;
          color: @di-color-text-main;

          .split-line {
            width: 1px;
            height: 16px;
            background-color: @di-border-color-second;
            margin: 0 8px;
          }
        }
      }
    }
  }

  .content {
    .report-list {
      display: flex;
      flex-direction: column;
      margin: 16px 0 0 20px;
      height: calc(100vh - 290px);
      overflow: auto;

      .report-item {
        margin-bottom: 24px;
        position: relative;
        width: 90%;
        cursor: pointer;

        &.active-1 {
          background: linear-gradient(
            270deg,
            #0e96e8 0%,
            rgba(14, 150, 232, 0) 100%
          );
        }

        &.active-2 {
          background: linear-gradient(
            270deg,
            #d0a200 0%,
            rgba(208, 162, 0, 0) 100%
          );
        }

        &.active-3 {
          background: linear-gradient(
            270deg,
            #aa254c 0%,
            rgba(170, 37, 76, 0) 100%
          );
        }

        .score {
          position: absolute;
          right: -20px;
          top: 0;
          width: 40px;
          height: 46px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          font-size: 20px;
        }
      }

      .psHealthLeve-1 {
        background: linear-gradient(
          270deg,
          rgba(14, 150, 232, 0.25) 0%,
          rgba(14, 150, 232, 0) 100%
        );

        &:hover {
          background: linear-gradient(
            270deg,
            #0e96e8 0%,
            rgba(14, 150, 232, 0) 100%
          );
        }
      }

      .psHealthLeve-2 {
        background: linear-gradient(
          270deg,
          rgba(208, 162, 0, 0.25) 0%,
          rgba(208, 162, 0, 0) 100%
        );

        &:hover {
          background: linear-gradient(
            270deg,
            #d0a200 0%,
            rgba(208, 162, 0, 0) 100%
          );
        }
      }

      .psHealthLeve-3 {
        background: linear-gradient(
          270deg,
          rgba(170, 37, 76, 0.25) 0%,
          rgba(170, 37, 76, 0) 100%
        );

        &:hover {
          background: linear-gradient(
            270deg,
            #aa254c 0%,
            rgba(170, 37, 76, 0) 100%
          );
        }
      }
    }
  }
}
</style>
