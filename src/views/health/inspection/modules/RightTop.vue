<template>
  <div class="container">
    <div class="head flex-space-between">
      <div class="title flex-center">
        <svg-icon icon-class="low-tip" class="font-size-20 margin-r-8" />
        <span class="text">低效指数</span>
      </div>
      <CardItemChart
        chart-id="left-top-chart"
        :score="cardItemData.finalScore"
      />
    </div>
    <div class="content">
      <div
        class="content-item flex-space-between"
        v-for="(item, index) in dataList"
        :key="index"
      >
        <div class="left flex-space-between margin-r-32">
          <svg-icon :icon-class="item.icon" class="font-size-35" />
          <div class="type flex-center">
            <span class="first-name font-14">{{ item.firstName }}</span>
            <span class="second-name" v-if="item.secondName">{{
              item.secondName
            }}</span>
          </div>
        </div>
        <div class="right">
          <div class="alarm-details">
            <div
              class="alarm-item flex-space-between"
              v-for="(k, kIndex) in item.alarmList"
              :key="kIndex"
            >
              <span class="key opacity-70">{{ k.name }}</span>
              <span class="value num-font-400 font-22">{{
                cardItemData.alarmList?.[index]?.[k.key]?.toString() || "--"
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import CardItemChart from "@/views/health/inspection/modules/CardItemChart.vue";

export default {
  name: "RightTop",
  components: { CardItemChart },
  props: {
    cardItemData: PropTypes.object.def({
      finalScore: 0,
      alarmList: [],
    }),
  },
  data() {
    return {
      dataList: [
        {
          icon: "system-inefficiency",
          firstName: "系统低效",
          alarmList: [
            {
              name: "损失发电量(kWh)",
              key: "powerLoss",
            },
            {
              name: "低效次数(次)",
              key: "faultNumber",
            },
          ],
        },
        {
          icon: "device-inefficient",
          firstName: "设备低效",
          alarmList: [
            {
              name: "损失发电量(kWh)",
              key: "powerLoss",
            },
            {
              name: "低效设备个数(个)",
              key: "inefficiencyFreq",
            },
          ],
        },
      ],
    };
  },
};
</script>

<style scoped lang="less"></style>
