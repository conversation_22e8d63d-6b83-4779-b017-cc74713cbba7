<template>
  <div class="container">
    <div class="head flex-space-between">
      <div class="title flex-center">
        <svg-icon icon-class="safety-tip" class="font-size-20 margin-r-8" />
        <span class="text">安全指数</span>
      </div>
      <CardItemChart
        chart-id="left-bottom-chart"
        :score="cardItemData.finalScore"
      />
    </div>
    <div class="content">
      <div
        class="content-item flex-space-between"
        v-for="(item, index) in dataList"
        :key="index"
      >
        <div class="left flex-space-between margin-r-32">
          <svg-icon :icon-class="item.icon" class="font-size-35" />
          <div class="type flex-center">
            <span class="first-name font-14">{{ item.firstName }}</span>
            <span class="second-name">安全告警</span>
          </div>
        </div>
        <div class="right">
          <div class="alarm-details">
            <div class="alarm-item flex-space-between">
              <span class="key opacity-70">告警次数(次)</span>
              <span class="value num-font-400 font-22">{{
                cardItemData.alarmList?.[index]?.faultNumber?.toString() || "--"
              }}</span>
            </div>
            <div class="alarm-item flex-space-between">
              <span class="key opacity-70">告警频次最高原因</span>
              <span
                class="value"
                :title="cardItemData.alarmList?.[index]?.alarmReason"
                >{{
                  cardItemData.alarmList?.[index]?.alarmReason || "--"
                }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import CardItemChart from "./CardItemChart.vue";

export default {
  name: "LeftBottom",
  components: { CardItemChart },
  props: {
    cardItemData: PropTypes.object.def({
      finalScore: 0,
      alarmList: [],
    }),
  },
  data() {
    return {
      dataList: [
        {
          icon: "device-safety-alarm",
          firstName: "设备",
        },
        {
          icon: "person-safety-alarm",
          firstName: "人员",
        },
        {
          icon: "environmental-safety-alarm",
          firstName: "环境",
        },
      ],
    };
  },
};
</script>

<style scoped lang="less"></style>
