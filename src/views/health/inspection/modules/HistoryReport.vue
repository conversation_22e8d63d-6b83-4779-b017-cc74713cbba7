<template>
  <div class="history-report width-height-100">
    <a-spin :spinning="loading" class="width-height-100">
      <a-row class="width-height-100">
        <a-col :span="4" class="height-100 left">
          <left-history-report-list
              ref="leftHistoryReportList"
              :report-list="reportList"
              :current-report-id="currentReportData.repotId"
              @showHistoryLine="showHistoryLine"
              @handleSearch="handleSearch"
              @selectReport="selectReport"
          />
        </a-col>
        <a-col :span="20" class="height-100 right">
          <right-history-report-detail
              ref="rightHistoryReportDetail"
              :reportFaultDetailData="reportFaultDetailData"
              :currentReportData="currentReportData"
          />
        </a-col>
      </a-row>
    </a-spin>
    <a-modal
      v-model="historyLineChartVisible"
      :closable="false"
      :maskClosable="false"
      centered
      destroyOnClose
      :footer="null"
      :zIndex="999"
      class="device-detail-modal-small"
      width=""
      :bodyStyle="{ padding: 0 }"
      :getContainer="getDom"
      title=""
    >
      <HistoryLineChart @close="historyLineChartVisible = false" :psId="psId" />
    </a-modal>
  </div>
</template>

<script>
import LeftHistoryReportList from "./LeftHistoryReportList";
import RightHistoryReportDetail from "./RightHistoryReportDetail";
import { getModelData } from "@/api/2dMap/psOverview";
import { PSA_INFO } from "@/store/mutation-types";
import HistoryLineChart from "./HistoryLineChart";
import { cloneDeep } from "lodash";

export default {
  name: "HistoryReport",
  components: {
    HistoryLineChart,
    LeftHistoryReportList,
    RightHistoryReportDetail,
  },
  data() {
    return {
      reportList: [],
      backReportList: [],
      historyLineChartVisible: false,
      currentReportData: {},
      reportFaultDetailData: {},
      loading: false
    };
  },
  mounted() {
    this.getReportList();
  },
  computed: {
    psId() {
      return this.$ls.get(PSA_INFO)?.psId;
    },
  },
  methods: {
    init() {
      return "详情";
    },
    async getReportList() {
      const res = await getModelData({
        modelName: "historyReportTagList",
        psId: this.psId,
      });
      this.reportList =
        res?.result_data?.dataResult?.historyReportTagList ?? [];
      this.backReportList = cloneDeep(this.reportList);
      this.currentReportData = this.reportList?.[0] ?? {};
      await this.getReportFaultDetail();
    },
    getDom() {
      return document.querySelector(".history-report");
    },
    showHistoryLine() {
      this.historyLineChartVisible = true;
    },
    handleSearch(value) {
      if (this.$isEmpty(value)) {
        this.reportList = cloneDeep(this.backReportList);
        return;
      }
      this.reportList = this.backReportList.filter(
        (item) =>
          item?.reportName.toLowerCase().indexOf(value.toLowerCase()) >= 0
      );
    },
    async getReportFaultDetail() {
      this.loading = true;
      const res = await getModelData({
        modelName: "historyReportFault",
        psId: this.psId,
        reportId: this.currentReportData.repotId,
      });
      this.loading = false;
      this.reportFaultDetailData = res?.result_data?.dataResult;
    },
    async selectReport(item) {
      this.currentReportData = item;
      this.$refs?.rightHistoryReportDetail?.resetActiveKey();
      await this.$nextTick();
      this.$refs?.rightHistoryReportDetail?.renderLiquid();
      this.$refs?.rightHistoryReportDetail?.renderRadar();
      await this.getReportFaultDetail();
    },
  },
};
</script>

<style scoped lang="less">
.history-report {
  .left {
    border-right: 1px solid #376da2;
  }

  .right {
  }
}
</style>
