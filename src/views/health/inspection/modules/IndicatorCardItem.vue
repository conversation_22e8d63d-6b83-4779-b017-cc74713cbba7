<template>
  <div
    :class="{
      [position]: true,
      'indicator-card-item': true,
      'breath-danger': isDangerByStep(reportInfo?.[position]?.finalScore),
      'breath-normal': !isDangerByStep(reportInfo?.[position]?.finalScore),
    }"
    :style="{
      '--indicator-card-animation-state': indicatorCardAnimationState,
      '--card-animation-duration': indicatorCardAnimationDuration,
    }"
  >
    <component :is="position" :card-item-data="reportInfo?.[position]" />
  </div>
</template>
<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import LeftTop from "./LeftTop.vue";
import LeftBottom from "./LeftBottom.vue";
import RightTop from "./RightTop.vue";
import RightBottom from "./RightBottom.vue";

export default {
  name: "IndicatorCardItem",
  props: {
    position: PropTypes.string.def("RightBottom"),
    indicatorCardAnimationState: PropTypes.string.def(""),
    indicatorCardAnimationDuration: PropTypes.number.def(5),
    reportInfo: PropTypes.object.def({}),
  },
  components: { LeftTop, LeftBottom, RightTop, RightBottom },
  data() {
    return {
      borders: new Array(4).fill(0),
    };
  },
  methods: {
    isDangerByStep(step) {
      return step < 60;
    },
  },
};
</script>
<style scoped lang="less">
@translateX: 580px;
@translateY: 180px;
.indicator-card-item {
  width: 520px;
  height: 320px;
  padding: 24px;
  position: absolute;

  &.breath-danger::before {
    background-image: url("../../../../assets/images/inspection/alarm-danger-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    content: "";
    animation: boxShadowBreath 1s linear infinite;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
  }

  &.breath-normal::before {
    background-image: url("../../../../assets/images/inspection/alarm-normal-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
  }

  &.RightBottom {
    animation: moveLowerRightCorner calc(var(--card-animation-duration) * 1s) linear var(--indicator-card-animation-state) forwards;
  }

  &.RightTop {
    animation: moveUpperRightCorner calc(var(--card-animation-duration) * 1s) linear var(--indicator-card-animation-state) forwards;
  }

  &.LeftBottom {
    animation: moveLowerLeftCorner calc(var(--card-animation-duration) * 1s) linear var(--indicator-card-animation-state) forwards;
  }

  &.LeftTop {
    animation: moveUpperLeftCorner calc(var(--card-animation-duration) * 1s) linear var(--indicator-card-animation-state) forwards;
  }

  :deep(.container) {
    width: 100%;
    height: 100%;
    background-image: url("../../../../assets/images/inspection/card-item-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .head {
      width: 100%;
      height: 40px;
      padding: 0 12px;
      background: linear-gradient(90deg, #0661AE 0%, rgba(5, 97, 175, 0) 100%);
      border-left: 3px solid #BDE2FF;
      position: relative;

    }

    .content {
      width: 100%;
      display: flex;
      flex-direction: column;
      height: calc(100% - 46px);
      padding: 12px;

      .content-item {
        background-image: url("../../../../assets/images/inspection/alarm-item-bg.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 0 12px;
        flex: 1;

        &:not(:last-child) {
          margin-bottom: 12px;
        }

        .left {
          width: 103px;

          .type {
            flex-direction: column;
          }
        }

        .right {
          flex: 1;

          .value {
            max-width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            text-align: right;
          }
        }
      }
    }
  }

}


@keyframes moveLowerRightCorner {
  from {
    transform: translate(0px, 0px) scale(0);

  }
  to {
    transform: translate(@translateX, @translateY) scale(1);

  }
}

@keyframes moveUpperRightCorner {
  from {
    transform: translate(0px, 0px) scale(0);
  }
  /*// 沿着左上角50deg方向缓慢滑动*/
  to {
    transform: translate(@translateX, -@translateY) scale(1);
  }
}

@keyframes moveUpperLeftCorner {
  from {
    transform: translate(0px, 0px) scale(0);
  }
  /*// 沿着左上角50deg方向缓慢滑动*/
  to {
    transform: translate(-@translateX, -@translateY) scale(1);
  }
}

@keyframes moveLowerLeftCorner {
  from {
    transform: translate(0px, 0px) scale(0);
  }
  /*// 沿着左上角50deg方向缓慢滑动*/
  to {
    transform: translate(-@translateX, @translateY) scale(1);
  }
}

@keyframes boxShadowBreath {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
</style>
