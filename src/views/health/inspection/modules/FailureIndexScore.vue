<!--故障指数-->
<template>
  <div>
    <div class="drawer-content-title">停机故障</div>
    <div class="fault-box" v-if="reportFaultDetailData.shutdownFault?.length">
      <FaultCardItem
        v-for="(item, index) in reportFaultDetailData.shutdownFault"
        :key="index"
        :faultCardItem="item"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
    <div class="drawer-content-title margin-t-16">通讯故障</div>
    <div class="fault-box" v-if="reportFaultDetailData.interrupCom?.length">
      <FaultCardItem
        v-for="(item, index) in reportFaultDetailData.interrupCom"
        :key="index"
        :faultCardItem="item"
      />
    </div>
    <div v-else style="margin: 0 auto" class="no-data"></div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import FaultCardItem from "./FaultCardItem.vue";

export default {
  name: "FailureIndexScore",
  props: {
    reportFaultDetailData: PropTypes.object.def({}),
  },
  components: { FaultCardItem },
};
</script>

<style scoped lang="less">
.fault-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(352px, 1fr));
  grid-gap: 16px;
  padding: 0 16px;
}
</style>
