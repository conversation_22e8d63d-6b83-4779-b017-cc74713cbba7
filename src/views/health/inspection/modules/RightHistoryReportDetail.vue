<template>
  <div class="right-history-report-detail width-height-100">
    <div class="title flex-center">
      <span class="psName font-size-18">{{ currentReportData.psName }}</span>
      <span class="detectDate">{{ currentReportData.detectDate }}</span>
    </div>
    <div class="score-panel margin-t-16">
      <div class="ps-info flex-center">
        <div class="liquidfill-score"></div>
        <div class="level-msg-info">
          <div class="level-msg margin-b-12">
            <span class="label font-14">电站健康水平：</span>
            <span
              class="font-20"
              :class="'level-' + currentReportData.psHealthLeve"
              >{{
                levelMapping[currentReportData.psHealthLeve]?.level || "--"
              }}</span
            >
          </div>
          <span class="msg font-12 opacity-50">{{
            levelMapping[currentReportData.psHealthLeve]?.msg || "--"
          }}</span>
        </div>
      </div>
      <div class="report-indicators">
        <div
          class="indicator-item flex-center"
          v-for="item in Object.keys(indicators)"
          :key="item"
        >
          <svg-icon :icon-class="item" class="indicator-icon" />
          <div class="indicator-info">
            <span class="font-14">{{ indicators[item] }}</span>
            <div>
              <span class="num-font-700 font-size-22">{{
                currentReportData[item]
              }}</span>
              <span class="margin-l-2">分</span>
            </div>
          </div>
        </div>
        <div class="radar-indicator indicator-item flex-center"></div>
      </div>
    </div>
    <div class="tab-content margin-t-16">
      <a-tabs v-model="activeKey">
        <a-tab-pane
          v-for="item in Object.keys(indicators)"
          :key="item"
          :tab="indicators[item]"
        >
          <component
            class="component-item"
            :is="capitalizeFirstLetter(item)"
            :reportFaultDetailData="reportFaultDetailData"
            :currentReportData="currentReportData"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import AssessmentIndexScore from "./AssessmentIndexScore.vue";
import FailureIndexScore from "./FailureIndexScore.vue";
import InefficiencyIndexScore from "./InefficiencyIndexScore.vue";
import SafetyIndexScore from "./SafetyIndexScore.vue";
import {
  excludeFinalScoreMapping,
  formatLiquidOption,
  formatRadarOption,
  levelMapping,
} from "../config";
import echarts from "@/utils/enquireEchart";
import "echarts-liquidfill";

export default {
  name: "HistoryReportDetail",
  props: {
    reportFaultDetailData: PropTypes.object.def({}),
    currentReportData: PropTypes.object.def({}),
  },
  components: {
    AssessmentIndexScore,
    FailureIndexScore,
    InefficiencyIndexScore,
    SafetyIndexScore,
  },
  data() {
    return {
      myChart1: null,
      myChart2: null,
      indicators: excludeFinalScoreMapping,
      levelMapping,
      activeKey: "failureIndexScore",
    };
  },
  watch: {
    currentReportData: {
      immediate: true,
      handler(value, oldValue) {
        if (
          !this.$isEmpty(value) &&
          JSON.stringify(value) !== JSON.stringify(oldValue)
        ) {
          this.renderLiquid();
          this.renderRadar();
        }
      },
    },
  },
  mounted() {
    this.myChart1 = echarts.init(document.querySelector(".liquidfill-score"));
    this.myChart2 = echarts.init(document.querySelector(".radar-indicator"));
  },
  methods: {
    renderLiquid() {
      const option = formatLiquidOption(
        this.currentReportData?.finalScore ?? 0
      );
      this.myChart1.setOption(option, true);
    },
    renderRadar() {
      const option = formatRadarOption(this.currentReportData);
      this.myChart2.setOption(option, true);
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    resetActiveKey() {
      this.activeKey = "failureIndexScore";
    },
  },
};
</script>

<style scoped lang="less">
.right-history-report-detail {
  padding: 16px 24px 16px 16px;
  color: @di-color-text-white;

  .title {
    height: 27px;

    .detectDate {
      position: absolute;
      right: 24px;
    }
  }

  .score-panel {
    height: 158px;
    width: 100%;
    border-radius: 8px;
    opacity: 1;
    background: #0d3b79;
    display: flex;
    align-items: center;

    .ps-info {
      width: 380px;
      border-right: 1px solid #376da2;

      .liquidfill-score {
        width: 110px;
        height: 110px;
      }

      .level-msg-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        width: calc(100% - 190px);

        .level-1 {
          color: #6ec0ff;
        }

        .level-2 {
          color: #ffc700;
        }

        .level-3 {
          color: #ff2e6b;
        }
      }
    }

    .report-indicators {
      display: flex;
      width: calc(100% - 400px);
      height: 100%;

      .indicator-icon {
        width: 49px;
        height: 44px;
        margin-right: 12px;
      }

      .indicator-item {
        margin: auto;

        .indicator-info {
          display: flex;
          flex-direction: column;
        }

        &.radar-indicator {
          width: 198px;
          height: 100%;
        }
      }
    }
  }

  .tab-content {
    height: calc(100% - 210px);
    border-radius: 8px;
    background: #0d3b79;
    overflow: auto;

    :deep(.component-item) {
      color: @di-color-text-white;
      height: calc(100vh - 485px);
      overflow: auto;
    }
  }
}
</style>
