<template>
  <div :id="chartId" class="card-item-chart">
    <div
      class="image-box flex-center width-height-100"
      :style="{ 'background-image': `url(${scoreImage})` }"
    >
      <span>{{ score }}</span>
      <span>分</span>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import { gsap } from 'gsap';

export default {
  name: "CardItemChart",
  props: {
    chartId: PropTypes.string.def(""),
    score: PropTypes.number.def(0),
  },
  data() {
    return {};
  },
  mounted() {
    this.animationStart();
  },
  computed: {
    scoreImage() {
      const singleDigits = parseInt(this.score / 10);
      if(singleDigits == 10) return require(`@/assets/images/inspection/<EMAIL>`);
      return require(`@/assets/images/inspection/${singleDigits * 10 + 1}-${
        (singleDigits + 1) * 10
      }@2x.png`);
    },
  },
  methods: {
    animationStart() {
      gsap.fromTo(
        `#${this.chartId}`,
        { opacity: 0, transform: "scale(0)", bottom: 0, left: "-61px" },
        {
          zIndex: 2,
          bottom: "-17px",
          duration: 1,
          delay: 1,
          opacity: 1,
          transform: "scale(1)",
          ease: "none",
          left: "65%",
          transformOrigin: "bottom center",
        }
      );
    },
  },
};
</script>

<style scoped lang="less">
.card-item-chart {
  position: absolute !important;
  left: 0;
  bottom: 0;
  width: 122px;
  height: 96px;

  .image-box {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    transform-origin: center;
  }
}
</style>
