<!--考核指数-->
<template>
  <div>
    <div class="drawer-content-title">下网功率因数考核</div>
    <a-spin :spinning="loading">
      <div class="power-assessment"></div>
    </a-spin>
    <div class="drawer-content-title margin-t-16">AGC/AVC策略考核</div>
    <div style="margin: 0 auto" class="no-data"></div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import echarts from "@/utils/enquireEchart";
import { formatPowerLineOption, powerLineParams } from "../config";
import { insightToolsInsightAnalysisData } from "@/api/dataCenter";
import moment from "moment";

export default {
  name: "AssessmentIndexScore",
  props: {
    reportFaultDetailData: PropTypes.object.def({}),
    currentReportData: PropTypes.object.def({}),
  },
  data() {
    return {
      myChart: null,
      loading: false,
    };
  },
  mounted() {
    this.myChart = echarts.init(document.querySelector(".power-assessment"));
  },
  watch: {
    currentReportData: {
      immediate: true,
      deep: true,
      handler() {
        this.renderChart();
      },
    },
  },
  methods: {
    async renderChart() {
      this.loading = true;
      const startTime = moment(this.currentReportData.detectDate)
        .startOf("month")
        .format("YYYY-MM-DD");
      const endTime = moment(this.currentReportData.detectDate).format(
        "YYYY-MM-DD"
      );
      const res = await insightToolsInsightAnalysisData({
        ...powerLineParams,
        startTime,
        endTime,
      });
      this.loading = false;
      const option = formatPowerLineOption(res?.result_data);
      this.myChart.setOption(option, true);
    },
  },
};
</script>

<style scoped lang="less">
.power-assessment {
  width: 100%;
  height: 360px;
}
</style>
