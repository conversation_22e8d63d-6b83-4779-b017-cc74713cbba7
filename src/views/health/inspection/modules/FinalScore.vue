<template>
  <div
    :style="{
      '--scoreDuration': scoreDuration,
      'background-image': `url(${streamImgPath})`,
    }"
    class="final-score"
  >
    <span class="text">电站检测得分</span>
    <div
      class="view-report flex-center cursor-pointer"
      @click="$emit('handleViewReport')"
    >
      查看报告
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import { gsap } from 'gsap';

export default {
  name: "FinalScore",
  props: {
    scoreDuration: PropTypes.number.def(2),
    step: PropTypes.number.def(0),
  },
  computed: {
    streamImgPath() {
      const type = this.scoreConfigFn(this.step);
      if (!type) return "";
      return require("@/assets/images/inspection/gifs/" + type + "-stream.gif");
    },
  },
  mounted() {
    gsap.fromTo(
      ".final-score",
      { opacity: 0, scale: 0 },
      { opacity: 1, scale: 1, duration: 1 }
    );

    setTimeout(() => {
      document.documentElement.style.setProperty(
        "--step",
        this.step.toString()
      );
    }, 200);
  },
  methods: {
    scoreConfigFn(score) {
      if (typeof score !== "number") return;
      if (score >= 0 && score < 60) {
        return "danger";
      } else if (score >= 60 && score < 80) {
        return "warning";
      } else {
        return "normal";
      }
    },
  },
  beforeDestroy() {
    document.documentElement.style.removeProperty("--step");
  },
};
</script>

<style scoped lang="less">
@property --step {
  syntax: "<integer>";
  inherits: true;
  initial-value: 0;
}

.final-score {
  //counter-reset: count var(--step);
  //z-index: var(--step);
  //transition: --step calc(var(--scoreDuration) * 1s) ease-in-out;
  position: absolute;
  width: 240px;
  height: 240px;
  color: @di-color-text-white;

  border-radius: 50%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .text {
    font-size: 16px;
    text-shadow: 0.37px 0.74px 1.48px #004399;
  }

  .view-report {
    position: absolute;
    top: 82%;
    width: 182px;
    height: 62px;
    background-image: url("../../../../assets/images/inspection/view-report-bg.png");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    z-index: 999;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      background-image: url("../../../../assets/images/inspection/view-report-hover.png");
    }
  }

  &::before {
    counter-reset: count var(--step);
    transition: --step calc(var(--scoreDuration) * 1s) ease-in-out;
    font-weight: bold;
    font-size: 50px;
    content: counter(count);
  }
}
</style>
