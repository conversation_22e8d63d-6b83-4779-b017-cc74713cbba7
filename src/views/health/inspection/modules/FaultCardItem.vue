<template>
  <div class="fault-card-item">
    <div class="header">{{ faultCardItem.deviceName }}</div>
    <div class="content">
      <div class="content-item" v-if="showAlarmHappenTime">
        <span class="label">发生时间：</span>
        <span class="value">{{
          moment(faultCardItem.alarmHappenTime).format("YYYY-MM-DD HH:mm:ss") ||
          "--"
        }}</span>
      </div>
      <div class="content-item">
        <span class="label">{{ isFault ? "故障原因" : "低效原因" }}：</span>
        <span class="value">{{ faultCardItem.reasonName || "--" }}</span>
      </div>
      <div class="content-item" v-if="showAlarmPowerLoss">
        <span class="label">损失电量：</span>
        <span class="value power-loss"
          >{{ faultCardItem.alarmPowerLoss || "--" }} kWh</span
        >
      </div>
      <div class="content-item" v-if="showFaultNumber">
        <span class="label">低效次数：</span>
        <span class="line-3 value"
          >{{ faultCardItem.faultNumber || "--" }} 次</span
        >
      </div>
      <div class="content-item" v-if="showAlarmOpinionContent">
        <span class="label">处理建议：</span>
        <span class="line-3 value" :title="faultCardItem.alarmOpinionContent">{{
          faultCardItem.alarmOpinionContent || "--"
        }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import moment from "moment";

export default {
  name: "FaultCardItem",
  data() {
    return {
      moment,
    };
  },
  props: {
    faultCardItem: PropTypes.object.def({}),
    showAlarmPowerLoss: PropTypes.bool.def(true),
    showAlarmHappenTime: PropTypes.bool.def(true),
    showAlarmOpinionContent: PropTypes.bool.def(true),
    showFaultNumber: PropTypes.bool.def(false),
    isFault: PropTypes.bool.def(true),
  },
};
</script>

<style scoped lang="less">
.fault-card-item {
  width: 100%;
  height: 220px;
  border-radius: 4px;
  background: #164587;

  .header {
    height: 32px;
    width: 100%;
    background: linear-gradient(90deg, #0661ae 0%, rgba(5, 97, 175, 0) 100%);
    padding: 0 16px;
    line-height: 32px;
  }

  .content {
    height: calc(100% - 32px);
    padding: 16px;

    .content-item {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      .label {
        width: 70px;
        color: #85caff;
      }

      .value {
        flex: 1;
      }

      .power-loss {
        color: #ffa56e;
      }

      .line-3 {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
      }
    }
  }
}
</style>
