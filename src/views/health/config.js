export const environmentalTabList = [
  {
    imgSrc: require('@/assets/images/health/pyrotechnicIdentification.png'),
    remark: "烟火识别",
    total: 0,
    addNum: 0,
    remarkName: "82",
    auth: 'pyrotechnicIdentification'
  },
  {
    imgSrc: require('@/assets/images/health/waterStatus.png'),
    remark: "水情识别",
    total: 0,
    addNum: 0,
    remarkName: "105",
    auth:'waterStatus'
  },
  {
    imgSrc: require('@/assets/images/health/perimeterInvasion.png'),
    remark: "周界入侵",
    total: 0,
    addNum: 0,
    remarkName: "84",
    auth: 'perimeterInvasion'
  },
  // {
  //   imgSrc: require('@/assets/images/health/weatherWarning.png'),
  //   remark: "天气预警",
  //   total: 0,
  //   addNum: 0,
  //   remarkName: "85",
  //   auth: 'weatherWarning'
  // },
  // {
  //   imgSrc: require('@/assets/images/health/takeStand.png'),
  //   remark: "站容站貌",
  //   total: 0,
  //   addNum: 0,
  //   remarkName: "86",
  //   auth: 'takeStand'
  // },
];

export const behavioralTabList = [
  {
    imgSrc: require('@/assets/images/health/dailyBehavior.png'),
    remark: "日常行为",
    total: 0,
    addNum: 0,
    remarkName: "87",
    auth:'dailyBehavior'
  },
  {
    imgSrc: require('@/assets/images/health/operationSpecification.png'),
    remark: "作业规范",
    total: 0,
    addNum: 0,
    remarkName: "10000",
    auth:'operationSpecification'
  },
  {
    imgSrc: require('@/assets/images/health/personnelSafety.png'),
    remark: "人员安全",
    total: 0,
    addNum: 0,
    remarkName: "10001",
    auth: 'personnelSafety'
  }
];

export const environmentalDetailBasicList = [
  {
    label: "电站名称",
    key: "psName",
  },
  {
    label: "设备名称",
    key: "deviceName",
  },
  {
    label: "设备位置",
    key: "location",
  },
  {
    label: "告警类型",
    key: "alarmRemarkName",
  },
  {
    label: "告警原因",
    key: "alarmReasonName",
  },
  {
    label: "告警时间",
    key: "happenTime",
  },
  {
    label: "确认时间",
    key: "disappearTime",
  },
]
// 设备健康-诊断详情-基本信息
export const baseInfoList = [
  { label: '电站名称', key: 'psName' },
  { label: '设备类型 ', key: 'deviceTypeName' },
  { label: '设备名称', key: 'deviceName' },
  { label: '厂家名称', key: 'maker' },
  { label: '设备型号', key: 'deviceModel' },
]
// 设备健康-诊断详情-告警信息
export const alarmInfoList = [
  { label: '诊断等级', key: 'alarmGradeName' },
  { label: '诊断类型', key: 'alarmRemarkName' },
  { label: '诊断原因', key: 'alarmReasonName' },
  { label: '最新发生时间', key: 'lastHappenTime' },
  { label: '发生次数', key: 'updateTimes' },
]
// 设备健康-诊断详情-诊断结论及建议
export const conclusionList = [
  { label: '持续时长(h)', key: 'durationTime' },
  { label: '损失电量(kWh)', key: 'powerLoss' },
  { label: '风险提示', key: 'riskWarning' },
  { label: '处理建议', key: 'alarmOpinion', span: 16 },
]
// 设备健康-诊断详情-现场处理
export const dealList = [
  { label: '工单编号', key: 'workCode', slot: 'workCode' },
  { label: '情况描述', key: 'workDesc' },
  { label: '工单闭环时间', key: 'finishedTime' },
  { label: '上传图片', key: 'stepPictureList', type: 'file:picture-card' },
]
// 设备健康-诊断详情-处理结果验证 
export const resultList = [
  { label: '诊断状态', key: 'faultStatusName' },
  { slot: 'disappearTime' },
]
// 设备健康-工单详情-基本信息
export const orderBaseInfoList = [
  { label: '工单分类', key: 'taskCategoryName' },
  { label: '工单类型', key: 'taskTypeName' },
  { label: '工程遗留', key: 'isEngLegacyLabel' },
  { label: '来源分类', key: 'orderSourceName', span: 24 },
  { label: '项目名称', key: 'psaName' },
  { label: '电站名称', key: 'psName' },
  { label: '发生时间', key: 'happenTime' },
  { label: '发现时间', key: 'findTime', span: 24 },
  { label: '故障描述', key: 'taskDescription', span: 24 },
  { label: '处理建议', key: 'handleOpinions', span: 24 },
  { label: '发现人', key: 'findUserName' },
  { label: '预计消除时间', key: 'predictRecoverTime' },
  { label: '负责人', key: 'liablePersonName' },
  { label: '故障图片', key: 'faultFileList', span: 24, type: 'file:picture-card' },
]

// 设备健康-工单详情-设备信息
export const orderDeviceInfoList = [
  { label: '设备类型', key: 'deviceTypeName' },
  { label: '设备名称', key: 'deviceName' },
  { label: '设备编号', key: 'deviceNo' },
  { label: '生产厂家', key: 'maker'},
  { label: '设备型号', key: 'deviceModel' },
  { label: '设备状态', key: 'deviceStatusName' },
  { label: '故障名称', key: 'defectName'},
  { label: '停电范围', key: 'powerCutRangeName'},
  { label: '故障类别', key: 'defectTypeName'},
]

// 设备健康-工单详情-两票信息
export const orderTicketInfoList = [
  { label: '关联两票', key: 'ticketNo', slot: 'ticketNo'  }
]

// 设备健康-工单详情-执行信息
export const orderExecuteInfoList = [
  { label: '现场情况', key: 'sceneConditionName' },
  { label: '故障停运容量(MWp)', key: 'faultCap'},
  { label: '累计损失电量(万kWh)', key: 'totalLossPower' },
  { label: '派发/领取时间', key: 'realStartTime' },
  { label: '执行提交时间', key: 'impCommitTime'},
  { label: '累计修复时间(h)', key: 'totalRepairTime'},
  { label: '备注', key: 'conditionRemark', span: 24 }
]
