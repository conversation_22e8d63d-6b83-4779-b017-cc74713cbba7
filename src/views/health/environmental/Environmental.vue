<template>
  <div class="height-100">
    <a-spin :spinning="loading" class="width-100 height-100">
      <TabList class="margin-b-24" :list="environmentalTabList" @change="handleTabChange" ref="tabList" />
      <div class="table-container" id="drawerDetail">
        <EnvironmentSearch :searchParams="searchParams" @query="handleSearch" @reset="handleReset" :alarmRemark="alarmRemark" />
        <div class="margin-t-16 margin-b-16 divide-line"></div>
        <a-row type="flex" justify="end" class="margin-b-16">
          <DiThrottleButton label="导出" @click="exportExcel" class="di-cancel-btn" :disabled="dataSource.length <= 0" v-has="'environment:export'" />
        </a-row>
        <div class="table-content" :class="dataSource.length ? '' : 'empty-data'">
          <template v-if="dataSource && dataSource.length">
            <div v-for="item in dataSource" :key="item.id" class="card-item" @click="handleOpenDrawer(item)" :style="{ height: ['105', '85'].includes(alarmRemark) ? '478px' : '448px' }">
              <div class="grade-div" :style="{ background: getBgColor(item.alarmGrade) }">
                {{ item.alarmGradeName }}
              </div>
              <div
                class="img-box"
                :style="{
                  borderRadius: item.fileDetail && item.fileDetail.path ? '8px' : '0'
                }"
              >
                <img
                  :src="item.fileDetail && item.fileDetail.path ? item.fileDetail.path : getErrorImg()"
                  :alt="item.alarmRemarkName"
                  class="width-100 height-100"
                  :class="{
                    'error-image': !(item.fileDetail && item.fileDetail.path)
                  }"
                  loading="lazy"
                />
              </div>
              <div class="content-box">
                <div class="info-item">
                  <span v-if="alarmRemark == '85'" class="text-ellipsis">
                    <svg-icon icon-class="health-cloud" class="font-size-20 margin-r-4" />预警名称：{{ item.weather ? item.weather.name + item.weather.level : '--' }}</span
                  >
                  <span v-else-if="alarmRemark == '105'" class="text-ellipsis"> <svg-icon icon-class="health-dashboard" class="font-size-20 margin-r-4" />监测点位：金阳电站16号方阵水位监测点</span>
                  <span v-else class="text-ellipsis"> <svg-icon icon-class="2d-alarm-monitor" class="font-size-20 margin-r-4" />设备名称：{{ getLabel(item.deviceName, null) }}</span>
                  <span v-if="alarmRemark == '85'">{{ item.alarmGradeName }}</span>
                </div>
<!--                <div class="info-item" v-if="alarmRemark === '105'">-->
<!--                  <span class="text-ellipsis"> <svg-icon icon-class="water-level" class="font-size-20 margin-r-4" />水位高度：{{ getLabel(item.waterLevel, null) }}m </span>-->
<!--                </div>-->
                <div class="info-item">
                  <span v-if="alarmRemark == '85'" class="flex-start">
                    <svg-icon icon-class="2d-alarm-warning" class="font-size-20 margin-r-4" style="position: absolute" />
                    <span class="grade-reason">
                      <a-tooltip :title="item.weather && item.weather.content ? getLabel(item.weather && item.weather.content, null) : null">
                        预警内容：{{ getLabel(item.weather && item.weather.content, null) }}
                      </a-tooltip>
                    </span>
                  </span>
                  <span v-else class="text-ellipsis"> <svg-icon icon-class="2d-alarm-warning" class="font-size-20 margin-r-4" />诊断原因：{{ getLabel(item.alarmReasonName, null) }} </span>
                </div>
                <div class="info-item">
                  <span class="flex-start">
                    <svg-icon icon-class="2d-alarm-time" class="font-size-20 margin-r-4" />
                    发生时间：{{ getLabel(item.happenTime, null) }}
                  </span>
                </div>
                <div class="info-item" v-if="item.handleStatus == '03'">
                  <span class="flex-start">
                    <svg-icon icon-class="circle-completed" class="font-size-20 margin-r-4" />
                    确认时间：{{ getLabel(item.disappearTime, null) }}
                  </span>
                </div>
                <div class="operations flex-center">
                  <DiThrottleButton
                    :label="item.handleStatus == '03' ? '已确认' : '确认'"
                    @click="(e) => handleDistributed(e, item)"
                    :class="{ 'di-cancel-btn': item.handleStatus == '03' }"
                    :disabled="item.handleStatus == '03'"
                    v-has="'environment:confirm'"
                  />
<!--                  <DiThrottleButton v-if="showFireControl(item)" label="消音" @click.stop="handleControl(item, 0)" />-->
<!--                  <DiThrottleButton v-if="showFireControl(item)" label="启动消防" @click.stop="handleControl(item, 1)" />-->
                  <DiThrottleButton v-if="!['105', '85'].includes(alarmRemark)" label="详情" class="di-cancel-btn" v-has="'environment:detail'" />
                  <DiThrottleButton v-if="item.handleStatus == '03'" label="删除" class="di-cancel-btn" v-has="'environment:delete'" @click.stop="handleDelete(item)" />
                </div>
              </div>
              <img class="card-item-vignette" src="@/assets/images/vignette.png" alt="晕影图片" />
            </div>
          </template>
          <div v-else class="no-data"></div>
        </div>
        <div class="table-vignette"></div>
        <page-pagination :pageSize="pageData.size" :current="pageData.curPage" :total="total" @size-change="pageChangeEvent" />
      </div>
    </a-spin>
    <DrawerView ref="drawerView" @cancel="handleSearch" @close="drawerClose" />
  </div>
</template>

<script>
import EnvironmentSearch from '../modules/SearchCommon';
import { environmentalTabList } from '../config';
import {
  dataStatisticalSumForSensorApi,
  dataStatisticalRateForSensorApi,
  listForSensorApi,
  affirmForSensorApi,
  exportForSensorApi,
  deleteAlarmEvent
  // getRemarkAndReasonGradeForSensorApi
} from '@/api/health/AlarmEvents';
import { PSA_INFO } from '@/store/mutation-types';
import { cloneDeep } from 'lodash';
import { thirdDeviceCtrlApi } from '@/api/health/healthapi';

export default {
  name: 'Environmental',
  components: { EnvironmentSearch },
  data() {
    return {
      environmentalTabList,
      total: 0,
      dataSource: [],
      pageData: {
        size: 10,
        curPage: 1
      },
      searchParams: {
        dateRange: undefined, //时间区间
        handleStatus: undefined, // 处理状态
        alarmStatus: '01',
        alarmReason: ''
      },
      treePsId: this.$ls.get(PSA_INFO).psId,
      loading: false,
      alarmRemark: null,
      comeFromAlarm: false
    };
  },
  activated() {
    this.dealRouterEvent();
  },
  created() {
    this.dealRouterEvent();
  },
  mounted() {
    // 查询总数、进入新增
    // this.initQuery();
  },
  methods: {
    // 处理告警列表、诊断概览跳转过来的情况
    dealRouterEvent() {
      if (window.comeFromAlarm) {
        window.comeFromAlarm = false;
        this.comeFromAlarm = true;
        let params = this.$route.params;
        this.setRouteData(params);
        this.$nextTick(() => {
          let data = {
            handleStatus: params.handleStatus,
            id: params.id,
            comeFromAlarm: true
          };
          this.$refs.drawerView.init('3', data, '/health/modules/DetailForm');
        });
      } else if (window.comeFromOverview) {
        let params = this.$route.params;
        this.setRouteData(params);
        this.initQuery();
        this.queryList();
      }
    },
    // 处理路由跳转数据，设置查询条件
    setRouteData(params) {
      Object.assign(this.searchParams, {
        ...this.$options.data.call(this).searchParams,
        handleStatus: params.handleStatus,
        alarmReason: params.alarmReason
      });
      let index = this.environmentalTabList.findIndex((item) => {
        return item.remark == params.tabName;
      });
      this.alarmRemark = this.environmentalTabList[index].remarkName;
      this.$nextTick(() => {
        this.$refs.tabList.activeKey = index;
      });
    },
    // 初始化查询
    async initQuery() {
      const { environmentalTabList, treePsId } = this;
      const alarmRemarkList = environmentalTabList.map((item) => item.remarkName);
      const params = this.formatParams();
      const [sumData, rateData] = await Promise.allSettled([
        dataStatisticalSumForSensorApi({
          alarmRemarkList,
          treePsId,
          ...params
        }),
        dataStatisticalRateForSensorApi({
          alarmRemarkList,
          treePsId,
          ...params
        })
      ]);
      environmentalTabList.forEach((item) => {
        item.total = sumData.value.result_data[item.remarkName];
        const findItem = rateData.value.result_data.find((o) => o.tabCode == item.remarkName);
        if (findItem) item.addNum = findItem.sumToday;
      });
      // console.log('environmentalTabList',environmentalTabList)
    },
    // 确认逻辑
    async handleDistributed(e, item) {
      e.stopPropagation();
      this.$confirm({
        title: '是否确认',
        // content: '是否确认?',
        centered: true,
        onOk: async () => {
          await affirmForSensorApi({ id: item.id, handleStatus: '03' });
          this.$message.success('操作成功');
          await this.handleSearch();
        }
      });
    },
    // 分页逻辑
    pageChangeEvent(curPage, size) {
      this.pageData.curPage = curPage;
      this.pageData.size = size;
      this.queryList();
    },
    // 查询列表逻辑
    async queryList() {
      this.loading = true;
      const { treePsId, pageData, alarmRemark } = this;
      const params = this.formatParams();
      const res = await listForSensorApi(Object.assign({ treePsId, alarmRemarkList: [alarmRemark] }, params, pageData));
      window.comeFromOverview = false;
      this.loading = false;
      const { pageList, rowCount } = res.result_data;
      this.dataSource = pageList;
      this.total = rowCount;
    },
    // 重置逻辑
    handleReset() {
      this.searchParams = this.$options.data.call(this).searchParams;
      this.queryList();
      this.initQuery();
    },
    // tab切换逻辑
    handleTabChange(index, currentTab) {
      if (this.comeFromAlarm || window.comeFromOverview) {
        return;
      }
      // 重置数据
      this.dataSource = [];
      this.alarmRemark = currentTab.remarkName;
      this.pageData.curPage = 1;
      this.searchParams.alarmReason = '';
      // 关闭抽屉
      this.$nextTick(() => {
        this.$refs.drawerView.visible = false;
        this.queryList();
        this.initQuery();
      });
    },
    // 打开抽屉查看详情
    handleOpenDrawer(item) {
      // 水情识别、天气预警不需要展示详情
      if (['105', '85'].includes(item.alarmRemark)) return;
      this.$refs.drawerView.init('3', item, '/health/modules/DetailForm');
    },
    //   查询
    handleSearch() {
      if (this.comeFromAlarm) {
        this.comeFromAlarm = false;
      }
      Object.assign(this.pageData, { curPage: 1 });
      this.initQuery();
      this.queryList();
    },
    //   format入参
    formatParams() {
      const params = cloneDeep(this.searchParams);
      if (!this.$isEmpty(params.dateRange)) {
        const [startTime, endTime] = params.dateRange;
        params.startTime = startTime;
        params.endTime = endTime;
        delete params.dateRange;
      }
      return params;
    },
    drawerClose() {
      if (this.comeFromAlarm) {
        this.comeFromAlarm = false;
        this.handleSearch();
      }
    },
    getErrorImg() {
      let name = 'error-image';
      if (this.alarmRemark == '105') {
        name = 'water-image';
      } else if (this.alarmRemark == '85') {
        name = 'weather-image';
      }
      return require(`@/assets/images/${name}.png`);
    },
    // 导出excel
    exportExcel() {
      const { treePsId, pageData, alarmRemark } = this;
      const params = this.formatParams();
      this.loading = true;
      exportForSensorApi(Object.assign({ treePsId, alarmRemarkList: [alarmRemark] }, params, pageData))
        .then((res) => {
          this.$downloadFile({
            fileBase64Code: res.result_data.strBase64,
            fileName: res.result_data.fileName
          });
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 根据告警类型获取背景色
    getBgColor(grade) {
      switch (grade) {
        case '1':
          return 'linear-gradient(180deg, #F07072 0%, #8C1B1D 100%)';
        case '2':
          return 'linear-gradient(180deg, #FFA56E 0%, #B6561B 100%)';
        case '3':
          return 'linear-gradient(180deg, #BB6EFF 0%, #591BB6 100%)';
        case '4':
          return 'linear-gradient(180deg, #B9CCD3 0%, #59737C 100%)';
      }
    },
    // 告警删除
    handleDelete(item) {
      this.$confirm({
        title: '是否删除',
        centered: true,
        onOk: async () => {
          await deleteAlarmEvent({ id: item.id });
          this.$message.success('操作成功');
          await this.handleSearch();
        }
      });
    },
    showFireControl(item) {
      const extend = JSON.parse(item.extend || '{}');
      return extend?.isNeedOperate == 1 && item.handleStatus == '01';
    },
    handleControl(item, controlType) {
      const content = controlType == 1 ? '确定要开启消防灭火设备吗？' : '确定要消除本次报警声音吗？';
      this.$confirm({
        title: '操作确认',
        content: content,
        centered: true,
        onOk: async () => {
          await thirdDeviceCtrlApi({
            controlType,
            psKey: item.psKey
          }).catch((err) => {
            this.$message.error('操作失败');
            return;
          });
          this.$message.success('操作成功');
          await this.handleSearch();
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
@import '../less/CardCommon.less';

//.table-container .table-content .card-item {
//  height: 448px;
//}
</style>
