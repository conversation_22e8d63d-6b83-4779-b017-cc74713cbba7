<template>
  <a-spin class="width-height-100" :spinning="loading">
    <iframe
        :id="Math.random()"
        :src="url"
        ref="framePage"
        frameborder="0"
        width="100%"
        scrolling="auto"
        :height="frameHeight"
        @load="sendBaseInfo()"
        v-show="!loading"
        :style="frameStyle"
    ></iframe>
  </a-spin>
</template>

<script>
import {ACCESS_TOKEN, HAS_ALL_DATA_HEALTH, USER_INFO,} from "@/store/mutation-types";
const VUE_APP_API_DOMAIN = process.env.VUE_APP_API_DOMAIN;

export default {
  name: "SystemConfigIframe",
  data() {
    return {
      frameHeight: window.innerHeight - 20,
      iframeWin: null,
      loading: true,
      url: VUE_APP_API_DOMAIN + "/YmFzaWN2ZXJ0aW9u/#/systemConfiguration",
      frameStyle: {},
      frameTimer: null,
    };
  },
  watch: {
    $route: {
      immediate: true,
      handler() {
        this.locationRoute();
      },
    },
  },
  mounted() {
    this.iframeWin = this.$refs.framePage.contentWindow;
    this.getFrameHeight();
    window.addEventListener("resize", this.getFrameHeight);
    window.addEventListener("message", (event) => {
      const { cmd, params } = event.data ?? {};
      if (cmd === "changeWeather") {
        window.g.event.fire("change_weather", params?.weatherSwitch);
      }
      if (cmd === "updateLoading") {
        this.loading = params.loading;
      }
    });
    this.$once("hook:beforeDestroy", () => {
      this.cleanup()
      window.removeEventListener("resize", this.getFrameHeight);
    });
  },
  deactivated() {
    window.removeEventListener("resize", this.getFrameHeight);
  },
  methods: {
    sendBaseInfo() {
      this.iframeWin.postMessage(
          {
            cmd: "baseInfo",
            params: {
              token: this.$ls.get(ACCESS_TOKEN),
              userInfo: this.$ls.get(USER_INFO),
              device: this.$store.getters.device,
              color: this.primaryColor,
              hasAllData: this.$ls.get(HAS_ALL_DATA_HEALTH),
              deviceId: localStorage.getItem("deviceId"),
            },
          },
          "*"
      );
    },
    getFrameHeight() {
      const isFullScreen = !!document["fullscreenElement"];
      this.frameStyle = {
        "margin-top": isFullScreen ? "10px" : 0,
      };
      this.frameHeight = window.innerHeight - (isFullScreen ? 30 : 20);
    },
    locationRoute() {
      this.cleanup()
      try {
        this.iframeWin.postMessage(
            {
              cmd: "locationRedirect",
              params: { redirectUrl: this.url },
            },
            "*"
        );
      } catch (err){
        this.frameTimer = setTimeout(this.locationRoute, 500)
      }
    },
    cleanup() {
      if (this.frameTimer) {
        clearTimeout(this.frameTimer);
        this.frameTimer = null;
      }
    }
  },
};
</script>

<style scoped lang="less"></style>
