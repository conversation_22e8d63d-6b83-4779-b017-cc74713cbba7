
export const AlarmMixins = {
    data(){
        return {
           alarmList:[],
           deviceIconSize:{
               iconSize: [28, 44], // 大小
               iconAnchor: [14, 44] // icon图标点位置偏移
           },
           deviceIconSizeHover:{
               iconSize: [40, 58], // 大小
               iconAnchor: [20, 58] // icon图标点位置偏移
           },
           deviceIconAlarmSize:{
               iconSize: [48, 55], // 大小
               iconAnchor: [24, 55] // icon图标点位置偏移
           },
           deviceIconAlarmSizeHover:{
               iconSize: [60, 70], // 大小
               iconAnchor: [30, 70] // icon图标点位置偏移
           },
            monitorIconAlarmSize:{
                iconSize:[30,78],
                iconAnchor:[15,78]
            },
            monitorIconAlarmSizeHover:{
                iconSize:[64,93],
                iconAnchor:[32,93]
            },
            monitorIconSize:{ // 摄像头icon size
                //iconSize:[30,78],
                iconSize:{
                    width: 30,
                    height: 78
                },
                iconAnchor:[15,78]
            },
            monitorIconSizeHover:{ // 摄像头icon size
               // iconSize:[64,93],
                  iconSize:{
                    width: 64,
                    height: 93
                },
                iconAnchor:[32,93]
            },
            drawIconDeviceType:[
                '54','4','1','6-4','10'
            ],
            drawRectangleDeviceType:[
                '58'
            ],
            alarmHalfMaxZoom:15, // 地图缩放层级教浅时 告警设备图标缩小 缩小边界层级
            alarm110kVs:[],
            rectangleMarkers:[]
        }
    },
    methods:{

        getIconSize(isHover,isError,isMonitor = false){
            if(isMonitor){
                if(isHover){
                    return isError ? this.monitorIconAlarmSizeHover : this.monitorIconSizeHover
                } else {
                    return isError ? this.monitorIconAlarmSize : this.monitorIconSize
                }
            }
            if(isHover){
                return isError ? this.deviceIconAlarmSizeHover : this.deviceIconSizeHover
            } else {
                return isError ? this.deviceIconAlarmSize : this.deviceIconSize
            }
        },
        getAlarmIconSizeByZoom(sizeObj){
            return this.mapZoom > this.alarmHalfMaxZoom ? sizeObj : this.getIconSizeHalf(sizeObj)
        },
        getIconSizeHalf(sizeObj){
            let scale = 2
            return {
                iconSize:[sizeObj.iconSize[0]/scale,sizeObj.iconSize[1]/scale,],
                iconAnchor:[sizeObj.iconAnchor[0]/scale,sizeObj.iconAnchor[1]/scale]
            }
        },
        setEventAlarm(marker,iconSize,hoverIconSize,isAlarm = true,isAddToMap = true){
            marker.setIcon(L.icon({
                ...iconSize,
                iconUrl:this.getIcon(marker.deviceType,false,isAlarm),
            }))
            marker.on('mouseover', e=> {
                marker.setIcon(L.icon({
                    ...hoverIconSize,
                    iconUrl:this.getIcon(marker.deviceType,true,isAlarm),
                }))
            })
            marker.on('mouseout', e=> {
                marker.setIcon(L.icon({
                    ...iconSize,
                    iconUrl:this.getIcon(marker.deviceType,false,isAlarm),
                }))
            })
            if(isAddToMap){
                marker.addTo(this.map)
            }
        },

    }
}