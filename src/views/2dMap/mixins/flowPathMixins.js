import { AntPath, antPath } from 'leaflet-ant-path';
import { flowPaths } from "../constants";

export const FlowPathMixins = {
    methods:{
        renderFlowPath(){
            flowPaths.map(item => {
                const path = new AntPath(item, {
                    "delay": 700,
                    "dashArray": [
                        7,
                        14
                    ],
                    "weight": 2,
                    opacity:1,
                    "color": "#0079EE",
                    "pulseColor": "#00D6EE",
                    "paused": false,
                    "reverse": false,
                    "hardwareAccelerated": false
                })
                path.addTo(this.map)
            })
        }
    }
}
