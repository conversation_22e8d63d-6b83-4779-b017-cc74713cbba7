import { getModelData } from "../../../api/2dMap/psOverview";

export const MonitorMixins = {
    data(){
        return {
            monitorList:[], // 监控器数据
            monitorLayers:[], // 监控器图层
        }
    },
    methods:{
        async initMonitorData(){
            this.monitorLayers = L.layerGroup()

            let res = await getModelData({
                psId:this.psaInfo.psId,
                modelName:'cameraList',
            })
            this.monitorList = res.result_data.dataResult.cameraInfo

            this.monitorList.map(item=>{

                let position = {longitude: 117.5569067895412+ Math.random() * 0.01, latitude: 31.936283550745518 + Math.random() * 0.01}
                if(item.coordinate){
                    let arr = item.coordinate.split(',')
                    position.longitude = arr[0]
                    position.latitude = arr[1]
                }
                let icon = this.getMonitorIcon({
                    isHover:false,
                    isError:false,
                    isOffline:item.onlineStatus != 1,
                })
                let marker = L.marker([position.latitude ,position.longitude],{icon})
                marker.isOffline = item.onlineStatus != 1
                marker.lat = position.latitude
                marker.lng = position.longitude
                marker.name = item.deviceName
                marker.id = item.uuid
                marker.deviceImg = item.fileUrlThumbnail
                Object.assign(marker,item)
                marker.deviceType = '54'
                this.setMonitorMarkerHoverEvents(marker)
                marker.on('click', e => {
                    this.$emit('deviceMarkerClick', e, marker);
                })
                marker.addTo(this.monitorLayers)
            })
        },
        addMonitorLayers(){
            this.monitorLayers && this.monitorLayers.getLayers().map(item => {
                if(!item.isError){
                    item.addTo(this.map)
                }
            })
        },

        // 设置监控器marker绑定事件
        setMonitorMarkerHoverEvents(marker){
            marker.on('mouseover', e=> {
                marker.setIcon(this.getMonitorIcon({
                    isHover:true,
                    isError:marker.isError,
                    isOffline:marker.isOffline,
                }))
                this.setTempNameMarker(true, marker,  (this.mapZoom > this.alarmHalfMaxZoom)? 'camera' : 'camera-half')
            })
            marker.on('mouseout', e=> {
                this.setTempNameMarker(false);
                marker.setIcon(this.getMonitorIcon({
                    isHover:false,
                    isError:marker.isError,
                    isOffline:marker.isOffline,
                }))
            })
        },
        // 根据配置获取leaflet 监控器的icon对象
        getMonitorIcon(config){
            let iconSize = config.isHover ? this.monitorIconSizeHover : this.monitorIconSize
            let iconUrl = this.getIcon('54',config.isHover,config.isError,(config.isError ? false : config.isOffline))
            if(this.mapZoom <= this.alarmHalfMaxZoom ){
                iconSize = this.getIconSizeHalf(iconSize)
            }
            return L.icon({
                ...iconSize,
                iconUrl
            })
        },
        removeMonitorLayers(){
            this.monitorLayers && this.monitorLayers.getLayers().map(item => {
                if(!item.isError){
                    item.remove(this.map)
                }
            })
        },

    }
}