// 地图故障告警轮播逻辑

import {getModelData} from "../../../api/2dMap/psOverview";
const cyclicInterval = 5 * 1000 // 轮播时间间隔 ms

export const AlarmCyclicMixins = {
    data(){
        return {
            nowAlarmCyclicLayer:null, //轮播图层
            nowAlarmCyclicLayerSize:{
                iconSize: [320,257],
                iconAnchor: [160, 257],
            }, //轮播图层
            cyclicAlarmTimer:null, //轮播定时器
            nowAlarmCyclicIndex:0, // 当前轮播index
            cyclicAlarmLayerZIndex:99, // 告警轮播地图上层级
        }
    },
    methods:{
        // 获取轮播数据
        async getMapAlarmCyclicData(){
            this.nowAlarmCyclicLayer && this.nowAlarmCyclicLayer.remove(this.map)
            this.mapAlarmList = []
            let res = await getModelData({
                psId:this.psaInfo.psId,
                modelName:'realtimeAlert',
                operateType:1,
            })

            if(res.result_data.length == 0){
                return
            }

            // 如果接口请求完 页面已经不在电站概览tab时 则不添加告警轮播
            if(!this.isInPsOverview){
                return
            }
            // 筛选非光功率轮播数据
            this.mapAlarmList = res.result_data.filter(item=>item.needPlay === 1) || [];

            let position = this.getPositionByPsKey(this.mapAlarmList[this.nowAlarmCyclicIndex].psKey)
            let icon = this.getMapAlarmCyclicDivIcon(this.mapAlarmList[this.nowAlarmCyclicIndex])
            let positionCenter = this.getCenter(position)
            this.nowAlarmCyclicLayer = L.marker([positionCenter.latitude, positionCenter.longitude], {icon})
            this.movePositionToCenter([positionCenter],16)
            this.nowAlarmCyclicLayer.addTo(this.map)
            this.nowAlarmCyclicLayer.setZIndexOffset(this.cyclicAlarmLayerZIndex)
            this.nowAlarmCyclicLayer.on('click', e=> {
                this.$emit('openRealtimeWindow',this.mapAlarmList[this.nowAlarmIndex])
            })
            this.startCyclic()

        },

        getMapAlarmCyclicDivIcon(alarmData){
            let html = `<div class="map-alarm-cyclic flex-start flex-column">
                      <div class="map-alarm-window relative cursor-pointer alarm-window-grade${alarmData.alarmGrade}">
                        <div class="width-100 alarm-map-img margin-b-8 relative" style="z-index: 1">
                          <img class="alarm-map-img" src="${alarmData.fileUrlThumbnail}" />
                          <div class="img-label flex-start">
                            <span class="font-12 color-text-main margin-l-12"> ${ alarmData.alarmReason } </span>
                          </div>
                        </div>
                        <span class="font-14 num-font-500 color-text-white relative" style="z-index: 1">告警时间：${ alarmData.alarmTime }</span>
                        <div class="alarm-window-bg window-bg${alarmData.alarmGrade}">
                        </div>
                      </div>
                      <div class="width-100 flex-center">
                        <div class="map-alarm-icon alarm-icon-grade${alarmData.alarmGrade}"/>
                      </div>
                    </div>`
            return L.divIcon({
                html: html
            })
        },

        // 开始轮播
        startCyclic(){
            this.nowAlarmIndex = 0
            clearInterval(this.cyclicAlarmTimer)
            this.cyclicAlarmTimer = null
            this.cyclicAlarmTimer = setInterval(()=>{
                if(this.nowAlarmIndex == this.mapAlarmList.length - 1){
                    this.nowAlarmIndex = 0
                } else {
                    this.nowAlarmIndex += 1
                }
                let icon = this.getMapAlarmCyclicDivIcon(this.mapAlarmList[this.nowAlarmIndex])
                this.nowAlarmCyclicLayer.setIcon(icon)
                let position = this.getPositionByPsKey(this.mapAlarmList[this.nowAlarmIndex].psKey)
                this.nowAlarmCyclicLayer.setLatLng([position[0].latitude,position[0].longitude]);
                this.movePositionToCenter({id:'point1'},2)
            },cyclicInterval)
        },

        // 终止轮播
        stopCyclic(){
            clearInterval(this.cyclicAlarmTimer)
            this.cyclicAlarmTimer = null
            this.nowAlarmCyclicLayer && this.nowAlarmCyclicLayer.remove(this.map)
            this.mapAlarmList = []
        },

    },
    beforeDestroy() {
        clearInterval(this.cyclicAlarmTimer)
        this.cyclicAlarmTimer = null
    },
}