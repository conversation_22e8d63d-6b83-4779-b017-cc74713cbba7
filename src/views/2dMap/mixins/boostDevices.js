export const boostDevices = [
  {
    name: '综合楼',
    psKey: '',
    index: 0,
    positions: [
      { latitude: 31.955066664056183, longitude: 117.57101253690026 },
      { latitude: 31.955225426626473, longitude: 117.57101253690026 },
      { latitude: 31.955066664056183, longitude: 117.57128751382554 },
      { latitude: 31.955225426626473, longitude: 117.57128751382554 },
    ]
  },
  {
    name: '二次设备室',
    psKey: '',
    index: 1,
    roomType: '3',
    positions: [
      { latitude: 31.955070080108776, longitude: 117.57102198818944 },
      { latitude: 31.95514718525944, longitude: 117.57102198818944 },
      { latitude: 31.955070080108776, longitude: 117.57115243456012 },
      { latitude: 31.95514718525944, longitude: 117.57115243456012 },
    ]
  },
  {
    name: '库房',
    psKey: '',
    index: 2,
    positions: [
      { latitude: 31.955132103858556, longitude: 117.57141594816989 },
      { latitude: 31.955208070875003, longitude: 117.57141594816989},
      { latitude: 31.955132103858556, longitude: 117.57148603375207 },
      { latitude: 31.955208070875003, longitude: 117.57148603375207 },
    ]
  },
  {
    name: '配电楼',
    psKey: '',
    index: 3,
    roomType: '2',
    positions: [
      { latitude: 31.95512385805526, longitude: 117.57159434937324 },
      { latitude: 31.955209498777368, longitude: 117.57159434937324},
      { latitude: 31.95512385805526, longitude: 117.57191359087673 },
      { latitude: 31.955209498777368, longitude: 117.57191359087673 },
    ]
  },
  {
    name: '接地变(兼站用变)',
    psKey: '107353_6_10004773_8',
    index: 4,
    positions: [
      { latitude: 31.954991265039094, longitude: 117.57159099501075 },
      { latitude: 31.9550182945505, longitude: 117.57159099501075},
      { latitude: 31.954991265039094, longitude: 117.57163257688724 },
      { latitude: 31.9550182945505, longitude: 117.57163257688724 },
    ]
  },
  {
    name: '1#主变',
    psKey: '107353_6_26_1',
    index: 5,
    positions: [
      { latitude: 31.954971348551883, longitude: 117.57169729706601 },
      { latitude: 31.955029390873946, longitude: 117.57169729706601},
      { latitude: 31.954971348551883, longitude: 117.57178783824871 },
      { latitude: 31.955029390873946, longitude: 117.57178783824871 },
    ]
  },
  {
    name: '备用变',
    psKey: '',
    index: 6,
    positions: [
      { latitude: 31.954959967700134, longitude: 117.57185557646686 },
      { latitude: 31.954980168711042, longitude: 117.57185557646686},
      { latitude: 31.954959967700134, longitude: 117.57188978091365 },
      { latitude: 31.954980168711042, longitude: 117.57188978091365 },
    ]
  },
  {
    name: 'SVG',
    // psKey: '107353_29_10000491_1',
    psKey: '107353_29_27_1',
    index: 7,
    positions: [
      { latitude: 31.95490960741416, longitude: 117.5718438396469 },
      { latitude: 31.954934360778502, longitude: 117.5718438396469},
      { latitude: 31.95490960741416, longitude: 117.57191794928161 },
      { latitude: 31.954934360778502, longitude: 117.57191794928161 },
    ]
  },
  {
    name: 'SVG连接变压器',
    psKey: '',
    index: 8,
    positions: [
      { latitude: 31.954820552151592, longitude: 117.57184350430919 },
      { latitude: 31.95487660291797, longitude: 117.57184350430919 },
      { latitude: 31.954820552151592, longitude: 117.57189715834336 },
      { latitude: 31.95487660291797, longitude: 117.57189715834336 },
    ]
  },
  {
    name: '5071刀闸',
    psKey: '107353_12_28_2',
    belong110kVPointKey:'700344',
    index: 9,
    positions: [
      { latitude: 31.95494408765647, longitude: 117.57171657475983 },
      { latitude: 31.954949778083517, longitude: 117.57171657475983 },
      { latitude: 31.95494408765647, longitude: 117.57176820238169 },
      { latitude: 31.954949778083517, longitude: 117.57176820238169 },
    ],
    baseData: {
      deviceName: '5071刀闸',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '50710接地刀闸',
    psKey: '107353_12_28_4',
    belong110kVPointKey:'700346',
    index: 10,
    positions: [
      { latitude: 31.95493697462218, longitude: 117.57171791573704 },
      { latitude: 31.954943518613742, longitude: 117.57171791573704 },
      { latitude: 31.95493697462218, longitude: 117.57176820238169 },
      { latitude: 31.954943518613742, longitude: 117.57176820238169 },
    ],
    baseData: {
      deviceName: '50710接地刀闸',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '507断路器',
    psKey: '107353_12_28_9',
    belong110kVPointKey:'12241',
    index: 11,
    positions: [
      { latitude: 31.95491134598089, longitude: 117.57171971523856 },
      { latitude: 31.954922442317216, longitude: 117.57171971523856 },
      { latitude: 31.95491134598089, longitude: 117.57176431515445 },
      { latitude: 31.954922442317216, longitude: 117.57176431515445 },
    ],
    baseData: {
      deviceName: '507断路器',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '110kV线路电流互感器',
    psKey: '107353_12_28_11',
    index: 12,
    positions: [
      { latitude: 31.954885739045697, longitude: 117.57171166713341 },
      { latitude: 31.954895128256087, longitude: 117.57171166713341 },
      { latitude: 31.954885739045697, longitude: 117.57177236325961 },
      { latitude: 31.954895128256087, longitude: 117.57177236325961 },
    ]
  },
  {
    name: '50720接地刀闸',
    psKey: '107353_12_28_5',
    belong110kVPointKey:'700347',
    index: 13,
    positions: [
      { latitude: 31.95487068111603, longitude: 117.571708864141 },
      { latitude: 31.954877794155465, longitude: 117.571708864141 },
      { latitude: 31.95487068111603, longitude: 117.57176954335885 },
      { latitude: 31.954877794155465, longitude: 117.57176954335885 },
    ],
    baseData: {
      deviceName: '50720接地刀闸',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '5070接地刀闸',
    psKey: '107353_12_28_6',
    belong110kVPointKey:'700348',
    index: 14,
    positions: [
      { latitude: 31.954859015730182, longitude: 117.5717075231638 },
      { latitude: 31.954864137119277, longitude: 117.5717075231638 },
      { latitude: 31.954859015730182, longitude: 117.57177054909177 },
      { latitude: 31.954864137119277, longitude: 117.57177054909177 },
    ],
    baseData: {
      deviceName: '5070接地刀闸',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '5072刀闸',
    psKey: '107353_12_28_3',
    belong110kVPointKey:'700345',
    index: 15,
    positions: [
      { latitude: 31.954864137119277, longitude: 117.57170718791953 },
      { latitude: 31.95487011207284, longitude: 117.57170718791953 },
      { latitude: 31.954864137119277, longitude: 117.57176920811455 },
      { latitude: 31.95487011207284, longitude: 117.57176920811455 },
    ],
    baseData: {
      deviceName: '5072刀闸',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '110kV线路电压互感器',
    psKey: '107353_12_28_7',
    index: 16,
    positions: [
      { latitude: 31.95484075319316, longitude: 117.5717120519254 },
      { latitude: 31.954849288843203, longitude: 117.5717120519254 },
      { latitude: 31.95484075319316, longitude: 117.57177006534987 },
      { latitude: 31.954849288843203, longitude: 117.57177006534987 },
    ],
    baseData: {
      deviceName: '110kV线路电压互感器',
      deviceModel: 'GW4-126DD/1250',
      maker: '山东泰开隔离开关有限公司'
    }
  },
  {
    name: '110kV线路避雷器',
    psKey: '107353_12_28_8',
    index: 17,
    positions: [
      { latitude: 31.954817706934115, longitude: 117.5717120519254 },
      { latitude: 31.95482766519492, longitude: 117.5717120519254 },
      { latitude: 31.954817706934115, longitude: 117.57177140670069 },
      { latitude: 31.95482766519492, longitude: 117.57177140670069 },
    ]
  },
  {
    name: '气象站',
    psKey: '107353_5_10003725_1',
    index: 18,
    positions: [
      {
        "longitude": 117.57159454685406,
        "latitude": 31.95512328191312
      },
      {
        "longitude": 117.57159454685406,
        "latitude": 31.955151164947875
      },
      {
        "longitude": 117.57161633773339,
        "latitude": 31.95512328191312
      },
      {
        "longitude": 117.57161633773339,
        "latitude": 31.955151164947875
      }
    ]
  },
  {
    name: '110kV线路',
    psKey: '107353_12_28_1',
    index: 19,
    positions: [
      {
        longitude: 117.57171053546868,
        latitude: 31.954783903028254,
      },
      {
        longitude: 117.57171053546868,
        latitude: 31.95481207042106,
      },
      {
        longitude: 117.57176920294148,
        latitude: 31.954783903028254,
      },
      {
        longitude: 117.57176920294148,
        latitude: 31.95481207042106,
      }
    ]
  }
]

export const boosterRoomDevice ={
  devicesHighVoltage:[
    {
      label:'35kV母线电压互感器3015隔离开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV母线电压互感器 3015隔离开关柜.png'),
      psKey:'107353_30_13_1',
    },{
      label:'35kV SVG进线312开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV SVG进线 312开关柜.png'),
      psKey:'',
    },{
      label:'35kV 接地变303开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 接地变 303开关柜.png'),
      psKey:'',
    },{
      label:'35kV 主变低压侧301开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 主变低压侧301开关柜.png'),
      psKey:'',
      shadowPsKey:'107353_9999_1_3'
    },{
      label:'35kV #6集电线路311开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 6集电线路311开关柜.png'),
      psKey:'107353_12_10_1',
    },{
      label:'35kV #5集电线路309开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 5集电线路309开关柜.png'),
      psKey:'107353_12_9_1',
    },{
      label:'35kV #4集电线路308开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 4集电线路308开关柜.png'),
      psKey:'107353_12_8_1',
    },{
      label:'35kV #3集电线路307开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 3集电线路307开关柜.png'),
      psKey:'107353_12_7_1',
    },{
      label:'35kV #2集电线路306开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 2集电线路306开关柜.png'),
      psKey:'107353_12_6_1',
    },{
      label:'35kV #1集电线路305开关柜',
      src:require('@/assets/images/2dMap/boosterRoom/deviceHighVoltage/35kV 1集电线路305开关柜.png'),
      psKey:'107353_12_5_1',
    },
  ],
  devicesLowVoltage:[
    {
      label:'440V出线柜1',
      psKey:'',
      shadowPsKey:'107353_9999_1_27',
      src:require('@/assets/images/2dMap/boosterRoom/deviceLowVoltage/440V出线柜1.png'),
    },{
      label:'440V出线柜2',
      psKey:'',
      shadowPsKey:'107353_9999_1_28',
      src:require('@/assets/images/2dMap/boosterRoom/deviceLowVoltage/440V出线柜2.png'),
    },{
      label:'440V出线柜3',
      psKey:'',
      shadowPsKey:'107353_9999_1_29',
      src:require('@/assets/images/2dMap/boosterRoom/deviceLowVoltage/440V出线柜3.png'),
    },{
      label:'440V出线柜4',
      psKey:'',
      shadowPsKey:'107353_9999_1_30',
      src:require('@/assets/images/2dMap/boosterRoom/deviceLowVoltage/440V出线柜4.png'),
    },{
      label:'400V双电源进线柜',
      psKey:'',
      src:require('@/assets/images/2dMap/boosterRoom/deviceLowVoltage/400V双电源进线柜.png'),
    },
  ],
  // 分成3行 自适应布局
  devicesTwice1:[
    {
      label: '光功率预测柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/光功率预测柜.png'),
      shadowPsKey:'107353_9999_1_4',
      psKey:''
    },{
      label: '光伏厂区-网络柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/光伏厂区-网络柜.png'),
      shadowPsKey:'107353_9999_1_5',
      psKey:''
    },{
      label: '光伏厂区-服务器柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/光伏厂区-服务器柜.png'),
      shadowPsKey:'107353_9999_1_6',
      psKey:''
    },{
      label: 'UPS电源柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/UPS电源柜.png'),
      shadowPsKey:'107353_9999_1_7',
      psKey:''
    },{
      label: '直流馈线柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/直流馈线柜.png'),
      shadowPsKey:'107353_9999_1_8',
      psKey:''
    },{
      label: '充电柜/直流屏',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/充电柜.png'),
      shadowPsKey:'107353_9999_1_9',
      psKey:'107353_33_10008348_1'
    },{
      label: '2#电池柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/2电池柜.png'),
      shadowPsKey:'107353_9999_1_10',
      psKey:''
    },{
      label: '1#电池柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/1电池柜.png'),
      shadowPsKey:'107353_9999_1_11',
      psKey:''
    },
  ],
  devicesTwice2:[
    {
      label: 'NMC8600新能源保护控制屏',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/NMC8600新能源保护控制屏.png'),
      shadowPsKey:'107353_9999_1_12',
      psKey:''
    },{
      label: 'PMU柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/PMU柜.png'),
      shadowPsKey:'107353_9999_1_13',
      psKey:''
    },{
      label: '通讯机柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/通讯机柜.png'),
      shadowPsKey:'107353_9999_1_14',
      psKey:''
    },{
      label: '运动工作站柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/运动工作站柜.png'),
      shadowPsKey:'107353_9999_1_15',
      psKey:''
    },{
      label: '网络柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/网络柜.png'),
      shadowPsKey:'107353_9999_1_16',
      psKey:''
    },{
      label: '省调数据网柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/省调数据网柜.png'),
      shadowPsKey:'107353_9999_1_17',
      psKey:''
    },{
      label: '地调数据网柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/地调数据网柜.png'),
      shadowPsKey:'107353_9999_1_18',
      psKey:''
    },{
      label: '保护及信息管理柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/保护及信息管理柜.png'),
      shadowPsKey:'107353_9999_1_19',
      psKey:''
    },{
      label: '110kV故障录波屏',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/110kV故障录波屏.png'),
      shadowPsKey:'107353_9999_1_20',
      psKey:''
    },
  ],
  devicesTwice3:[
    {
      label: '主变保护柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/主变保护柜.png'),
      shadowPsKey:'107353_9999_1_21',
      psKey:''
    },{
      label: '主变测控柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/主变测控柜.png'),
      shadowPsKey:'107353_9999_1_22',
      psKey:''
    },{
      label: '110kV线路保护测控柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/110kV线路保护测控柜.png'),
      shadowPsKey:'107353_9999_1_23',
      psKey:''
    },{
      label: '频率电压紧急控制柜/故障解列',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/网络柜.png'),
      shadowPsKey:'107353_9999_1_24',
      psKey:'107353_13_10006995_2'
    },{
      label: '35kV母线保护柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/35kV母线保护柜.png'),
      psKey:'107353_31_24_1'
    },{
      label: '公用测控柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/公用测控柜.png'),
      psKey:'107353_34_10007216_1'
    },{
      label: '计量柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/计量柜.png'),
      shadowPsKey:'107353_9999_1_25',
      psKey:''
    },{
      label: '电能质量在线监测柜',
      src: require('@/assets/images/2dMap/boosterRoom/twiceDevice/电能质量在线监测柜.png'),
      shadowPsKey:'107353_9999_1_26',
      psKey:''
    },
  ],
}

export const volatageRoomDeviceKeys = boosterRoomDevice.devicesHighVoltage.concat(boosterRoomDevice.devicesLowVoltage).filter(item => item.psKey != '').map(item => item.psKey)
export const twiceRoomDeviceKeys = boosterRoomDevice.devicesTwice3.concat(boosterRoomDevice.devicesTwice1.concat(boosterRoomDevice.devicesTwice2)).filter(item => item.psKey != '').map(item => item.psKey)
export const baseDataPsKeys =  boostDevices.filter(cur => cur && cur.baseData).map(cur => cur.psKey)
