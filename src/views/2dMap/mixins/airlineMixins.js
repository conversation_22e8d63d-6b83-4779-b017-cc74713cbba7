import { getPsAirLines } from '@/api/2dMap/map';
import airicon from '@/assets/images/2dMap/uav/air-mark.png';
import startImg from '@/assets/images/2dMap/uav/start-light.png';
import endImg from '@/assets/images/2dMap/uav/end-light.png';
import startImgHover from '@/assets/images/2dMap/uav/start-light-hover.png';
import endImgHover from '@/assets/images/2dMap/uav/end-light-hover.png';
export const AirlineMixins = {
  data () {
    return {
      pdfUrl: '',
      wholeLine: {},
      partLine: {},
      startMarker: null,
      endMarker: null,
      airMark: null,
      line: {},
      airIndex: 0,
      oldIndex: 0,
      longitude: 0,
      latitude: 0,
      airLineCenter: null,
      autoBack: false,
      lineCenter: false,
      markerCenter: false,
      flightId: '',
      droneId: '',
      isEndTask: false,
      isFlying: false
    };
  },
  watch: {
    isFlying: {
      immediate: true,
      handler(val) {
        this.$emit("setIsFlying", val);
      },
    },
  },
  methods: {
    getAirLine (setCenter, lineId) {
      this.autoBack = false;
      this.lineCenter = false;
      this.markerCenter = false;
      this.isEndTask = false;
      this.isFlying = true;
      this.airIndex = 0;
      getPsAirLines({ psaId: this.psaId }, { waylineIds: lineId }).then(res => {
        if (res.payload && res.payload.length > 0) {
          this.line = res.payload[0];
          this.wholeLine = {
            latlngs: res.payload[0].pointList.map(el => [el.lat, el.lon]),
            polyline: {}, // 路径图层
            polylineBg: {}, // 路径背景图层
            arrows: {} // 路径箭头图层
          };
          let arr = [res.payload[0].pointList[0], res.payload[0].pointList[1]];
          this.partLine = {
            latlngs: arr.map(el => [el.lat, el.lon]),
            polyline: {}, // 路径图层
            polylineBg: {}, // 路径背景图层
            arrows: {} // 路径箭头图层
          };

          let len = res.payload[0].pointList.length;
          let startIcon = L.icon({ iconUrl: startImg, iconSize: [35, 45], iconAnchor: [17.5, 45] });
          let startIconHover = L.icon({ iconUrl: startImgHover, iconSize: [35, 45], iconAnchor: [17.5, 45] });
          this.startMarker = L.marker(
            [res.payload[0].pointList[0].lat, res.payload[0].pointList[0].lon], {
            icon: startIcon
          });
          this.startMarker.on('mouseover', () => {
            this.startMarker.setIcon(startIconHover)
          })
          this.startMarker.on('mouseout', () => {
            this.startMarker.setIcon(startIcon)
          })
          this.startMarker.addTo(this.map);
          let endIcon = L.icon({ iconUrl: endImg, iconSize: [36, 45], iconAnchor: [18, 45] });
          let endIconHover = L.icon({ iconUrl: endImgHover, iconSize: [36, 45], iconAnchor: [18, 45] });
          this.endMarker = L.marker(
            [res.payload[0].pointList[len - 1].lat, res.payload[0].pointList[len - 1].lon], {
            icon: endIcon
          });
          this.endMarker.on('mouseover', () => {
            this.endMarker.setIcon(endIconHover)
          })
          this.endMarker.on('mouseout', () => {
            this.endMarker.setIcon(endIcon)
          })
          this.endMarker.addTo(this.map);
          if (setCenter) {
            let minX = this.getExtremum(res.payload[0].pointList, 'lon', 'min');
            let maxX = this.getExtremum(res.payload[0].pointList, 'lon', 'max');
            let minY = this.getExtremum(res.payload[0].pointList, 'lat', 'min');
            let maxY = this.getExtremum(res.payload[0].pointList, 'lat', 'max');
            this.map.fitBounds([
              [minY, minX],
              [maxY, maxX]
            ]);
            setTimeout(() => {
              this.airLineCenter = this.map.getCenter();
            }, 100);
          }
          this.showLineInMap(this.wholeLine, 'whole');
        } else {
          this.isFlying = false;
        }
      }).catch(() => { this.isFlying = false; })
    },
    showLineInMap (line, type) {
      let bgColor = type == 'part' ? '#40AAFF' : '#B4B4B4';
      let lineColor = type == 'part' ? '#40AAFF' : '#ccc';
      const { latlngs } = line;
      line.polyline = L.polyline(latlngs, { color: lineColor, weight: 8 }).addTo(this.map);
      line.polylineBg = L.polyline(latlngs, { color: bgColor, weight: 6 }).addTo(this.map);
      line.arrows = L.polylineDecorator(line.polyline, {
        patterns: [{
          repeat: 50,
          symbol: L.Symbol.arrowHead({
            pixelSize: 4,
            headAngle: 75,
            polygon: false,
            pathOptions: {
              stroke: true,
              weight: 2,
              color: '#FFFFFF'
            }
          })
        }]
      }).addTo(this.map);
    },
    // 航线数据展示
    setAirLineData (res) {
      
      if (this.map && this.map.removeLayer) {
        let data = JSON.parse(res.data);
        // console.log(data);
        // 航线巡检进度
        if (data.biz_code == 'flighttask_progress') {
          if (this.flightId == data.data.bid) {
            this.airIndex = data.data.output.ext.current_waypoint_index;
          }
        }
        // 无人机位置
        if (data.biz_code == 'device_osd') {
          if (data.data.sn == this.droneId) {
            this.longitude = data.data.host.longitude;
            this.latitude = data.data.host.latitude;
            this.setAirLinePosition(data.data.host);
          }
        }
      }
    },
    setAirLinePosition (data) {
      if (this.map && this.map.removeLayer) {
        if (this.airMark) {
          this.map.removeLayer(this.airMark);
        }
        let arr = [];
        for (let i = 0; i < this.airIndex; i++) {
          arr.push(this.line.pointList[i]);
        }
        arr.push({ lat: this.latitude, lon: this.longitude });
        if (data.mode_code == 9) {
          this.autoBack = true;
        }
        if (this.airIndex == 2 && data.mode_code == 0) {
          this.isFlying = false;
          this.cleanLine(true);
          this.$emit('closeLiveWindow');
        }
        if(data.mode_code == 0 && this.isEndTask) {
          this.isFlying = false;
          this.$emit('closeLiveWindow');
        }
        if (!this.autoBack && !this.isEndTask && this.isFlying) {
          this.cleanLine();
          this.partLine = {
            latlngs: arr.map(el => [el.lat, el.lon]),
            polyline: {}, // 路径图层
            polylineBg: {}, // 路径背景图层
            arrows: {} // 路径箭头图层
          };
          this.showLineInMap(this.partLine, 'part');
        }
        if (this.latitude && this.longitude && this.isFlying) {
          this.$emit('getDroneVideo');
          this.airMark = L.marker([this.latitude, this.longitude], {
            icon: L.icon({
              iconUrl: airicon, // 路径
              iconSize: [42, 43], // 大小
              iconAnchor: [21, 21] // icon图标点位置偏移
            })
          });
          this.airMark.on('click', () => {
            this.$emit('openLiveWindow');
          })
          this.airMark.addTo(this.map);
        }
        // 飞往航线起始点和自动返航时，以飞机位置居中显示
        if (this.autoBack && this.isFlying) {
          if (this.latitude && this.longitude) {
            setTimeout(() => {
              if (this.markerCenter) {
                this.map.setView([this.latitude, this.longitude], this.map.getZoom());
              } else {
                this.markerCenter = true;
                this.map.flyTo([this.latitude, this.longitude], this.map.getZoom());
              }
            }, 100);
          }
        // 在航线上飞行时，以整体航线居中显示
        } else if (arr.length == 2 && !this.lineCenter) {
          this.lineCenter = true;
          setTimeout(() => {
            this.map.setView([this.airLineCenter.lat, this.airLineCenter.lng], this.map.getZoom());
          }, 200);
        }
      }
    },
    cleanLine (cleanAll) {
      if (this.partLine.polyline && this.map.hasLayer(this.partLine.polyline)) {
        this.map.removeLayer(this.partLine.polyline);
        this.map.removeLayer(this.partLine.polylineBg);
        this.map.removeLayer(this.partLine.arrows);
      }
      if (cleanAll) {
        this.map.removeLayer(this.wholeLine.polyline);
        this.map.removeLayer(this.wholeLine.polylineBg);
        this.map.removeLayer(this.wholeLine.arrows);
        this.wholeLine = {};
        this.partLine = {};
        if (this.airMark) {
          this.map.removeLayer(this.airMark);
        }
        if (this.startMarker) {
          this.map.removeLayer(this.startMarker);
          this.map.removeLayer(this.endMarker);
        }
      }
    }
  }
};
