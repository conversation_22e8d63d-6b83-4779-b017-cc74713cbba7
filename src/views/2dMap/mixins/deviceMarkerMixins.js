// 设备标注相关js
import { getBindLocation, getBindArea, stationTowerInfo } from '@/api/2dMap/map';
import towerIcon from '@/assets/images/2dMap/mapIcon/tower.png';
import towerHoverIcon from '@/assets/images/2dMap/mapIcon/tower-hover.png';
import {boostDevices, twiceRoomDeviceKeys, volatageRoomDeviceKeys} from "./boostDevices";
// import J from "v-viewer";
// import {assign} from "xe-utils";
import { debounce } from 'lodash'
export const DeviceMarkerMixins = {
  data () {
    return {
      areaLayers: [],
      towerLineArr: [],
      towerMarkers: [],
      lastSelectMarker: null,
      activeDeviceType: '',
      boostDevices,
      boostDeviceLayers: [],
      boostDeviceMarkers: [],
      tempNammeMarker: null,
      selectedAreaLayer: null,
      lastHoverAreaLayer: null,
      lastHoverAreaMarker: null,
      tempNameLayerZIndex: 100,
      rightNames: ['50710接地刀闸', '507断路器', '110kV线路电流互感器', '5070接地刀闸', '50720接地刀闸', '110kV线路电压互感器', '110kV线路避雷器'],
      leftNames: ['5071刀闸', '5072刀闸']
    }
  },

  methods: {
    // 获取电站区域（方阵）信息
    getBindArea (modelId) {
      this.areaLayers = [];
      this.areaMarkers = [];
      getBindArea({ modelId: modelId }).then(res => {
        if (res.result_data && res.result_data.length > 0) {
          this.$emit('getAreaData',res.result_data)
          res.result_data.forEach((item, index) => {
            let drawingStyle = {
              color: '#40AAFF',
              weight: 1,
              fillColor: 'transparent'
            };
            let layer = {};
            if (item.shapeType == 2) {
              let data = this.dealRectPositions(item.positions);
              layer = L.rectangle(data, drawingStyle).addTo(this.map);
            } else {
              let data = this.dealPolygonPositions(item.positions);
              layer = L.polygon(data, drawingStyle).addTo(this.map);
            }
            this.areaLayers.push(layer);
            layer.id = item.id;
            layer.name = item.name;
            layer.psKey = item.uniqueIndex;
            layer.shapeType = item.shapeType;
            layer.index = index;
            let center = this.getCenter(item.positions);
            let divIcon = this.getDivIcon('bottom', item);
            let areaMarker =  L.marker([center.latitude, center.longitude], { icon: divIcon });
            areaMarker.index = index;
            areaMarker.id = item.id;
            areaMarker.name = item.name;
            areaMarker.psKey = item.uniqueIndex;
            areaMarker.shapeType = item.shapeType;
            this.areaMarkers.push(areaMarker);
            this.addAreaLayerEvents(layer, areaMarker);
            areaMarker.on('mouseover', e=> {
              this.areaMouseover(layer, areaMarker);
            })
            areaMarker.on('mouseout', e=> {
              this.areaMouseout(layer, areaMarker);
            })
            areaMarker.on('click', e => {
              if(this.mapZoom >=19){
                return
              }
              this.areaClick(this.areaLayers[areaMarker.index]);
              this.$emit('areaLayerClick', e, this.areaLayers[areaMarker.index]);
            })
          });
        }
      });
    },
    // 电站区域（方阵）标注绑定事件
    addAreaLayerEvents (layer, areaMarker) {
      // 图层单击事件
      layer.on('click', e => {
        if(this.mapZoom >=19){
          return
        }
        this.areaClick(layer)
        this.$emit('areaLayerClick', e, layer);
      });
      layer.on('mouseover', () => {
        this.areaMouseover(layer, areaMarker)
      })
      layer.on('mouseout', () => {
        this.areaMouseout(layer, areaMarker)
      })
    },
    // 方阵移入事件
    areaMouseover (layer, areaMarker) {
      if(this.mapZoom >= 19){
        return
      }
      if (this.lastHoverAreaLayer) {
        if (this.selectedAreaLayer && this.lastHoverAreaLayer.id == this.selectedAreaLayer.id) {
          this.setLayerStyle(this.selectedAreaLayer, '#40AAFF', 4, 'transparent');
        } else {
          this.setLayerStyle(this.lastHoverAreaLayer, '#40AAFF', 2, 'transparent');
        }        
      }
      this.setLayerStyle(layer, '#40AAFF', 2, '#40AAFF', 1, 0.2);
      this.lastHoverAreaLayer = layer;
      if (this.mapZoom >= 16 ) {
        if (this.lastHoverAreaMarker) {
          let obj = {
            id: this.lastHoverAreaMarker.id,
            name: this.lastHoverAreaMarker.name,
          }
          let divIcon = this.getDivIcon('bottom', obj);
          this.lastHoverAreaMarker.setIcon(divIcon)
          this.setNameMarkerStyle('bottom', obj);
        }
        let divIcon = this.getDivIcon('hover', {id: areaMarker.id, name: areaMarker.name });
        areaMarker.setIcon(divIcon)
        this.setNameMarkerStyle('hover', { id: areaMarker.id});
        this.lastHoverAreaMarker = areaMarker;
      } else {
        this.lastHoverAreaMarker = null;
      } 
    },
    // 方阵移出事件
    areaMouseout (layer, areaMarker) {
      if(this.mapZoom >= 19){
        return
      }
      if (this.selectedAreaLayer && layer.id == this.selectedAreaLayer.id) {
        this.setLayerStyle(layer, '#40AAFF', 4, 'transparent');
      } else {
        this.setLayerStyle(layer, '#40AAFF', 2, 'transparent');
      }
      if (this.mapZoom >= 16) {
        let divIcon = this.getDivIcon('bottom', {id: areaMarker.id, name: areaMarker.name });
        areaMarker.setIcon(divIcon)
        this.setNameMarkerStyle('bottom', {id: areaMarker.id });
      }
    },
    // 外部选中方阵 #防止连续快速点击切换，增加防抖优化
    acticeAreaClick: debounce(function (psKey){
      let layer = this.areaLayers.find(item => item.psKey == psKey);
      this.areaClick(layer);
      requestAnimationFrame(this.zoomend);// fix: 方阵切换时不触发瓦片地图加载
    }, 300),
    // 方阵点击事件 外部主动触发
    areaClick(layer){
      // 获取被点击图层的边界范围
      let bounds = layer.getBounds();
      // 缩放地图到图层范围
      this.map.fitBounds(bounds);
      if (this.selectedAreaLayer) {
        this.setLayerStyle(this.selectedAreaLayer, '#40AAFF', 2, 'transparent');
      }
      this.setLayerStyle(layer, '#40AAFF', 4 , '#40AAFF', 1, 0.2);
      this.selectedAreaLayer = layer;
      setTimeout(() => {   
        this.setLayerStyle(layer, '#40AAFF', 4, 'transparent');
      }, 1000);
    },
    // 获取名称icon
    getDivIcon (type, data) {
      let className = 'matrix-name matrix-name-' + type;
      let divContent = `
          <div id="${data.id}" class="map-left-icon"></div>
          <div>${data.name}</div>
          <div class="map-right-icon"></div>`
      // 在圆形中心添加数字标注
      let divIcon = L.divIcon({ className: className, html: divContent});
      return divIcon
    },
    // 设置名称样式
    setNameMarkerStyle (type, data) {
      this.$nextTick(() => {
        let parentNode = document.getElementById(data.id).parentNode;
        let width = parseFloat(window.getComputedStyle(parentNode).width)
        if (type == 'right') {
          parentNode.style.marginLeft = -1 * (width + 15) + 'px';
        } else if (type == 'left') {
          parentNode.style.marginLeft = '15px';
        } else {
          parentNode.style.marginLeft = -1 * (width/2) + 'px';
        }
      })
    },

    addDeviceMarkerEvents (marker) {
      marker.on('mouseover', e => {
        let icon = L.icon({
          iconUrl: this.getIcon(marker.deviceType, true), // 路径
          ...this.deviceIconSizeHover
        });
        marker.setIcon(icon);
        this.setTempNameMarker(true, marker, marker.isError ? (this.mapZoom >this.alarmHalfMaxZoom ?  'device-error' : 'device-error-half') : 'device');
      });
      marker.on('mouseout', e => {
        let icon = L.icon({
          iconUrl: this.getIcon(marker.deviceType, false), // 路径
          iconSize: [28, 44], // 大小
          iconAnchor: [14, 44] // icon图标点位置偏移
        });
        marker.setIcon(icon);
        this.setTempNameMarker(false);
      });
      marker.on('click', e => {
        this.$emit('deviceMarkerClick', e, marker);
      })
    },

    setRectangleMarkerLabelLayer(psKey){
      let marker = this.rectangleMarkers.getLayers().find(item => item.psKey == psKey)
      this.setTempNameMarker(true,marker,'bottom')
    },

    setZoom(zoom){
      this.map.setZoom(zoom)
    },
    // 设置架空线路杆塔图标及连线展示
    setTowerPosition(psaId) {
      if(this.towerLineArr.length > 0) {
        this.towerLineArr.forEach(item => {
          this.map.removeLayer(item);
        })
        this.towerLineArr = [];
      }
      if(this.towerMarkers.length > 0) {
        this.towerMarkers.forEach(item => {
          this.map.removeLayer(item);
        })
        this.towerMarkers = [];
      }
      stationTowerInfo({ psaId: psaId }).then(res => {
        if(res.payload && res.payload.length > 0) {
          let icon = L.icon({ iconUrl: towerIcon, iconSize: [32.5, 32.5], iconAnchor: [16, 16] });
          let hoverIcon = L.icon({ iconUrl: towerHoverIcon, iconSize: [40, 40], iconAnchor: [20, 20] });
          res.payload.forEach(item => {
            let latLngs = [];
            item.towerInfoVOList.forEach((ele,index) => {
              let position = [ele.lat, ele.lon];
              let marker = L.marker(position, { icon: icon });
              marker.addTo(this.map);
              marker.id = new Date().getTime();
              marker.name = ele.name;
              marker.lng = ele.lon;
              marker.lat = ele.lat;
              marker.deviceType = '59';
              marker.index = index;
              marker.psKey = '';
              marker.shadowPsKey = ele.uniqueIndex || '';
              marker.on('mouseover', e => {
                this.setTempNameMarker(true, marker, 'tower');
                marker.setIcon(hoverIcon);
              })
              marker.on('mouseout', e => {
                this.setTempNameMarker(false, marker, 'tower');
                marker.setIcon(icon);
              })
              marker.on('click', e => {
                this.$emit('deviceMarkerClick', e, marker);
              })
              latLngs.push(position)
              this.towerMarkers.push(marker);
            })
            latLngs.push([31.954817706934115, 117.571741729313045]);
            // 创建折线对象并添加到地图上
            let towerLine = L.polyline(latLngs, {color: '#20A9FF', weight: 0.5 }).addTo(this.map);
            towerLine.id = 'tower';
            this.towerLineArr.push(towerLine);
          })
        }
      });
    },
    // 展示、隐藏升压站设备
    setBoostDevice (isShow) {

      this.boostDeviceLayers.forEach(item => {
        if (this.map.hasLayer(item)) {
          this.map.removeLayer(item);
        }
      })
      this.boostDeviceMarkers.forEach(item => {
        if (this.map.hasLayer(item)) {
          this.map.removeLayer(item);
        }
      })

      if(isShow) {
        this.boostDeviceLayers = [];
        this.boostDeviceMarkers = [];
        this.boostDevices.forEach((item, index) => {
          item.index = index;
          item.id = 'boost' + index;
          let positions = item.positions;
          let type = 'bottom';
          let center = {
            latitude: positions[1].latitude,
            longitude: (positions[0].longitude + positions[2].longitude)/2
          };
          if (this.rightNames.includes(item.name)) {
            type = 'right';
            center.latitude = (positions[0].latitude  + positions[1].latitude)/2;
            center.longitude = positions[0].longitude;
          } else if (this.leftNames.includes(item.name)) {
            type = 'left';
            center.latitude = (positions[0].latitude  + positions[1].latitude)/2;
            center.longitude = positions[2].longitude;
          }          
          this.setBoostLayers(item, center);
          this.setBoostMarkers(item, center, type);
        })
      } else {
        this.boostDeviceLayers.forEach(item => {
          if (this.map.hasLayer(item)) {
            this.map.removeLayer(item);
          }
        })
        this.boostDeviceMarkers.forEach(item => {
          if (this.map.hasLayer(item)) {
            this.map.removeLayer(item);
          }
        })
        this.boostDeviceLayers = [];
        this.boostDeviceMarkers = [];
      }
    },
    // 升压站设备名称图标展示
    setBoostMarkers (item, center, type) {
      let divIcon = this.getDivIcon(type, item);
      let marker =  L.marker([center.latitude, center.longitude], { icon: divIcon });
      marker.index = item.index;
      marker.name = item.name;
      marker.psKey = item.psKey;
      marker.positions = item.positions
      marker.lng = center.longitude;
      marker.lat = center.latitude;
      marker.roomType = item.roomType;
      marker.belong110kVPointKey = item.belong110kVPointKey || null;
      marker.addTo(this.map);
      this.setNameMarkerStyle(type, item);
      this.boostDeviceMarkers.push(marker);
      marker.on('mouseover', () => {

        // 已变红时 hover不变色
        if(!this.isBoosterLayerAlarm(marker)){
          return
        }
        this.setLayerStyle(this.boostDeviceLayers[marker.index], '#90F9BF', 2, 'transparent', 0.5);
      })
      marker.on('mouseout', e=> {
        this.setLayerStyle(this.boostDeviceLayers[marker.index], 'transparent', 1, 'transparent');
      })
      if(!item.roomType) {
        marker.on('click', e => {
          if(item.psKey){
            let temp = this.alarmList.find(el => el.psKey == item.psKey)
            if(temp){
              let obj = {
                ...marker
              }
              Object.assign(obj,temp)
              obj.isError=true
              this.$emit('deviceMarkerClick', e, obj)
              return
            }
          }
          this.$emit('boostDeviceClick', e, marker)
        })
      }
      if (item.roomType) {
        marker.on('click', e=> {
          this.$emit('goStationRoom', { value: marker.roomType });
        })
      }
    },
    isBoosterLayerAlarm(marker){
      if(this.alarm110kVs.includes(marker.hasOwnProperty('belong110kVPointKey') && marker.belong110kVPointKey)){
        return false
      }
      if(marker.roomType == 3 && this.rectangleMarkers.getLayers().some(item => item.psKey == 'twiceRoomAlarm')){
        return false
      }
      if(marker.roomType == 2 && this.rectangleMarkers.getLayers().some(item => item.psKey == 'volatageRoomAlarm')){
        return false
      }
      if( marker.psKey && this.rectangleMarkers.getLayers().some(item => item.psKey == marker.psKey )){
        return false
      }
      return true

    },
    // 升压站设备边框展示
    setBoostLayers (item, center) {
      let drawingStyle = {
        color: 'transparent',
        weight: 1,
        fillColor: 'transparent'
      };
      let data = this.dealRectPositions(item.positions);
      let layer = L.rectangle(data, drawingStyle).addTo(this.map);
      layer.index = item.index;
      layer.name = item.name;
      layer.psKey = item.psKey;
      layer.positions = item.positions;
      layer.lng = center.longitude;
      layer.lat = center.latitude;
      layer.roomType = item.roomType;
      layer.belong110kVPointKey = item.belong110kVPointKey || null;
      this.boostDeviceLayers.push(layer);
      layer.on('mouseover', () => {
        // 已变红时 hover不变色
        if(!this.isBoosterLayerAlarm(layer)){
          return
        }
        this.setLayerStyle(layer, '#90F9BF', 2, 'transparent', 0.5);
      })
      layer.on('mouseout', () => {
        this.setLayerStyle(layer, 'transparent', 1, 'transparent');
      })
      if(!item.roomType){
        layer.on('click', e=> {
          if(item.psKey){
            let temp = this.alarmList.find(el => el.psKey == item.psKey)
            if(temp){
              let obj = {
                ...layer
              }
              Object.assign(obj,temp)
              obj.isError=true
              this.$emit('deviceMarkerClick', e, obj)
              return
            }
          }
          this.$emit('boostDeviceClick', e, layer)
        })
      }

      if (item.roomType) {
        layer.on('click', e=> {
          this.$emit('goStationRoom', { value: layer.roomType });
        })
      }
    },
    // 根据psKey居中显示升压站设备
    setBoostDeviceCenter(psKey) {
      let layer = this.boostDeviceLayers.find(item => { return item.psKey == psKey});
      let bounds = layer.getBounds();
      // 缩放地图到图层范围
      this.map.fitBounds(bounds);
      this.setLayerStyle(layer, '#90F9BF', 2, 'transparent', 0.5);
    },
    // 设置边框样式
    setLayerStyle (layer, color, weight, fillColor, opacity, fillOpacity) {
      layer.setStyle({
        color: color,
        weight: weight,
        fillColor: fillColor,
        opacity: opacity || 1,
        fillOpacity: fillOpacity || 1
      });
    },
    getIcon (deviceType, isHover, isError,isOffline = false) {
      let name = '';
      switch (deviceType) {
        case '3':
          name = 'boost';
          break;
        case '6-4':
          name = 'transformer';
          break;
        case '6':
          name = 'transformer';
          break;
        case '1':
          name = 'inverter';
          break;
        case '4':
          name = 'combinerbox';
          break;
        case '10':
          name = 'string';
          break;
        case '54':
          name = 'camera';
          break;

      }
      if(isError) {
        name += '-error'
      }
      if(isHover) {
        name += '-hover'
      }
      if(isOffline) {
        name += '-offline'
      }
      return require('@/assets/images/2dMap/mapIcon/' + name + '.png');
    },
    // 临时名称图标展示、隐藏
    setTempNameMarker (isShow, data, className) {
      if (this.tempNammeMarker) {
        this.map.removeLayer(this.tempNammeMarker);
      }
      if (!isShow) {
        this.tempNammeMarker = null;
        return;
      }
      let divIcon = this.getDivIcon(className, data);
      this.tempNammeMarker =  L.marker([data.lat, data.lng], { icon: divIcon });
      this.tempNammeMarker.setZIndexOffset(this.tempNameLayerZIndex);
      this.tempNammeMarker.addTo(this.map);
      this.setNameMarkerStyle('bottom', data);
    },
    // 处理后台返回矩形标注数据
    dealRectPositions (positions) {
      let data = [
        [positions[0].latitude * 1, positions[0].longitude * 1],
        [positions[1].latitude * 1, positions[1].longitude * 1],
        [positions[3].latitude * 1, positions[3].longitude * 1],
        [positions[2].latitude * 1, positions[2].longitude * 1]
      ];
      return data;
    },
    // 处理后台返回多边形标注数据
    dealPolygonPositions (positions) {
      let arr = [];
      positions.forEach(item => {
        arr.push([item.latitude * 1, item.longitude * 1]);
      });
      return arr;
    },
  }
}