// 地图故障告警轮播逻辑
import {getModelData} from "../../../api/2dMap/psOverview";
// import {realtimeAlert} from "@/views/2dMap/data";
const cyclicInterval = 5 * 1000; // 轮播时间间隔 ms

export const AlarmCyclicMixins = {
  data(){
    return {
      nowAlarmCyclicLayer:null, //轮播图层
      nowAlarmCyclicLayerSize:{
        iconSize: [320,257],
        iconAnchor: [160, 257],
      }, //轮播图层
      cyclicAlarmTimer:null, //轮播定时器
      nowAlarmCyclicIndex:0, // 当前轮播index
      cyclicAlarmLayerZIndex:99, // 告警轮播地图上层级
      mapAlarmList:[],
      modalShow: false,
      BoostPosition: { lat: 610, lng: 157 },
    };
  },
  computed: {
    alarmData(){
      return this.mapAlarmList[this.nowAlarmIndex] || {};
    },
  },
  methods:{
    // 获取轮播数据
    async getMapAlarmCyclicData(){
      this.mapAlarmList = [];
      this.modalShow = false;
      let res = await getModelData({
        psId:this.psaInfo.psId,
        modelName:'realtimeAlert',
        operateType:1,
      });
      // res = {result_data: realtimeAlert};
      if(res.result_data.length == 0){
        return;
      }

      // 如果接口请求完 页面已经不在电站概览tab时 则不添加告警轮播
      if(!this.isInPsOverview){
        return;
      }
      // 筛选非光功率轮播数据
      this.mapAlarmList = res.result_data.filter(item=>item.needPlay === 1) || [];
           
      let position = this.getPositionByPsKey(this.mapAlarmList[this.nowAlarmCyclicIndex].psKey);
      // let icon = this.getMapAlarmCyclicDivIcon(this.mapAlarmList[this.nowAlarmCyclicIndex])
      // let positionCenter = this.getCenter(position)
      // this.nowAlarmCyclicLayer = L.marker([positionCenter.latitude, positionCenter.longitude], {icon})
      // this.movePositionToCenter([positionCenter],16)
      // this.nowAlarmCyclicLayer.addTo(this.map)
      // this.nowAlarmCyclicLayer.setZIndexOffset(this.cyclicAlarmLayerZIndex)
      // this.nowAlarmCyclicLayer.on('click', e=> {
      //     this.$emit('openRealtimeWindow',this.mapAlarmList[this.nowAlarmIndex])
      // })
      this.movePositionToCenter(position);
      this.startCyclic();

    },
    // 开始轮播
    startCyclic(){
      this.nowAlarmIndex = 0;
      clearInterval(this.cyclicAlarmTimer);
      this.cyclicAlarmTimer = null;
      this.cyclicAlarmTimer = setInterval(()=>{
        if(this.nowAlarmIndex == this.mapAlarmList.length - 1){
          this.nowAlarmIndex = 0;
        } else {
          this.nowAlarmIndex += 1;
        }
        // let icon = this.getMapAlarmCyclicDivIcon(this.mapAlarmList[this.nowAlarmIndex])
        // this.nowAlarmCyclicLayer.setIcon(icon)
        let position = this.getPositionByPsKey(this.mapAlarmList[this.nowAlarmIndex].psKey);
        // this.nowAlarmCyclicLayer.setLatLng([position[0].latitude,position[0].longitude]);
        // const arr = ['point1','point2','point3'];
        // const randomIdx = Math.floor(Math.random() * arr.length);
        // const randomId = arr[randomIdx];
        this.modalShow = true;
        this.movePositionToCenter(position, 2);
      },cyclicInterval);
    },

    // 终止轮播
    stopCyclic(){
      clearInterval(this.cyclicAlarmTimer);
      this.cyclicAlarmTimer = null;
      this.nowAlarmCyclicLayer && this.nowAlarmCyclicLayer.remove(this.map);
      this.mapAlarmList = [];
    },

  },
  beforeDestroy() {
    clearInterval(this.cyclicAlarmTimer);
    this.cyclicAlarmTimer = null;
  },
};