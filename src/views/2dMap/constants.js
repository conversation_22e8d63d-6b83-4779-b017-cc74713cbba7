
export const deviceTypePoints = [
    {
        deviceType:'4',
        points:[
            "p1001",
            "p1002",
            "p1009",
            "p1010",
            "p1011",
            "p1012",
            "p1013",
            "p1014",
            "p1015",
            "p1016",
            "p1017",
            "p1018",
            "p1019",
            "p1020",
            "p1021",
            "p1022",
            "p1023",
            "p1024",
            "p1005",
            "p1006",
            "p51000",
            "p51001",
            "p53008",
            "p53009"
        ]
    },{
        deviceType:'1',
        points: [
            "p1",
            "p24",
            "p14",
            "p21",
            "p22",
            "p23",
            "p5",
            "p7",
            "p9",
            "p45",
            "p6",
            "p8",
            "p10",
            "p46",
            "p39",
            "p40",
            "p25",
            "p27",
            "p15",
            "p16",
            "p17",
            "p26",
            "p4",
            "p33",
            "p34",
            "p35",
            "p36",
            "p53033",
            "p53034",
            "p53035",
            "p53010",
            "p53011",
            "p53012",
            "p53013",
            "p53014"
        ]
    },{
        deviceType:'6-4',
        points: [
            "p6000",
            "p6001",
            "p6002",
            "p6003",
            "p6004",
            "p6005",
            "p6006",
            "p6007",
            "p6008",
            "p62317",
            "p6011",
            "p6009",
            "p6010",
            "p6012",
            "p6030",
            "p6031",
            "p6032",
            "p6033",
            "p6034",
            "p6035",
            "p6036",
            "p6037",
            "p6038",
            "p62318",
            "p6041",
            "p6039",
            "p6040",
            "p6042",
            "p53015",
            "p53016",
            "p53017",
            "p53018",
            "p53019",
            "p53020",
            "p53021",
            "p53022",
            "p53023"
        ]
    },{
        deviceType:'12-8',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },
]


export const devicePsKeyPoints = [
    {
        psKey:'107353_33_10008348_1',
        points:[
            "p25202",
            "p25274",
            "p25203",
            "p25275",
            "p25269",
            "p25268",
            "p25270",
            "p25231",
            "p25232",
            "p25234",
            "p25276",
            "p25277",
            "p25278",
            "p25279",
            "p25280",
            "p25281",
            "p25282",
            "p25283",
            "p25284",
            "p25285"
        ]
    },{
        psKey:'107353_30_13_1',
        points:[
            "p35001",
            "p35002",
            "p35003",
            "p35004",
            "p35005",
            "p35006",
            "p35047",
            "p35048",
            "p35049",
            "p35050",
            "p35051",
            "p35052"
        ]
    },{
        psKey:'107353_31_24_1',
        points:[
            "p36058",
            "p36059",
            "p36060",
            "p36061",
            "p36062",
            "p36063",
            "p36064",
            "p36065",
            "p36066",
            "p36067",
            "p36068",
            "p36069",
            "p36070",
            "p36071",
            "p36072",
            "p36073",
            "p36074",
            "p36075",
            "p36076",
            "p36077",
            "p36078",
            "p36079",
            "p36080",
            "p36081",
            "p36082",
            "p36083",
            "p36084"
        ]
    },{
        psKey:'107353_6_26_1',
        points: [
            "p6060",
            "p6061",
            "p6062",
            "p6063",
            "p6064",
            "p6065",
            "p6438",
            "p6066",
            "p6067",
            "p6068",
            "p6069",
            "p6070",
            "p6072",
            "p6000",
            "p6001",
            "p6002",
            "p6003",
            "p6004",
            "p6005",
            "p62003",
            "p6006",
            "p6007",
            "p6008",
            "p6009",
            "p6010",
            "p6012",
            "p62011",
            "p62012",
            "p62013",
            "p62014",
            "p67283",
            "p67284",
            "p67285",
            "p53024",
            "p53025",
            "p53026",
            "p53027",
            "p53028",
            "p53029",
            "p53030",
            "p53031",
            "p53032",
            "p53036",
            "p53037",
            "p53038",
            "p53039",
            "p53040",
            "p53041",
            "p53042",
            "p53043",
            "p53044"
        ]
    },{
        psKey:'107353_6_10004773_8',
        points: [
            "p6060",
            "p6061",
            "p6062",
            "p6063",
            "p6064",
            "p6065",
            "p6066",
            "p6068",
            "p6069",
            "p6070",
            "p6072",
            "p67286",
            "p67287",
            "p67288"
        ]
    },{
        psKey:'107353_5_2_1',
        points: [
            "p2005",
            "p2001",
            "p2017",
            "p2009",
            "p2015",
            "p2014",
            "p2016",
            "p2018",
            "p2019",
            "p2012",
            "p2007",
            "p2003",
            "p2020",
            "p2021",
            "p2022"
        ]
    },{
        psKey:'107353_5_2_1',
        points: [
            "p2005",
            "p2001",
            "p2017",
            "p2009",
            "p2015",
            "p2014",
            "p2016",
            "p2018",
            "p2019",
            "p2012",
            "p2007",
            "p2003",
            "p2020",
            "p2021",
            "p2022"
        ]
    },
    // 110kV线路
    {
        psKey:'107353_12_28_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12010",
            "p12011",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p12016",
            "p12792",
            "p12013",
            "p12014",
            "p12015",
            "p12027",
            "p700345",
            "p700348",
            "p12241",
            "p700347",
            "p700344",
            "p700346"
        ]
    },
    // 气象站
    {
        psKey:'107353_5_10003725_1',
        points: [
            'p2005',
            'p2001',
            'p2017',
            'p2009',
            'p2015',
            'p2014',
            'p2016',
            'p2018',
            'p2019',
            'p2012',
            'p2007',
            'p2003',
            'p2020',
            'p2021',
            'p2022'
        ]
    },



    /*
    * 35kV #3集电线路 开关柜
    * */
    {
        psKey:'107353_12_7_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },
    {
        psKey:'107353_12_9_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },
    {
        psKey:'107353_12_10_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },{
        psKey:'107353_12_6_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },{
        psKey:'107353_12_8_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },{
        psKey:'107353_12_5_1',
        points: [
            "p12007",
            "p12008",
            "p12009",
            "p12013",
            "p12014",
            "p12015",
            "p12010",
            "p12012",
            "p12001",
            "p12002",
            "p12017",
            "p700532",
            "p700533",
            "p12244"
        ]
    },

]

export const keysIn110kv = [
    // 5071刀闸
    {
        key:'700344',
        points: [
            "p700344"
        ]
    },
    // 50710刀闸
    {
        key:'700346',
        points: [
            "p700346"
        ]
    },
    // 507断路器
    {
        key:'12241',
        points: [
            "p12241"
        ]
    },
    // 5072刀闸
    {
        key:'700345',
        points: [
            "p700345"
        ]
    },

    // 50720接地刀闸
    {
        key:'700347',
        points: [
            "p700347"
        ]
    },

    // 5070接地刀闸
    {
        key:'700348',
        points: [
            "p700348"
        ]
    },
]

export const exchangeOnOffPoint = [
    'p700344','p700346','p12241','p700345','p700347','p700348','p67286','p67287','p67288'
]

//组串电流
export const stringPoints = [
    'p1009', 'p1010', 'p1011', 'p1012', 'p1013', 'p1014', 'p1015', 'p1016', 'p1017', 'p1018', 'p1019', 'p1020', 'p1021', 'p1022', 'p1023', 'p1024'
]

// 组件电压
export const moduleVolatage = [
    'p520001', 'p520002', 'p520015', 'p520016', 'p520029', 'p520030', 'p520043', 'p520044', 'p520057', 'p520058', 'p520071', 'p520072', 'p520085', 'p520086', 'p520099', 'p520100', 'p520113', 'p520114', 'p520127', 'p520128', 'p520141', 'p520142'
]

// 组件电流
export const moduleAmpere = [
    'p520003', 'p520004', 'p520017', 'p520018', 'p520031', 'p520032', 'p520045', 'p520046', 'p520059', 'p520060', 'p520073', 'p520074', 'p520087', 'p520088', 'p520101', 'p520102', 'p520115', 'p520116', 'p520129', 'p520130', 'p520143', 'p520144'
]

export const pointModuleTemperature = 'p53006'

export const flowPaths = [
    [
        [31.935510770306202, 117.55694434046745],
        [31.935885495653125, 117.55684643983842],
        [31.936067309450994, 117.55683705210689],
        [31.936556127863074, 117.55696982145311],
        [31.936883902199156, 117.55709253251554],
        [31.937121196431807, 117.5572715699673],
        [31.937419947292458, 117.55761556327346],
        [31.93753091165051, 117.55779862403871],
        [31.93769878055298, 117.55792669951919],
        [31.938502839892156, 117.55883261561397],
        [31.93862404567928, 117.55902372300625],
        [31.939034323237248, 117.55933485925198],
        [31.93957718420926, 117.55959033966066],
        [31.939998838160523, 117.55981162190439],
        [31.940441544275917, 117.56004229187967],
        [31.941185262988032, 117.56023809313777],
        [31.941441892914362, 117.56036818027498],
        [31.941790134595173, 117.560732960701],
        [31.942090577337865, 117.56112724542619],
        [31.94252872124364, 117.56143972277641],
        [31.942901426428307, 117.56162881851198],
        [31.94328095824618, 117.56190039217473],
        [31.944441166482115, 117.56232284009457],
        [31.944988547528087, 117.56248041987419],
        [31.945057474365477, 117.56265281352596],
        [31.94536067373697, 117.56384700536731],
        [31.94528897981393, 117.56570778787137],
        [31.945551288260276, 117.56677061319354],
        [31.945732229441923, 117.56756655871871],
        [31.946213599529987, 117.56876349449159],
        [31.946457128590268, 117.56919465959074],
        [31.947528534990358, 117.57027089595795],
        [31.94808158746765, 117.57121033966544],
        [31.948325111576903, 117.57139205932619],
        [31.948839468788965, 117.57154226303102],
        [31.94916492341357, 117.57155299186708],
        [31.949497204568157, 117.57144302129747],
        [31.94992734756561, 117.57124722003938],
        [31.950227763702735, 117.571177482605],
        [31.950514523644483, 117.57144033908845],
        [31.95098562731991, 117.57245555520059],
        [31.95474981822293, 117.57178366184236]
    ],
    [
        [31.94522610533539, 117.5656544789672],
        [31.94525142583192, 117.56602831184867],
        [31.945464231527055, 117.56671294569972],
        [31.945509182399594, 117.5668839365244],
        [31.945555840244054, 117.56734661757949],
        [31.945584859135366, 117.56754510104659],
        [31.94567703672933, 117.56768591701987],
        [31.94622497940701, 117.5688694417477],
        [31.946314311392538, 117.56914235651494],
        [31.946572633970302, 117.56942532956602],
        [31.946983445032018, 117.56984576582911],
        [31.947592261371412, 117.57046133279802],
        [31.947777750407642, 117.57077783346179],
        [31.947974618729663, 117.57120698690417],
        [31.948193107934237, 117.57140547037126],
        [31.948554046504576, 117.57154720298774],
        [31.94918540653319, 117.57163479924203],
        [31.94930830515507, 117.5715932250023],
        [31.949757794084842, 117.57134109735492],
        [31.949994486105748, 117.57124722003938],
        [31.950241418958345, 117.57121905684474],
        [31.95051338570998, 117.57147252559663],
        [31.95098648076639, 117.57246594876052],
        [31.954746404576742, 117.57183194160463]
    ],
    [
        [31.933828526982666, 117.55883486321635],
        [31.93380917508172, 117.55888918307599],
        [31.93393894657475, 117.55897971617544],
        [31.93394065409319, 117.5593559314998],
        [31.93470732666145, 117.55987163485878],
        [31.93502378355579, 117.55936397888638],
        [31.936888793438175, 117.56063455507348],
        [31.936638365095654, 117.5622775631744],
        [31.936886122804907, 117.56231940016998],
        [31.938358235934228, 117.56254011176836],
        [31.939293834057644, 117.56326347501287],
        [31.939367846329457, 117.56353560275532],
        [31.939984791149122, 117.56396077301493],
        [31.940333550608994, 117.56396356884804],
        [31.941730094324456, 117.56469156900154],
        [31.94217628690349, 117.56526293345131],
        [31.942192544589037, 117.5655568793995],
        [31.942107500175666, 117.565735148212],
        [31.94199481390465, 117.56580757469155],
        [31.94115169479794, 117.56814129287424],
        [31.941056081218797, 117.56944496950616],
        [31.941625208199756, 117.57090422894602],
        [31.942831745743124, 117.57169823775885],
        [31.943127686494886, 117.57209792462749],
        [31.943796964372442, 117.57350621728541],
        [31.944076966936187, 117.5760518739185],
        [31.944819083424953, 117.57720265020473],
        [31.945208350720485, 117.57736359793705],
        [31.947104582442837, 117.57722410990235],
        [31.94945831384125, 117.57794032731123],
        [31.950298267217054, 117.57858143577839],
        [31.950874165771392, 117.57948810800386],
        [31.951768736208173, 117.58020432541275],
        [31.95182791867795, 117.58054231565067],
        [31.951693619941633, 117.58189964152669],
        [31.952167079019347, 117.58239321457252],
        [31.952897749057243, 117.5821866649827],
        [31.954308995605402, 117.57847681975235],
        [31.954110967155575, 117.57561999750344],
        [31.95316634281275, 117.57500034873392],
        [31.952280890933853, 117.5746033443275],
        [31.95139087800237, 117.57333185724204],
        [31.951078984122788, 117.57253358206225],
        [31.954747268434453, 117.57187221359058]
    ]
    ,
    [
        [31.941999534186234, 117.56601691246034],
        [31.941794686765256, 117.5666230916977],
        [31.941212576185016, 117.56815731525423],
        [31.94121314520986, 117.5686763226986],
        [31.94111015165453, 117.56942600011828],
        [31.941660966674707, 117.57083147764209],
        [31.942459870196785, 117.57136121392251],
        [31.942630575177045, 117.57139071822166],
        [31.942837696793955, 117.57160395383836],
        [31.943159758600633, 117.57189631462099],
        [31.94323259220962, 117.57202237844469],
        [31.9437014571855, 117.5729236006737],
        [31.943746977929525, 117.57306843996051],
        [31.943972305280457, 117.57381275296213],
        [31.944141869437786, 117.5760269165039],
        [31.944838331016832, 117.57711321115495],
        [31.9452002158317, 117.5772097706795],
        [31.946395108400193, 117.57712393999101],
        [31.947132520060034, 117.57710784673694],
        [31.94777661243922, 117.57732778787616],
        [31.948356974496697, 117.57745116949084],
        [31.949449410777447, 117.57784008979799],
        [31.950334729817424, 117.5785079598427],
        [31.95091962743359, 117.5794440507889],
        [31.951802656468494, 117.58015751838686],
        [31.95188003489697, 117.58053839206697],
        [31.951734381330425, 117.58189022541048],
        [31.952169065509274, 117.58233278989795],
        [31.95286546624146, 117.58215308189394],
        [31.95394191842362, 117.57930725812913],
        [31.954255976091243, 117.57848381996155],
        [31.954071638025184, 117.57563263177873],
        [31.95365516918062, 117.57533758878711],
        [31.953159045360355, 117.57503449916841],
        [31.952255546933053, 117.57466703653337],
        [31.95172982965274, 117.57389992475512],
        [31.951363418860012, 117.5733581185341],
        [31.9510516271588, 117.57250785827638],
        [31.954744128812568, 117.57186010479928]
    ],
    [
        [
            31.944756394634446,
            117.57684364914897
        ],
        [
            31.9448713331502,
            117.57699988782407
        ],
        [
            31.94502382561257,
            117.57707498967649
        ],
        [
            31.945481870479668,
            117.57707230746747
        ],
        [
            31.946414454152055,
            117.57700592279436
        ],
        [
            31.947162107441425,
            117.5770139694214
        ],
        [
            31.94750293062836,
            117.57711522281171
        ],
        [
            31.947998516011737,
            117.5771326571703
        ],
        [
            31.949474445623295,
            117.57750816643241
        ],
        [
            31.949631482228007,
            117.57793933153155
        ],
        [
            31.95028807439973,
            117.57843352854253
        ],
        [
            31.950532730594695,
            117.57875740528108
        ],
        [
            31.95067667916773,
            117.57880635559559
        ],
        [
            31.950918489504097,
            117.57942192256453
        ],
        [
            31.95140267722914,
            117.57981821894649
        ],
        [
            31.951812328775603,
            117.58015483617784
        ],
        [
            31.951891414072133,
            117.58053906261924
        ],
        [
            31.951746329483232,
            117.58188284933568
        ],
        [
            31.952173617165176,
            117.58232071995737
        ],
        [
            31.9528580698562,
            117.58214436471464
        ],
        [
            31.95424630404143,
            117.57848113775256
        ],
        [
            31.954063672791772,
            117.57564403116704
        ],
        [
            31.95315620060594,
            117.57504589855671
        ],
        [
            31.95224985736819,
            117.57467575371265
        ],
        [
            31.951354315467675,
            117.57336214184762
        ],
        [
            31.95104309269953,
            117.5725018233061
        ],
        [
            31.954745835635713,
            117.5718527287245
        ]
    ],
    [
        [
            31.9513076605678,
            117.5795925781131
        ],
        [
            31.951753725958024,
            117.58007436990741
        ],
        [
            31.951827121713944,
            117.58015483617784
        ],
        [
            31.95190165532857,
            117.58054241538048
        ],
        [
            31.951906775956367,
            117.58119821548463
        ],
        [
            31.951770794743695,
            117.58186876773836
        ],
        [
            31.95218272047632,
            117.58230060338977
        ],
        [
            31.952847259753568,
            117.58213162422182
        ],
        [
            31.953809922851523,
            117.57909670472147
        ],
        [
            31.954201357443576,
            117.57848918437959
        ],
        [
            31.953997675116927,
            117.57570371031764
        ],
        [
            31.95345262354096,
            117.57529601454735
        ],
        [
            31.953189768702266,
            117.57517397403717
        ],
        [
            31.952718676330175,
            117.57498621940613
        ],
        [
            31.95222596119196,
            117.57468581199647
        ],
        [
            31.95186467300826,
            117.5743679702282
        ],
        [
            31.951737795088516,
            117.57396563887599
        ],
        [
            31.951360005088002,
            117.57342517375946
        ],
        [
            31.95116769572596,
            117.57294505834582
        ],
        [
            31.951039109951605,
            117.57277674973011
        ],
        // [
        //     31.95076145509929,
        //     117.57216051220895
        // ],
        // [
        //     31.950480385603107,
        //     117.57150605320932
        // ],
        // [
        //     31.950113969826983,
        //     117.57127538323404
        // ],
        // [
        //     31.94996717551908,
        //     117.5713196396828
        // ],
        // [
        //     31.95011624570589,
        //     117.57130622863771
        // ],
        // [
        //     31.950461040707374,
        //     117.57152281701565
        // ],
        // [
        //     31.95075235164731,
        //     117.57218599319458
        // ],
        [
            31.950993592820208,
            117.57249712944032
        ],
        [
            31.954744128812568,
            117.57184334099293
        ]
    ],
    [
        // [
        //     31.95076145509929,
        //     117.57216051220895
        // ],
        // [
        //     31.950480385603107,
        //     117.57150605320932
        // ],
        // [
        //     31.950113969826983,
        //     117.57127538323404
        // ],
        [
            31.94996717551908,
            117.5713196396828
        ],
        [
            31.95011624570589,
            117.57130622863771
        ],
        [
            31.950461040707374,
            117.57152281701565
        ],
        [
            31.95075235164731,
            117.57218599319458
        ],
        [
            31.950993592820208,
            117.57249712944032
        ],
        [
            31.954744128812568,
            117.57184334099293
        ]
    ]
]

export const timeIntervalOptionsAll = {
    day: [
        { label: "1min", value: 1 },
        { label: "5min", value: 5 },
        { label: "15min", value: 15 },
        { label: "30min", value: 30 },
        { label: "60min", value: 60 },
    ],
    week: [
        { label: "15min", value: 15 },
        { label: "30min", value: 30 },
        { label: "60min", value: 60 },
    ],
    month: [
        { label: "30min", value: 30 },
        { label: "60min", value: 60 },
    ],
};

export const alarmTypeList = [
  {
    label: "全部",
    value: "all",
    num: 0,
    alarmTypes: "",
    alarmRemarks: ""
  },
  {
    label: "设备健康",
    value: "device",
    num: 0,
    alarmTypes: "0110,0210",
    alarmRemarks: "",
  },
  {
    label: "环境诊断",
    value: "environment",
    num: 0,
    alarmTypes: "",
    alarmRemarks: "82,84,86",
  },
//   {
//     label: "行为分析",
//     value: "behavioural",
//     num: 0,
//     alarmTypes: "",
//     alarmRemarks: "87",
//   },
];




