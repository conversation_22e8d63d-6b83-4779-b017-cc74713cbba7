<!-- 2d地图 弹窗标题组件 -->
<template>
  <div class="width-100 modal-title-2d flex-space-between">
    <div class="flex-start flex-shrink-0">
      <svg-icon icon-class="2d-double-arrow" class="modal-title-icon margin-l-8 margin-r-4"  />
      <span class="color-text-white"> {{ title }} </span>
      <slot name="left" />
    </div>
    <div class="width-100">
      <slot name="right" />
    </div>
  </div>
</template>

<script>
export default {
  name: "ModalTitle",
  props: {
    title:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      doubleArrowSrc:require('@/assets/images/2dMap/double-arrow.png'),

    }
  },
  created() {
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.modal-title-2d{
  height: 30px;
  border-radius: 2px;
  background: linear-gradient(270deg, rgba(0, 145, 255, 0) 0%, rgba(57, 145, 212, 0) 0%, #0091FF 100%);
  .modal-title-icon{
    color: #D8D8D8;
    font-size: 10px;
  }
}
</style>