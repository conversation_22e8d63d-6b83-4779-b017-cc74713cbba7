<!--光功率预测柜弹窗-->
<template>
  <a-modal
    v-model="visible"
    width="65%"
    :closable="false"
    :maskClosable="false"
    centered
    destroyOnClose
    :footer="null"
    :zIndex="1000"
    :bodyStyle="modalStyle"
    :getContainer="getDom"
    title=""
  >
    <a-spin :spinning="loading" class="height-100 width-100">
      <div class="height-100 width-100 detail-modal-2d detail-modal-bg-2d">
        <modal-title :title="nowDetailParams.deviceName" class="modal-title">
          <div slot="right" class="flex-end">
            <svg-icon
              icon-class="close"
              class="font-12 margin-r-8 cursor-pointer color-text-white"
              @click="handleClose"
            />
          </div>
        </modal-title>
        <div class="detail-tabs flex-start item-bg">
          <div
            class="detail-tab-item flex-center cursor-pointer"
            @click="changeTab(item.value)"
            :class="{ 'detail-tab-item-active': activeTab === item.value }"
            v-for="(item, index) in tabsList"
            :key="index"
          >
            <span class="font-14">{{ item.label }}</span>
          </div>
        </div>
        <div class="width-100 detail-tab-content">
          <template v-if="activeTab === 0">
            <!--     今日预测曲线       -->
            <div class="flex-center width-100 title-center pointer-events-none">
              <div class="title flex-center">
                <span class="color-text-white font-16">今日预测曲线</span>
              </div>
            </div>
            <div class="flex-end width-100 title-right">
              <svg-icon
                icon-class="refresh"
                @click="getCurrentTabData"
                class="color-text-second font-16 cursor-pointer"
              />
              <span class="margin-l-4 color-text-second font-14"
                >数据更新时间：{{ updateTime }}</span
              >
            </div>
            <CkRealTimeChart
              ref="ckRealTimeChart"
              :dataResult="tabData.todayData"
              class="margin-t-12"
            />
            <!--      短期/超短期     -->
            <div class="flex-center width-100 margin-t-16 margin-b-12">
              <div class="title flex-center">
                <span class="color-text-white font-16">短期/超短期</span>
              </div>
            </div>
            <SevenDayChart
              ref="sevenDayChart"
              :dataResult="tabData.sevenDayData"
            />
            <!--     巡检数据       -->
            <div class="flex-center width-100 margin-t-16 margin-b-12">
              <div class="title flex-center">
                <span class="color-text-white font-16">巡检数据</span>
              </div>
            </div>
            <div class="flex-start flex-row-wrap">
              <div
                class="flex-center width-100"
                v-if="tabData.nonRealTimeData.length === 0"
              >
                <div class="no-data margin-t-48"></div>
              </div>
              <div
                class="non-real-time-item flex-column"
                v-for="(item, index) in tabData.nonRealTimeData"
                :key="index"
              >
                <div class="fixed-img-area">
                  <img
                    class="non-real-time-item-img cursor-pointer"
                    @click="previewImage(item)"
                    :src="item.imgSrc"
                  />
                </div>
                <p
                  class="font-14 color-text-white text-ellipsis width-100 margin-b-0"
                  :title="item.title"
                >
                  {{ item.title }}
                </p>
                <p
                  class="non-real-time-item-label font-12 color-text-main margin-b-0"
                  :title="item.label"
                >
                  {{ item.label }}
                </p>
                <p
                  v-if="item.createTime"
                  class="font-14 color-text-white text-ellipsis width-100 margin-b-0"
                  :title="item.title"
                >
                  {{ item.createTime }}
                </p>
              </div>
            </div>
          </template>
          <!--    告警记录      -->
          <template v-if="activeTab === 1">
            <vxe-table
              class="my-table"
              :height="490"
              ref="xTable"
              border="none"
              :data="tabData.alarmListData"
              show-overflow
            >
              <vxe-table-column type="seq" title="序号" width="60" />
              <vxe-table-column
                v-for="(item, index) in alarmColumns"
                :key="index"
                :field="item.field"
                :title="item.title"
              >
                <template #default="{ row }">
                  <span v-if="item.field === 'affirmStatus'">
                    {{ row[item.field] ? "已确认" : "未确认" }}
                  </span>
                  <span v-else>{{ getLabel(row[item.field]) }}</span>
                </template>
              </vxe-table-column>
            </vxe-table>
          </template>
          <!--     基本信息      -->
          <template v-if="activeTab === 2">
            <div class="width-100 flex-start">
              <img
                class="device-img margin-r-12"
                :src="tabData.fileUrlThumbnail"
              />
              <div class="flex-center flex-column device-base-infos">
                <div class="device-base-info-item width-100 item-bg">
                  <span class="color-text-main flex-shrink-0 margin-r-8"
                    >设备名称</span
                  >
                  <div class="align-right-label">
                    <span class="color-text-white" style="text-align: right">{{
                      getLabel(nowDetailParams.deviceName)
                    }}</span>
                  </div>
                </div>
                <div
                  class="device-base-info-item width-100 flex-space-between item-bg"
                >
                  <span class="color-text-main flex-shrink-0 margin-r-8"
                    >设备型号</span
                  >
                  <div class="align-right-label">
                    <span class="color-text-white">{{
                      getLabel(tabData.deviceModel)
                    }}</span>
                  </div>
                </div>
                <div
                  class="device-base-info-item width-100 flex-space-between item-bg"
                >
                  <span class="color-text-main flex-shrink-0 margin-r-8"
                    >生产厂家</span
                  >
                  <div class="align-right-label">
                    <span class="color-text-white">{{
                      getLabel(tabData.maker)
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex-center width-100 margin-t-24 margin-b-16">
              <div class="title flex-center">
                <span class="color-text-white font-16">技术参数</span>
              </div>
            </div>

            <div class="width-100 flex-start flex-row-wrap">
              <div
                class="point-data-item flex-space-between item-bg"
                v-for="(item, index) in tabData.paramList"
                :key="index"
              >
                <span class="color-text-main">{{ item.itemLabel }}</span>
                <div class="flex-start">
                  <span class="color-text-white num-font-400">{{
                    item.itemDataValue || "--"
                  }}</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import ModalTitle from "./ModalTitle";
import { getModelData, nonRealTimeDataNotUAV } from "@/api/2dMap/psOverview";
import { PSA_INFO } from "@/store/mutation-types";
import VueViewer from "@/mixins/VueViewer";
import CkRealTimeChart from "./CkRealTimeChart";
import SevenDayChart from "./SevenDayChart.vue";
import moment from "moment";

export default {
  name: "OpticalPowerDrawer",
  props: {
    modalStyle: PropTypes.object.def({}),
    getDom: PropTypes.func,
  },
  components: { ModalTitle, CkRealTimeChart, SevenDayChart },
  mixins: [VueViewer],
  data() {
    return {
      visible: false,
      loading: false,
      nowDetailParams: {},
      activeTab: 0,
      tabsList: [
        {
          label: "监测数据",
          value: 0,
        },
        {
          label: "告警记录",
          value: 1,
        },
        {
          label: "基本信息",
          value: 2,
        },
      ],
      alarmColumns: [
        {
          field: "eventDesc",
          title: "文件名称",
        },
        {
          field: "remarkName",
          title: "上传状态",
        },
        {
          field: "alarmTime",
          title: "发生时间",
        },
        {
          field: "affirmStatus",
          title: "确认状态",
        },
      ],
      tabData: {
        nonRealTimeData: [], // 巡检数据
        alarmListData: [], // 告警记录
        paramList: [], //技术参数
        deviceModel: null, // 设备型号
        fileUrlThumbnail: null, // 设备图片
        deviceName: null, // 设备名称
        manufactureDate: null, // 生产日期
        maker: null, // 生产厂家
        todayData: [], // 今日预测数据
        sevenDayData: [], // 超短期
      },
      deviceType: 60,
    };
  },
  computed: {
    psaInfo() {
      return this.$ls.get(PSA_INFO);
    },
    updateTime() {
      return moment().format("YYYY-MM-DD HH:mm");
    },
  },
  methods: {
    init(params) {
      this.activeTab = 0;
      this.visible = true;
      this.nowDetailParams = params;
      this.$nextTick(() => {
        this.getCurrentTabData();
      });
    },
    changeTab(value) {
      this.activeTab = value;
      this.getCurrentTabData();
    },
    previewImage(item) {
      this.viewerImage({ images: [{ path: item.path }] });
    },
    // 获取巡检数据、今日预测、超短期
    async getMonitorData() {
      this.loading = true;
      const [nonRealTimeData, todayData, sevenDayData] =
        await Promise.allSettled([
          this.getNonRealTimeDataNotUav(),
          this.getTodayData(),
          this.getSevenDayData(),
        ]);
      this.loading = false;
      Object.assign(this.tabData, {
        nonRealTimeData: nonRealTimeData.value,
        todayData: todayData.value,
        sevenDayData: sevenDayData.value,
      });
    },
    // 获取巡检数据
    async getNonRealTimeDataNotUav() {
      const { shadowPsKey, psKey, deviceName } = this.nowDetailParams;
      let res = await nonRealTimeDataNotUAV({
        psKeyList: [shadowPsKey ? shadowPsKey : psKey],
      });
      if (res.result_data.length === 0) {
        return [];
      }
      return res.result_data?.[0].fileDetail?.pic?.map((item) => ({
        title: deviceName,
        label: item.annexName,
        imgSrc: item.fileUrlThumbnail,
        path: item.path,
        createTime: item.createTime,
      }));
    },
    // 获取今日预测曲线数据
    async getTodayData() {
      const res = await getModelData({
        psId: this.psaInfo.psId,
        psKey: this.nowDetailParams.shadowPsKey,
        modelName: "ckRealTimeData",
        recordDate: moment().format("YYYY-MM-DD"),
        deviceType: this.deviceType,
        points: "time_stamp,fileType,fileName,rov,create_time",
      });
      return res?.result_data ?? [];
    },
    // 获取近七日超短期曲线数据
    async getSevenDayData() {
      const res = await getModelData({
        psId: this.psaInfo.psId,
        psKey: this.nowDetailParams.shadowPsKey,
        modelName: "opticalPowerPredictionSevenDay",
        recordDate: moment().format("YYYY-MM-DD"),
        deviceType: this.deviceType,
        points: "time_stamp,fileType,fileName,stopCap,cap,rov,directRadiation",
      });
      return res?.result_data ?? [];
    },
    // 获取告警记录
    async getAlarmData() {
      this.loading = true;
      const res = await getModelData({
        psId: this.psaInfo.psId,
        psKey: this.nowDetailParams.shadowPsKey,
        modelName: "eventAlarmList",
        eventType: 1
      }).catch(() => {
        this.loading = false;
      });
      this.loading = false;
      Object.assign(this.tabData, {
        alarmListData: res?.result_data?.dataResult?.eventAlarmList ?? [],
      });
    },
    // 获取光功率预测柜基本信息
    async getBaseData() {
      this.loading = true;
      let res = await getModelData({
        psId: this.psaInfo.psId,
        psKey: this.nowDetailParams.shadowPsKey,
        modelName: "deviceBaseInfo",
        deviceTypeId: 870084,
        deviceName: "光功率",
      }).catch(() => {
        this.loading = false;
        return;
      });
      Object.assign(this.tabData, res.result_data);
      this.loading = false;
    },
    // 获取当前tab的数据
    getCurrentTabData() {
      switch (this.activeTab) {
        case 0:
          return this.getMonitorData();
        case 1:
          return this.getAlarmData();
        case 2:
          return this.getBaseData();
      }
    },
    // 处理弹窗关闭
    handleClose() {
      this.visible = false;
      this.tabData = this.$options.data().tabData;
    },
  },
};
</script>

<style scoped lang="less">
.non-real-time-item-label {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 40px;
}

.detail-tab-content {
  height: calc(100% - 76px);
  padding: 16px 16px 0;
  position: relative;
  overflow-y: scroll;

  .title-right {
    height: 30px;
  }
}

.non-real-time-item {
  background: #1f559e;
  width: 19%;
  border-radius: 4px;
  border: 1px solid #40aaff;
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 8px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .fixed-img-area {
    width: 100%;
    height: 0;
    padding: 0;
    padding-bottom: 56.25%;
    position: relative;
    overflow: hidden;
  }

  .non-real-time-item-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    background: #d8d8d8;
  }
}

.non-real-time-item:nth-child(5n) {
  margin-right: 0;
}

.device-img {
  width: 213px;
  height: 182px;
}

.device-base-infos {
  height: 182px;
}

.line-btn {
  padding: 0 8px;
  border-radius: 3px;
  border: 1px solid @di-color-text-main;
  box-sizing: border-box;
}

.modal-title {
  margin: 4px 2px 0;
  width: calc(100% - 8px);
}

.title-center {
  position: absolute;
  left: 0;
  top: 16px;
}

.detail-tabs {
  margin: 12px 16px 0;
  height: 30px;
  width: calc(100% - 32px);
  padding-left: 16px;

  .detail-tab-item {
    width: 64px;
    height: 30px;
    margin-right: 24px;
    color: @di-color-text-second;
  }
}

.item-bg {
  background: #0f3b78;
}

.detail-tab-item-active {
  color: @di-color-text-white !important;
  position: relative;
  background: linear-gradient(
    180deg,
    rgba(64, 170, 255, 0) 0%,
    rgba(64, 170, 255, 0.44) 100%
  );
}

.detail-tab-item-active::after {
  content: " ";
  position: absolute;
  width: 100%;
  height: 3px;
  bottom: 0;
  left: 0;
  background: @di-color-text-main;
}

.title {
  width: 138px;
  height: 30px;
  background: url("../../../assets/images/2dMap/2d-booster-station-room-title-bg.png");
  background-size: cover;
}

.point-data-item {
  width: 24.4%;
  height: 30px;
  margin-right: 0.6%;
  margin-bottom: 8px;
  padding: 0 8px;

  .color-text-strong-tip {
    color: #30d84c;
  }
}

.device-base-info-item {
  min-width: 254px;
  min-height: 30px;
  padding: 4px 8px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;

  .align-right-label {
    text-align: right;
    margin-left: 42px;
  }
}

.device-base-info-item:last-of-type {
  margin-right: 0;
}

.point-data-item:nth-child(4n) {
  margin-right: 0;
}
</style>
