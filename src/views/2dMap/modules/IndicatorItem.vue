<!-- 指标组件 -->
<template>
  <div class="indicator width-100" :style="{ height: String(height).includes('%') ? height : height + 'px' }">
    <a-spin :spinning="loading" class="width-100 height-100">
      <div class="title flex-space-between">
        <div class="flex-start flex-shrink-0">
          <svg-icon :icon-class="iconClass" class="title-icon" v-if="titlePosition == 'left'" />
          <span class="font-18 color-text-white text-blod title-label" v-if="titlePosition == 'left'">{{ title }}</span>
        </div>
        <slot class="title-slot" />
        <div class="flex-start flex-shrink-0">
          <span class="font-18 color-text-white text-blod title-label" v-if="titlePosition == 'right'">{{
            title
          }}</span>
          <svg-icon :icon-class="iconClass" class="title-icon" v-if="titlePosition == 'right'" />
        </div>

        <div class="left-bg pointer-events-none" :class="{ 'rotate-180': titlePosition == 'right' }">
          <div class="line-1"></div>
          <div class="line-2"></div>
        </div>
      </div>
      <component
        class="content"
        ref="component"
        v-if="hasContent"
        :dataParams="dataParams"
        @setLoading="setLoading"
        @callBack="callBack"
        :is="componentName"
        v-bind="$attrs"
      />
    </a-spin>
  </div>
</template>

<script>
import PsInfos from '../indicators/PsInfos';
import InductionDeviceScale from '../indicators/InductionDeviceScale';
import DeviceRunningStatus from './DeviceRunningStatus';
import GenerateElectricityCondition from '../indicators/GenerateElectricityCondition';
import ElectricQuantityLoss from '../indicators/ElectricQuantityLoss';
import DailyGenerateElectricityTrend from '../indicators/DailyGenerateElectricityTrend';
import RealTimeAlarm from '../indicators/RealTimeAlarm';
import DeviceScale from '../indicators/DeviceScale';
import AlarmList from '../indicators/AlarmList';
import MatrixList from '../indicators/MatrixList';
import MonitorDeviceCondition from '../indicators/MonitorDeviceCondition';
import ActivePowerTrend from '../indicators/ActivePowerTrend';
import ImportantMonitorVideo from '../indicators/ImportantMonitorVideo';

export default {
  name: 'IndicatorItem',
  components: {
    PsInfos,
    AlarmList,
    InductionDeviceScale,
    GenerateElectricityCondition,
    DailyGenerateElectricityTrend,
    ElectricQuantityLoss,
    RealTimeAlarm,
    DeviceScale,
    MatrixList,
    MonitorDeviceCondition,
    ActivePowerTrend,
    ImportantMonitorVideo,
    DeviceRunningStatus
  },
  props: {
    // 组件名
    componentName: {
      type: String,
      default: '',
      required: true
    },
    // 标题
    title: {
      type: String,
      default: '',
      required: true
    },
    // 页面查询参数
    dataParams: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 高度
    height: {
      type: Number | String,
      default: 400
    },
    // 标题位置
    titlePosition: {
      type: String,
      default: 'left'
    },
    hasContent: {
      type: Boolean,
      default: true
    },
    // 标题图标
    iconClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: true
    };
  },
  created() {
    if (!this.hasContent) {
      this.loading = false;
    }
  },
  methods: {
    refreshData(params) {
      this.$refs.component.refreshData(params);
    },
    setLoading(loading) {
      this.loading = loading;
    },
    // 更改方阵列表
    changeMatrix(index) {
      this.$refs.component.changeMatrix(index, true);
    },
    // 设置升压站告警列表按钮
    setRoom(e) {
      this.$refs.component.setRoom(e);
    },
    callBack(type, param) {
      this.$emit('callBack', type, param);
    },
    // 刷新无人机复检任务状态
    getListTaskStatus() {
      this.$refs.component.getListTaskStatus();
    }
  }
};
</script>

<style lang="less" scoped>
.indicator {
  .title {
    height: 32px;
    position: relative;
    .title-label{
      position: relative;
      z-index: 10;
    }
    .title-icon{
      font-size: 20px;
      margin: 0 6px;
      z-index: 10;
    }
    .title-slot{
      position: relative;
      z-index: 10;
    }
    .left-bg{
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, #0661AE 0%, rgba(5, 97, 175, 0) 100%);
      border-left: 2px solid #BDE2FF;
      z-index: 0;
      .line-1{
        position: absolute;
        right: 0;
        top: 0;
        background: linear-gradient(90deg, rgba(11, 94, 163, 0) 0%, #0B5EA3 100%);
        height: 1px;
        width: 97px;
      }
      .line-2{
        position: absolute;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, rgba(11, 94, 163, 0) 0%, #0B5EA3 100%);
        height: 1px;
        width: 97px;
      }
    }


  }
  .content{
    height: calc(100% - 32px) ;
  }
}
</style>
