<!-- 无人机飞行直播框 -->
<template>
  <div class="live-window"
       :style="{position:'fixed',left: this.left,top: this.top}"
       id="liveWindow">
    
    <div class="live-bg flex-start flex-column">
      <span class="close-icon" title="关闭" @click="close"><svg-icon icon-class="close"/></span>
      <div class="live-video-area margin-t-36" v-if="flyURL1">
        <live class="width-100 height-100"
              ref="live1"
              :url="flyURL1"/>
      </div>
      <div class="live-video-area empty-live div-center margin-t-28" v-else>
        <img src="../../../assets/images/2dMap/uav/load-error.png">
      </div>
      <div class="live-video-area margin-t-8" v-if="flyURL2">
        <live class="width-100 height-100"
              ref="live2"
              :url="flyURL2"/>
      </div>
      <div class="live-video-area empty-live div-center margin-t-8" v-else>
        <img src="../../../assets/images/2dMap/uav/load-error.png">
      </div>
      <div v-if="showLiveBtn" class="live-btn flex-center cursor-pointer"  @click="startOrStopFly">
        <div class="live-btn-icon" :class="isInFlight ? 'stop'  : 'start' "></div>
        <span class="font-14 margin-l-4" :class="isInFlight ? 'stop-text'  : 'start-text' ">
          {{ isInFlight ? '点击结束' : '点击起飞' }}
        </span>
      </div>

    </div>
  </div>
</template>

<script>
import {setElementDraggable } from "../../../utils/draggableWindow";
import Live from "../../../components/Live";
import {pLayLiveApi} from "../../../api/common";

export default {
  name: "LiveWindow",
  components: {Live},
  props: {},
  data() {
    return {
      left:'45%',
      isInFlight:false,
      psaInfo:null,
      flyURL1:'',
      flyURL2:'',
      top:'35%',
      deviceId: '',
      showLiveBtn: true,
      loading: true
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  mounted() {
    setTimeout(()=>{
      let el = document.getElementById('liveWindow')
      setElementDraggable(el,'handle',(e)=>{
        this.left = e.left
        this.top = e.top
      })
    },500)

  },
  methods: {
    // 开始飞行或结束飞行
    startOrStopFly(){
      if (!this.isInFlight) {
        this.$emit('addTask', this.deviceId)
      } else {
        this.$emit('endTask');
      } 
    },
    async getUrl(psKey, deviceId, urlProp){
      this.deviceId = deviceId;
      this.loading = true;
      let res = await pLayLiveApi({
        psaId:this.psaInfo.psaList[0],
        psId:this.psaInfo.psId,
        psKeys:psKey
      })
      this.loading = false;
      if (!this[urlProp]) {
        this[urlProp] = res && res[0] && res[0].liveUrl && res[0].liveUrl[0] ? res[0].liveUrl[0] : '';
      }
    },
    close () {
      this.$emit('close');
    }

  }
}
</script>

<style lang="less" scoped>
.live-btn-bg{
  background: rgba(0, 0, 0, .2);
}
.live-window{
  position: fixed;
  left: 50%;
  top: 50%;
  min-width: 100px;
  min-height: 100px;
  z-index: 99999;
  .live-bg{
    width: 324px;
    height: 430px;
    border-radius: 4px;
    background-image:url("../../../assets/images/2dMap/uav/2d-fly-live-bg.png");
    background-size: cover;
    .live-btn{
      height: 40px;
      color: #fff;
      .live-btn-icon{
        width: 24px;
        height: 24px;
        background-size: 24px 24px;
      }
      .start {
        background-image: url("../../../assets/images/2dMap/uav/2d-live-start.png");
      }
      .stop {
        background-image: url("../../../assets/images/2dMap/uav/2d-live-stop.png");
      }
    }
    .live-btn:hover {
      .start {
        background-image: url("../../../assets/images/2dMap/uav/2d-live-start-hover.png");;
      }
      .start-text {
        color: #BDE2FF;
      }
      .stop {
        background-image: url("../../../assets/images/2dMap/uav/2d-live-stop-hover.png");
      }
      .stop-text {
        color: #FFBDBD;
      }
    }
    .live-video-area{
      position: relative;
      width: 300px;
      height: 168px;
    }
    .empty-live {
      background: #0B2D5D;
      img {
        width: 80px;
      }
    }
    .close-icon {
      font-size: 10px;
      color: #fff;
      cursor: pointer;
      position: absolute;
      right: 12px;
      top: 16px;
    }
  }
  .live-bg:hover {
    background-image:url("../../../assets/images/2dMap/uav/2d-fly-live-bg-hover.png");
  }
}
</style>