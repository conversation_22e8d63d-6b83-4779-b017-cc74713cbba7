<!-- 设备详情抽屉 -->
<template>
  <a-spin :spinning="loading" class="height-100 width-100">
    <div class="height-100 width-100 detail-modal-2d detail-modal-bg-2d">
      <modal-title :title="nowDetailParams.deviceName" class="modal-title">
        <div slot="right" class="flex-end">
          <svg-icon
            icon-class="close"
            class="font-12 margin-r-8 cursor-pointer color-text-white"
            @click="close"
          />
        </div>
      </modal-title>
      <div class="detail-tabs flex-start item-bg">
        <div
          class="detail-tab-item flex-center cursor-pointer"
          @click="changeTab(item.value)"
          :class="{ 'detail-tab-item-active': activeTab == item.value }"
          v-for="(item, index) in nowDetailParams.psKey == ''
            ? tabsNoPsKey
            : tabs"
          :key="index"
        >
          <span class="font-14">{{ item.label }}</span>
        </div>
      </div>
      <div class="width-100 detail-tab-content" v-if="activeTab == 0">
        <template
          v-if="
            nowDetailParams.psKey != '' || nowDetailParams.belong110kVPointKey
          "
        >
          <div class="flex-center width-100 title-center pointer-events-none">
            <div class="title flex-center">
              <span class="color-text-white font-16">实时数据</span>
            </div>
          </div>
          <div class="flex-end width-100 title-right">
            <svg-icon
              icon-class="refresh"
              @click="refreshData"
              class="color-text-second font-16 cursor-pointer"
            />
            <span class="margin-l-4 color-text-second font-14"
              >数据更新时间：{{ updateTime }}</span
            >
          </div>
          <div class="flex-start flex-row-wrap width-100 margin-t-16">
            <div
              class="point-data-item flex-space-between item-bg"
              v-for="(item, index) in pointsData"
              :key="index"
            >
              <span
                class="color-text-main"
                :class="{ 'color-text-strong-tip': item.sourceType }"
                >{{ item.label }}</span
              >
              <div class="flex-start">
                <span class="color-text-white margin-r-2">{{
                  item.value
                }}</span>
                <span class="color-text-gray">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </template>

        <template>
          <div class="flex-center width-100 margin-t-16 margin-b-12">
            <div class="title flex-center">
              <span class="color-text-white font-16">巡检数据</span>
            </div>
          </div>
          <div class="flex-start flex-row-wrap">
            <div
              class="flex-center width-100"
              v-if="nonRealTimeData.length == 0"
            >
              <div class="no-data margin-t-48"></div>
            </div>
            <div
              class="non-real-time-item flex-column"
              v-for="(item, index) in nonRealTimeData"
              :key="index"
            >
              <div class="fixed-img-area">
                <img
                  class="non-real-time-item-img cursor-pointer"
                  @click="previewImage(item)"
                  :src="item.imgSrc"
                />
              </div>
              <p
                class="font-14 color-text-white text-ellipsis width-100 margin-b-0"
                :title="item.title"
              >
                {{ item.title }}
              </p>
              <p
                class="non-real-time-item-label font-12 color-text-main margin-b-0"
                :title="item.label"
              >
                {{ item.label }}
              </p>
              <p
                v-if="item.createTime"
                class="font-14 color-text-white text-ellipsis width-100 margin-b-0"
                :title="item.title"
              >
                {{ item.createTime }}
              </p>
            </div>
          </div>
        </template>
      </div>
      <div class="width-100 detail-tab-content" v-if="activeTab == 1">
        <vxe-table
          class="my-table"
          :height="490"
          ref="xTable"
          border="none"
          :data="alarmListData"
          show-overflow
        >
          <vxe-table-column
            type="seq"
            title="序号"
            width="60"
          ></vxe-table-column>
          <vxe-table-column
            v-for="item in columns"
            :key="item.key"
            :field="item.key"
            :title="item.label"
            :width="item.width || 120"
            show-overflow="title"
          >
            <template #default="{ row }">
              <span>{{ row[item.key] || "--" }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" width="80">
            <template v-slot="{ row }">
              <span
                class="color-text-white cursor-pointer"
                @click="goHealth(row)"
              >
                查看
              </span>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <div class="width-100 detail-tab-content" v-if="activeTab == 2">
        <div class="width-100 flex-start">
          <img
            class="device-img margin-r-12"
            :src="baseData.fileUrlThumbnail"
          />
          <div class="flex-center flex-column device-base-infos">
            <div class="device-base-info-item width-100 item-bg">
              <span class="color-text-main flex-shrink-0 margin-r-8"
                >设备名称</span
              >
              <div class="align-right-label">
                <span class="color-text-white" style="text-align: right">{{
                  baseData.deviceName || "--"
                }}</span>
              </div>
            </div>
            <div
              class="device-base-info-item width-100 flex-space-between item-bg"
            >
              <span class="color-text-main flex-shrink-0 margin-r-8"
                >设备型号</span
              >
              <div class="align-right-label">
                <span class="color-text-white">{{
                  baseData.deviceModel || "--"
                }}</span>
              </div>
            </div>
            <div
              class="device-base-info-item width-100 flex-space-between item-bg"
            >
              <span class="color-text-main flex-shrink-0 margin-r-8"
                >生产厂家</span
              >
              <div class="align-right-label">
                <span class="color-text-white">{{
                  baseData.maker || "--"
                }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-center width-100 margin-t-24 margin-b-16">
          <div class="title flex-center">
            <span class="color-text-white font-16">技术参数</span>
          </div>
        </div>

        <div class="width-100 flex-start flex-row-wrap">
          <div
            class="point-data-item flex-space-between item-bg"
            v-for="(item, index) in baseData.paramList"
            :key="index"
          >
            <span class="color-text-main">{{ item.itemLabel }}</span>
            <div class="flex-start">
              <span class="color-text-white num-font-400">{{
                item.itemDataValue || "--"
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="justify-center detail-tab-content width-100"
        v-if="activeTab == 3"
      >
        <div class="no-data margin-t-128"></div>
      </div>
      <div v-if="activeTab == 4" class="detail-tab-content width-100">
        <GraphChart
          :nowDetailParams="nowDetailParams"
          :pointsData="pointsData"
        />
      </div>
    </div>
  </a-spin>
</template>

<script>
import {
  getModelData,
  getPointsData,
  nonRealTimeDataNotUAV,
  nonRealTimeDataUAV,
} from "../../../api/2dMap/psOverview";
import moment from "moment";
import ModalTitle from "./ModalTitle";
import {
  deviceTypePoints,
  devicePsKeyPoints,
  stringPoints,
  moduleVolatage,
  moduleAmpere,
  pointModuleTemperature,
  keysIn110kv,
  exchangeOnOffPoint,
} from "../constants";
import {baseDataPsKeys, boostDevices} from "../mixins/boostDevices";
import VueViewer from "@/mixins/VueViewer";

import GraphChart from "./GraphChart";

export default {
  name: "DeviceDetailDrawer",
  components: { ModalTitle, GraphChart },
  mixins: [VueViewer],
  props: {
    nowDetailParams: {
      type: Object,
      default: () => {
        return {
          psKey: "",
          deviceName: "",
          shadowPsKey: "",
          deviceType: "",
        };
      },
    },
  },
  data() {
    return {
      tabs: [
        {
          label: "监测数据",
          value: 0,
        },
        {
          label: "告警记录",
          value: 1,
        },
        {
          label: "曲线图",
          value: 4,
        },
        {
          label: "基本信息",
          value: 2,
        },
        {
          label: "维修记录",
          value: 3,
        },
      ],
      tabsNoPsKey: [
        {
          label: "监测数据",
          value: 0,
        },
      ],
      activeTab: 0,
      tableHeight: 600,
      baseData: {
        deviceModel: null, // 设备型号
        fileUrlThumbnail: null, // 设备图片
        deviceName: null, // 设备名称
        manufactureDate: null, // 生产日期
        maker: null, // 生产厂家
        paramList: [], //技术参数
      },
      nonRealTimeData: [],
      columns: [
        {
          key: "alarmName",
          label: "故障类型",
        },
        {
          key: "alarmGradeName",
          label: "故障等级",
        },
        {
          key: "alarmReasonName",
          label: "故障原因",
        },
        {
          key: "riskWarning",
          label: "风险提示",
        },
        {
          key: "happenTime",
          label: "发生时间",
          width: 160,
        },
        {
          key: "lastHappenTime",
          label: "更新时间",
          width: 160,
        },
        {
          key: "updateTimes",
          label: "次数",
        },
        {
          key: "faultStatusName",
          label: "故障状态",
        },
        {
          key: "powerLoss",
          label: "电量损失值(kWh)",
          width: 200,
        },
        {
          key: "opinion",
          label: "处理建议",
          width: 300,
        },
      ],
      updateTime: "",
      alarmListData: [],
      loading: true,
      psaInfo: null,
      pointsData: [],
    };
  },
  created() {
    this.psaInfo = this.$ls.get("PSA_INFO");
    this.refreshData();
  },
  methods: {
    async refreshData() {
      this.loading = true;
      this.getNonRealTimeData();
      let param = this.getPointsParams();
      if (param == null) {
        this.loading = false;
        return;
      }
      let res = await getPointsData({
        psId: this.psaInfo.psId,
        queryItemList: [this.getPointsParams()],
      }).catch((e) => {
        this.loading = false;
        return;
      });

      this.pointsData = res.result_data[0].latestPointList.map((item) => {
        return {
          label: item.nameCn,
          point: item.point,
          value: !exchangeOnOffPoint.some((el) => el == item.point)
            ? item.displayValue || "--"
            : item.displayValue == null
            ? "--"
            : item.displayValue == 1
            ? "合"
            : "分",
          unit: item.displayUnit,
          sourceType: item.sourceType,
          pointType: item.pointType,
        };
      });
      this.updateTime = moment().format("YYYY-MM-DD HH:mm");
      this.loading = false;
    },
    previewImage(item) {
      this.viewerImage({ images: [{ path: item.path }] });
    },
    getPointsParams() {
      if (this.nowDetailParams.belong110kVPointKey) {
        let el = keysIn110kv.find(
          (item) => item.key == this.nowDetailParams.belong110kVPointKey
        );
        return {
          psKey: boostDevices[19].psKey,
          needPointList: el.points,
        };
      }
      let item = deviceTypePoints.find(
        (item) => item.deviceType == this.nowDetailParams.deviceType
      );
      if (item) {
        return {
          psKey: this.nowDetailParams.psKey,
          needPointList: item.points,
        };
      }
      item = devicePsKeyPoints.find(
        (item) => item.psKey == this.nowDetailParams.psKey
      );
      if (item) {
        return {
          psKey: this.nowDetailParams.psKey,
          needPointList: item.points,
        };
      }

      let splitPsKey = this.nowDetailParams.psKey.split("_");
      if (splitPsKey.length == 5) {
        // let index = splitPsKey[4] - 1;
        return {
          psKey: [
            splitPsKey[0],
            splitPsKey[1],
            splitPsKey[2],
            splitPsKey[3],
          ].join("_"),
          // needPointList: [stringPoints[index]],
          needPointList: stringPoints // 查询同汇流箱下所有组串
        };
      }
      if (splitPsKey.length == 6) {
        let index = splitPsKey[5] - 1;
        return {
          psKey: [
            splitPsKey[0],
            splitPsKey[1],
            splitPsKey[2],
            splitPsKey[3],
            splitPsKey[4],
          ].join("_"),
          needPointList: [
            moduleVolatage[index],
            moduleAmpere[index],
            pointModuleTemperature,
          ],
        };
      }
      return null;
    },
    getNonRealTimeData() {
      console.log(this.nowDetailParams);
      if (["10", "58", "59"].includes(this.nowDetailParams.deviceType)) {
        this.getNonRealTimeDataUAV();
      } else {
        this.getNonRealTimeDataNotUav();
      }
    },
    async getNonRealTimeDataUAV() {
      let res = await nonRealTimeDataUAV({
        deviceId: ["10", "58"].includes(this.nowDetailParams.deviceType)
          ? this.nowDetailParams.psKey
          : this.nowDetailParams.shadowPsKey,
        // deviceId:'107353_4_1495_7_2'
      });
      const { payload } = res;
      if (payload) {
        this.nonRealTimeData = [
          {
            title: this.nowDetailParams.deviceName,
            label: "可见光图片",
            imgSrc: payload.visibleThumbnail,
            path: payload.visible,
          },
          {
            title: this.nowDetailParams.deviceName,
            label: "红外图片",
            imgSrc: payload.infraredThumbnail,
            path: payload.infrared,
          },
        ];
      } else {
        this.nonRealTimeData = [];
      }
    },
    async getNonRealTimeDataNotUav() {
      const { shadowPsKey, psKey, deviceName } = this.nowDetailParams;
      let res = await nonRealTimeDataNotUAV({
        psKeyList: [shadowPsKey ? shadowPsKey : psKey],
        // psKeyList:['107353_34_10007216_1']
      });
      if (res.result_data.length == 0) {
        this.nonRealTimeData = [];
        return;
      }
      this.nonRealTimeData = res.result_data[0].fileDetail.pic.map((item) => {
        return {
          title: deviceName,
          label: item.annexName,
          imgSrc: item.fileUrlThumbnail,
          path: item.path,
          createTime: item.createTime,
        };
      });
    },

    close() {
      this.$emit("close");
    },
    async refreshAlarmData() {
      this.loading = true;
      let res = await getModelData({
        psId: this.psaInfo.psId,
        psKey: this.nowDetailParams.psKey,
        modelName: "alarmRecord",
      }).catch((e) => {
        this.loading = false;
        return;
      });

      this.alarmListData = res.result_data.dataResult.alarmRecord || [];
      this.loading = false;
    },
    async refreshBaseData() {
      this.loading = true;
      if(baseDataPsKeys.some(item => item == this.nowDetailParams.psKey)){
        Object.assign(this.baseData, boostDevices.find(o => o.psKey == this.nowDetailParams.psKey)?.baseData ?? {});
        this.loading = false;
        return
      }
      let res = await getModelData({
        psId: this.psaInfo.psId,
        psKey: this.nowDetailParams.psKey,
        modelName: "deviceBaseInfo",
      }).catch((e) => {
        this.loading = false;
        return;
      });
      this.baseData = res.result_data;
      this.loading = false;
    },
    changeTab(index) {
      this.activeTab = index;
      if (index == 0) {
        this.refreshData();
      }
      if (index == 1) {
        this.refreshAlarmData();
      }

      if (index == 2) {
        this.refreshBaseData();
      }
    },
    // 跳转health
    goHealth(data) {
      this.close();
      window.comeFromAlarm = true;
      this.$router.push({
        name: "/health/safe",
        params: {
          tabName: data.alarmName,
          handleStatus: data.alarmStatus,
          id: data.id,
        },
      });
    },
    // 跳转洞察工具
    goCurve() {},
  },
};
</script>

<style lang="less" scoped>
.non-real-time-item-label {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 40px;
}

.detail-tab-content {
  height: calc(100% - 76px);
  padding: 16px 16px 0;
  position: relative;
  overflow-y: scroll;

  .title-right {
    height: 30px;
  }
}

.non-real-time-item {
  background: #1f559e;
  width: 19%;
  border-radius: 4px;
  border: 1px solid #40aaff;
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 8px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .fixed-img-area {
    width: 100%;
    height: 0;
    padding: 0;
    padding-bottom: 56.25%;
    position: relative;
    overflow: hidden;
  }

  .non-real-time-item-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    background: #d8d8d8;
  }
}

.non-real-time-item:nth-child(5n) {
  margin-right: 0;
}

.device-img {
  width: 213px;
  height: 182px;
}

.device-base-infos {
  height: 182px;
}

.line-btn {
  padding: 0 8px;
  border-radius: 3px;
  border: 1px solid @di-color-text-main;
  box-sizing: border-box;
}

.modal-title {
  margin: 4px 2px 0;
  width: calc(100% - 8px);
}

.title-center {
  position: absolute;
  left: 0;
  top: 16px;
}

.detail-tabs {
  margin: 12px 16px 0;
  height: 30px;
  width: calc(100% - 32px);
  padding-left: 16px;

  .detail-tab-item {
    width: 64px;
    height: 30px;
    margin-right: 24px;
    color: @di-color-text-second;
  }
}

.item-bg {
  background: #0f3b78;
}

.detail-tab-item-active {
  color: @di-color-text-white !important;
  position: relative;
  background: linear-gradient(
    180deg,
    rgba(64, 170, 255, 0) 0%,
    rgba(64, 170, 255, 0.44) 100%
  );
}

.detail-tab-item-active::after {
  content: " ";
  position: absolute;
  width: 100%;
  height: 3px;
  bottom: 0;
  left: 0;
  background: @di-color-text-main;
}

.title {
  width: 138px;
  height: 30px;
  background: url("../../../assets/images/2dMap/2d-booster-station-room-title-bg.png");
  background-size: cover;
}

.point-data-item {
  width: 24.4%;
  height: 30px;
  margin-right: 0.6%;
  margin-bottom: 8px;
  padding: 0 8px;

  .color-text-strong-tip {
    color: #30d84c;
  }
}

.device-base-info-item {
  min-width: 254px;
  min-height: 30px;
  padding: 4px 8px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;

  .align-right-label {
    text-align: right;
    margin-left: 42px;
  }
}

.device-base-info-item:last-of-type {
  margin-right: 0;
}

.point-data-item:nth-child(4n) {
  margin-right: 0;
}
</style>
