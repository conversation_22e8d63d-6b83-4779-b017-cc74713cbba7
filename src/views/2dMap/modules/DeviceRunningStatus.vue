<!-- 2D/地图 发电设备异常情况  指标卡-->
<template>
  <div class="flex-start flex-column">

    <div class="title flex-space-between margin-t-16 width-100 linear-bg-border">
      <div class="flex-start">
        <svg-icon icon-class="alarm-total" class="font-24" />
        <span class="color-text-main font-16 margin-l-8">告警总数</span>
      </div>

      <div class="flex-start">
        <span class="color-text-white font-20 margin-r-4 num-font-700">{{ totalNum }}</span>
        <span class="color-text-gray font-12">个</span>
      </div>
    </div>
    <div class="height-100 flex-start">

      <div class="chart-area relative height-100">
        <div :id="'chartDeviceRunningStatus' + (dataParams.areaType || 0) "
             v-if="showChart"
             class="width-100 chart height-100">
        </div>
        <div class="chart-bg"></div>
      </div>

      <div class="legends flex-start flex-column">
        <div class="legend flex-space-between" v-for="(item,index) in legends" :key="index">
          <div class="flex-start" >
            <svg-icon :icon-class="item.iconClass" class="font-22" />
            <span class="color-text-main font-14 margin-l-8">{{ item.label }}</span>
          </div>
          <div class="flex-start">
            <span class="font-20 margin-r-4 num-font-700" :style="{color:item.color}">{{ item.value }}</span>
            <span class="color-text-gray font-12">个</span>
          </div>

        </div>
      </div>
    </div>
  </div>

</template>

<script>
import echarts from '@/utils/enquireEchart'
import 'echarts-gl';
import {textColor,chartColor} from "../../../utils/color";
import {getModelData} from "../../../api/2dMap/psOverview";
export default {
  name: "DeviceRunningStatus",
  props: {
    dataParams:{
      type:Object,
      default:()=>{
        return {
          areaType:undefined
        }
      }
    }
  },
  data() {
    return {
      option: {},
      myChart:null,
      legends:[
        {
          label:'火情识别',
          value:0,
          grade:1,
          color:'#D883F0',
          iconClass: 'fault-stop'
        },{
          label:'周界入侵',
          value:0,
          grade:2,
          color:'#FFA46B',
          iconClass: 'interruption'
        },{
          label:'水情监测',
          value:0,
          grade:3,
          color:'#FFF984',
          iconClass: 'hidden-danger'
        },{
          label:'风机监测',
          grade:4,
          value:0,
          color:'#3DCFDF',
          iconClass: 'inefficient'
        },
      ],
      showChart:false,
      psaInfo:null,
      totalNum:0,
      correntData:[]
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  mounted() {
    this.refreshData()
  },
  methods: {
    correntSeriesData(){
      this.correntData = this.legends.map(item => item.value)
      let sum = this.correntData.reduce((prev,cur) => prev + cur,0)
      this.correntData = this.correntData.map(item=>{
        if(item !=0 && item / sum < 0.01){
          return Math.round(sum * 0.01)
        } else {
          return item
        }
      })
    },
    async refreshData(){
      this.$emit('setLoading',true)
      let res = await getModelData({
        psId:this.psaInfo.psId,
        areaType:this.dataParams.areaType,
        modelName:'powerGenDevStatus',
      }).catch(e=>{
        this.$emit('setLoading',false)
        return
      })
      let data = res.result_data.dataResult.powerGenDevStatus
      this.legends.map((item,index)=>{
        let temp = data.find(el=> el.grade == item.grade)
        item.value = temp.number || 0
        // item.label = temp.faultType || item.label
        item.label = item.label
      })
      this.totalNum  = this.legends.reduce((prev,cur) => prev + cur.value,0)
      this.correntSeriesData()
      if(this.legends.every(item => item.value == 0)){
        this.showChart = false
        this.myChart = null
        this.$emit('setLoading',false)
      } else {
        this.showChart = true
        this.$nextTick(()=>{
          this.renderChart()
        })
      }
    },

    renderChart(){

      if(this.myChart == null){
        this.myChart = echarts.init(document.getElementById('chartDeviceRunningStatus' + (this.dataParams.areaType || 0)));
      }
      // 传入数据生成 option ; getPie3D(数据，透明的空心占比（调节中间空心范围的0就是普通饼1就很镂空）)
      this.option = this.getPie3D(this.legends.map((item,index) => {
        return {
          label:item.label,
          value:this.correntData[index],
          itemStyle:{
            color:item.color
          }
        }
      }), 0.85);
      //将配置项设置进去
      this.myChart.setOption(this.option,true);
      //鼠标移动上去特效效果
      this.bindListen(this.myChart);
      this.$emit('setLoading',false)
    },

    //配置构建 pieData 饼图数据 internalDiameterRatio:透明的空心占比
    getPie3D(pieData, internalDiameterRatio) {
      let that = this;
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let legendData = [];
      let legendBfb = [];
      let k = 1 - internalDiameterRatio;
      pieData.sort((a, b) => {
        return (b.value - a.value);
      });
      // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;
        let seriesItem = {
          //系统名称
          name: pieData[i].name,
          type: 'surface',
          //是否为参数曲面（是）
          parametric: true,
          //曲面图网格线（否）上面一根一根的
          wireframe: {
            show: false
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k
          },
          //设置饼图在容器中的位置(目前没发现啥用)
          //   center: ['50%', '100%']
        };

        //曲面的颜色、不透明度等样式。
        if (typeof pieData[i].itemStyle != 'undefined') {
          let itemStyle = {};
          typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;
          typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;
          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      legendData = [];
      legendBfb = [];
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,
          false, false, k, series[i].pieData.value);
        startValue = endValue;
        let bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4);
        legendData.push({
          name: series[i].name,
          value: bfb
        });
        legendBfb.push({
          name: series[i].name,
          value: bfb
        });
      }
      //(第二个参数可以设置你这个环形的高低程度)
      let boxHeight =  this.getHeight3D(series, 13);;//通过传参设定3d饼/环的高度
      if(boxHeight > 20){
        boxHeight = 20
      }
      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        //图例组件
        legend: {
          data: legendData,
          //图例列表的布局朝向。
          orient: 'horizontal',
          left: 10,
          top: 140,
          //图例文字每项之间的间隔
          itemGap: 15,
          textStyle: {
            color: '#A1E2FF',
          },
          show: false,
          icon: "circle",
          //格式化图例文本（我是数值什么显示什么）
          formatter: params => {
            let str = params.reduce((prev, cur, index) => {
              return prev + cur.marker +
                cur.seriesName + ' : ' +
                (cur.value == null ? '--' : cur.value)
            }, params[0].axisValue + '</br>');
            return str;
          }
          // 这个可以显示百分比那种（可以根据你想要的来配置）
          //   formatter: function(param) {
          //       let item = legendBfb.filter(item => item.name == param)[0];
          //       let bfs = that.fomatFloat(item.value * 100, 2) + "%";
          //       console.log(item.name)
          //       return `${item.name} :${bfs}`;
          //   }
        },
        //移动上去提示的文本内容(我没来得及改 你们可以根据需求改)
        tooltip: {
          className: 'alarm-device-tooltip',
          padding:8,
          borderRadius: 4, // 设置边框圆角半径
          formatter: params => {
            if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
              let temp = this.legends.find(e => e.color == params.color)
              return `<span class="color-text-main">${params.marker} ${temp.label}</span>
<span>
<span class="num-font-700" style="color:${temp.color}">${ temp.value }</span><span class="font-12 color-text-gray">个</span><span>
</span>
`;
            }
          },

        },
        //这个可以变形
        xAxis3D: {
          min: -1,
          max: 1
        },
        yAxis3D: {
          min: -1,
          max: 1
        },
        zAxis3D: {
          min: -1,
          max: 1
        },
        //此处是修改样式的重点
        grid3D: {
          show: false,
          boxHeight: boxHeight, //圆环的高度
          //这是饼图的位置
          top: '10',
          left: '0',
          viewControl: { //3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 30, //角度(这个很重要 调节角度的)
            distance: 180,//调整视角到主体的距离，类似调整zoom(这是整体大小)
            rotateSensitivity: 0, //设置为0无法旋转
            zoomSensitivity: 0, //设置为0无法缩放
            panSensitivity: 0, //设置为0无法平移
            autoRotate: false //自动旋转
          }
        },
        series: series
      };
      return option;
    },

    //获取3d丙图的最高扇区的高度
    getHeight3D(series, height) {
      series.sort((a, b) => {
        return (b.pieData.value - a.pieData.value);
      })
      return height * 25 / series[0].pieData.value;
    },

    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;
      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
      }
      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== 'undefined' ? k : 1 / 3;
      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;
      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20
        },
        x: function(u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function(u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function(u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * .1;
          }
          return Math.sin(v) > 0 ? 1 * h * .1 : -1;
        }
      };
    },

    //这是一个自定义计算的方法
    fomatFloat(num, n) {
      var f = parseFloat(num);
      if (isNaN(f)) {
        return false;
      }
      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂
      var s = f.toString();
      var rs = s.indexOf('.');
      //判定如果是整数，增加小数点再补0
      if (rs < 0) {
        rs = s.length;
        s += '.';
      }
      while (s.length <= rs + n) {
        s += '0';
      }
      return s;
    },
    bindListen(myChart) {
      let that = this;
      let selectedIndex = '';
      let hoveredIndex = '';
      // 监听点击事件，实现选中效果（单选）
      myChart.on('click', function(params) {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
        let isSelected = !that.option.series[params.seriesIndex].pieStatus.selected;
        let isHovered = that.option.series[params.seriesIndex].pieStatus.hovered;
        let k = that.option.series[params.seriesIndex].pieStatus.k;
        let startRatio = that.option.series[params.seriesIndex].pieData.startRatio;
        let endRatio = that.option.series[params.seriesIndex].pieData.endRatio;
        // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
        if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
          that.option.series[selectedIndex].parametricEquation = that.getParametricEquation(that.option.series[
            selectedIndex].pieData
            .startRatio, that.option.series[selectedIndex].pieData.endRatio, false, false, k, that.option.series[
            selectedIndex].pieData
            .value);
          that.option.series[selectedIndex].pieStatus.selected = false;
        }
        // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
        that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
          isSelected,
          isHovered, k, that.option.series[params.seriesIndex].pieData.value);
        that.option.series[params.seriesIndex].pieStatus.selected = isSelected;
        // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
        isSelected ? selectedIndex = params.seriesIndex : null;
        // 使用更新后的 option，渲染图表
        myChart.setOption(that.option);
      });
      // 监听 mouseover，近似实现高亮（放大）效果
      myChart.on('mouseover', function(params) {
        // 准备重新渲染扇形所需的参数
        let isSelected;
        let isHovered;
        let startRatio;
        let endRatio;
        let k;
        // 如果触发 mouseover 的扇形当前已高亮，则不做操作
        if (hoveredIndex === params.seriesIndex) {
          return;
          // 否则进行高亮及必要的取消高亮操作
        } else {
          // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
          if (hoveredIndex !== '') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
            isSelected = that.option.series[hoveredIndex].pieStatus.selected;
            isHovered = false;
            startRatio = that.option.series[hoveredIndex].pieData.startRatio;
            endRatio = that.option.series[hoveredIndex].pieData.endRatio;
            k = that.option.series[hoveredIndex].pieStatus.k;
            // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
            that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
              isSelected,
              isHovered, k, that.option.series[hoveredIndex].pieData.value);
            that.option.series[hoveredIndex].pieStatus.hovered = isHovered;
            // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
            hoveredIndex = '';
          }
          // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
          if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
            isSelected = that.option.series[params.seriesIndex].pieStatus.selected;
            isHovered = true;
            startRatio = that.option.series[params.seriesIndex].pieData.startRatio;
            endRatio = that.option.series[params.seriesIndex].pieData.endRatio;
            k = that.option.series[params.seriesIndex].pieStatus.k;
            // 对当前点击的扇形，执行高亮操作（对 option 更新）
            that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
              isSelected, isHovered, k, that.option.series[params.seriesIndex].pieData.value + 5);
            that.option.series[params.seriesIndex].pieStatus.hovered = isHovered;
            // 记录上次高亮的扇形对应的系列号 seriesIndex
            hoveredIndex = params.seriesIndex;
          }
          // 使用更新后的 option，渲染图表
          myChart.setOption(that.option);
        }
      });
      // 修正取消高亮失败的 bug
      myChart.on('globalout', function() {
        // 准备重新渲染扇形所需的参数
        let isSelected;
        let isHovered;
        let startRatio;
        let endRatio;
        let k;
        if (hoveredIndex !== '') {
          // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
          isSelected = that.option.series[hoveredIndex].pieStatus.selected;
          isHovered = false;
          k = that.option.series[hoveredIndex].pieStatus.k;
          startRatio = that.option.series[hoveredIndex].pieData.startRatio;
          endRatio = that.option.series[hoveredIndex].pieData.endRatio;
          // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
            isSelected,
            isHovered, k, that.option.series[hoveredIndex].pieData.value);
          that.option.series[hoveredIndex].pieStatus.hovered = isHovered;
          // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
          hoveredIndex = '';
        }
        // 使用更新后的 option，渲染图表
        myChart.setOption(that.option);
      });
    },

    changeType(){

    }
  }
}
</script>

<style lang="less" scoped>
.title{
  height: 40px;
  box-sizing: border-box;
  padding: 0 16px 0 10px;
}

.scale-icon{
  .scale-img{
    width: 110px;
    height: 74px;
    background: #D8D8D8;

  }
  .scale-num{
    font-size: 24px;
    line-height: 22px;
  }
}
.chart-area{
  width: 200px;
  .chart{
    z-index: 1;
  }
  .chart-bg{
    width: 200px;
    height: 93px;
    left: 50%;
    top: 65%;
    transform: translate(-50%,-50%);
    position: absolute;
    background: url("../../../assets/images/2dMap/2d-device-running-bg.png");
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 0;
  }
}
.legends{
  width: 168px;
  height: 100%;
  padding-top: 40px;
  .legend{
    width: 168px;
    height: 26px;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 10px;
    background: rgba(13, 46, 93, .4);
  }
  .info-item:last-of-type{
    margin-bottom: 0;
  }
}
.info-item{
  width: 223px;
  height: 33px;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 0 4px 0 0;

  .info-icon{
    font-size: 20px;
  }
}
.info-item:last-of-type{
  margin-bottom: 0;
}

</style>