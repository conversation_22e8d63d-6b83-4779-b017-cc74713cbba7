<!-- 天气告警弹窗内容 -->
<template>
  <div class="width-100 detail-modal-2d padding-l-16 padding-r-16 detail-modal-bg-2d">
    <div class="width-100 flex-end margin-t-8 margin-b-8">
      <div class="flex-center weather-alarm-close-icon" @click="close">
        <svg-icon icon-class="close"
                  class="font-10 margin-r-8 cursor-pointer color-text-white"
                  />
      </div>

    </div>
    <img class="weather-alarm-img" :src="alarmInfo.src" />
    <div class="flex-space-between margin-t-8 margin-b-12">
      <div class="weather-alarm-texts">
        <div class="flex-start margin-b-8 alarm-text-bg"
        :style="getAlarmTextColor()"
        >
           <span class="font-14 margin-l-16">{{ alarmInfo.text }}</span>
        </div>
        <span class="num-font-500 font-16 color-text-white">
          {{ alarmInfo.time }}
        </span>
      </div>
      <di-throttle-button
                           @click="confirmAlarm"
                           label="确认" />
    </div>
  </div>
</template>

<script>
import {affirmForSensor} from "../../../api/2dMap/psOverview";

export default {
  name: "WeatherPopup",
  props: {
    alarmInfo:{
      type:Object,
      default:()=>{
        return {
        }
      }
    }
  },
  data() {
    return {
      psaInfo:null,
      alarmColors:[
        {
          text:'红色',
          textColor:'#FFD4D4',
          bgColor:'rgba(196, 23, 23, 0.31)'
        },{
          text:'黄色',
          textColor:'#FFF8D4',
          bgColor:'rgba(255, 220, 41, 0.31)'
        },{
          text:'蓝色',
          textColor:'#C8E4FF',
          bgColor:'rgba(45, 152, 255, 0.31)'
        },{
          text:'白色',
          textColor:'#C8E4FF',
          bgColor:'rgba(45, 152, 255, 0.31)'
        },{
          text:'橙色',
          textColor:'#FFBE90',
          bgColor:'rgba(214, 96, 0, 0.31)'
        },
      ]
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  methods: {
    close(){
      this.$emit('close')
    },
    getAlarmTextColor(){
      let temp = this.alarmColors[3];
      if(this.alarmInfo.text) {
        let data = this.alarmColors.find(item => this.alarmInfo.text.includes(item.text));
         if(data){
          temp = data;
        }
      }
      return {
        color:temp.textColor,
        background:temp.bgColor
      }
    },
    confirmAlarm(){
      this.$confirm({
        title: "是否确认",
        content: '确认后该告警信息表示已完成',
        centered: true,
        onOk: async () => {
          await affirmForSensor({
            psId:this.psaInfo.psId,
            id:this.alarmInfo.id,
            handleStatus:'03'
          })
          this.$message.success("操作成功");
          this.close()
        },
      });

    },
  }
}
</script>

<style lang="less" scoped>
.weather-alarm-texts{
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  .alarm-text-bg{
    width: 231px;
    height: 31px;
    border-radius: 4px;
  }
}
.weather-alarm-img{
  width: 312px;
  height: 208px;
  border-radius: 4px;
}
.weather-alarm-close-icon{
  width: 20px;
  height: 20px;
}
</style>