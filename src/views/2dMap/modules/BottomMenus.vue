<!--  -->
<template>
  <div class="bottom-menu flex-start">
    <div v-if="showBack">
      返回
    </div>
    <div v-else class="width-100 flex-start">
      <div v-for="(item,index) in tabs" :key="index"
           class="tab-item"
           :class="{'active-tab':activeTabIndex == index}"
           @click="activeTab(index)">
        <span>{{ item.label }}</span>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  name: "BottomMenus",
  props: {
    activeTabIndex:{
      type:Number,
      default:0
    },
    showBack:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      tabs: [
        {
          label:'电站概览',
          path:''
        },{
          label:'光伏区',
          path:''
        },{
          label:'升压站',
          path:''
        }
      ],

    }
  },
  created() {
  },
  methods: {
    activeTab(index){

    },
  }
}
</script>

<style lang="less" scoped>
.active-tab{
  background: #666666;
}
.bottom-menu{
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  .tab-item{
    width: 100px;
    cursor: pointer;
  }
}
</style>