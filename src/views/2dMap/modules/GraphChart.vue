<template>
  <div class="graph-chart width-height-100">
    <a-row class="operations" :gutter="8">
      <a-col :span="5">
        <a-range-picker
          v-model="chosenRange"
          @change="rangeDateChange"
          :allowClear="true"
          :disabled-date="disabledDate"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="width-100"
        />
      </a-col>
      <a-col :span="2">
        <a-select
          class="width-100"
          :getPopupContainer="(node) => node.parentNode"
          :options="timeIntervalOptions"
          v-model="queryParams.timeInterval"
          @change="initQuery"
        />
      </a-col>
      <a-col :span="6">
        <a-select
          showArrow
          class="width-100"
          v-model="points"
          mode="multiple"
          :maxTagCount="1"
          :allowClear="true"
          placeholder="请选择点位类型"
          @change="changePoints"
          :filterOption="$filterOption"
          optionFilterProp="children"
          :getPopupContainer="(node) => node.parentNode"
        >
          <a-select-option
            v-for="item in filtersPointData"
            :key="item.point"
            :value="item.point"
            >{{ item.label }}
          </a-select-option>
        </a-select>
      </a-col>
      <a-radio-group
        v-model="dataShowType"
        style="float: right"
        @change="changeDataShowType"
      >
        <a-radio-button value="chart">
          <svg-icon
            class="filter-icon solar-eye-hover-primary cursor-pointer margin-r-4"
            iconClass="insight-chart"
          ></svg-icon>
          <span>图表</span>
        </a-radio-button>
        <a-radio-button value="table">
          <svg-icon
            class="filter-icon solar-eye-hover-primary cursor-pointer margin-r-4"
            iconClass="insight-table"
          ></svg-icon>
          <span>表格</span>
        </a-radio-button>
      </a-radio-group>
    </a-row>
    <div
      id="graph-chart"
      v-show="dataShowType == 'chart'"
      class="margin-t-16"
    ></div>
    <GraphTable v-if="dataShowType == 'table'" ref="graphTable" />
  </div>
</template>

<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import { chartBaseOptions } from "@/views/dashboard/equipment/insightTool/data";
import moment from "moment";
import { flattenDeep, initial, groupBy } from "lodash";
import { clone } from "xe-utils";
import {mapState} from "vuex";
import {
  insightToolsInsightAnalysisData,
  getModuleDataApi,
} from "@/api/dataCenter";
import { PSA_INFO } from "@/store/mutation-types";
import { mixin as chartFunctions } from "@/views/dashboard/equipment/mixins/chartFuntions";
import echarts from "@/utils/enquireEchart";
import GraphTable from "./GraphTable";
import { timeIntervalOptionsAll } from "../constants";
import { debounce,cloneDeep } from "lodash";

export default {
  name: "GraphChart",
  mixins: [chartFunctions],
  components: { GraphTable },
  props: {
    nowDetailParams: PropTypes.object,
    pointsData: PropTypes.array.def([]),
  },
  data() {
    return {
      chartBaseOptions: chartBaseOptions, // 图表基础配置
      myChart: null,
      queryParams: {
        timeInterval: null,
      },
      timeIntervalOptions: [],
      timeIntervalOptionsAll: timeIntervalOptionsAll,
      chosenRange: [
        // 选择日期范围
        moment().format("YYYY-MM-DD"),
        moment().format("YYYY-MM-DD"),
      ],
      points: [], // 测点类型
      unit: [], // 所选择的单位执行栈
      chartBaseData: [], // 图表数据
      dataShowType: "chart", // 数据展示类型
      tableData: [], // 表格数据
      columnData: [], // 表格表头
    };
  },
  created() {
    this.timeIntervalOptions = cloneDeep(this.timeIntervalOptionsAll.day)
    // 是否开启1min频率
    const setValue = this.systemConfigMap?.configDataFrequency?.setValue;
    if(setValue != "1") this.timeIntervalOptions.splice(0, 1);
    this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
  },
  mounted() {
    this.initChart(); // 初始化图表
    //   根据是否是组件判断需要渲染什么图表
    this.initQuery();
  },
  computed: {
    // 主题颜色
    navTheme() {
      return this.$store.state.app.theme;
    },
    // 判断是否是组件
    isModule() {
      return this.nowDetailParams.deviceType == 58;
    },
    psId() {
      return this.$ls.get(PSA_INFO).psId;
    },
    psName() {
      return this.$ls.get(PSA_INFO).psaNameList[0];
    },
    filtersPointData() {
      return this.pointsData.filter(item=>item.pointType == 2)
    },
    ...mapState({
      systemConfigMap: state => state.app.systemConfigMap
    })
  },
  methods: {
    // 初始化
    initChart() {
      if (this.myChart == null) {
        let chartDom = document.getElementById("graph-chart");
        this.myChart = echarts.init(chartDom);
        this.unitArr = [];
        const options = this.chartBaseOptions;
        options && this.myChart.setOption(options);
        this.myChart.on("datazoom", () => {
          this.myChart.dispatchAction({
            type: "hideTip",
          });
        });
        this.$once("hook:beforeDestroy", function () {
          echarts.dispose(this.myChart);
        });
      }
    },
    // 根据是否是组件判断需要渲染什么图表
    async initQuery() {
      await this.getChartData();
      if (this.isModule) {
        this.renderModuleChart();
      } else {
        this.renderNotModuleChart();
      }
      // 如果当前展示类型是表格，需要处理表格数据
      if (this.dataShowType == "table") {
        this.resolveTableData();
      }
    },
    // 日期切换逻辑
    rangeDateChange(val) {
      if (val && val.length > 0) {
        let dayInterval = moment(val[1]).diff(val[0], "day");
        if (dayInterval === 0) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
          const enableOneMin = this.systemConfigMap?.configDataFrequency?.setValue != "1";
          if(enableOneMin) this.timeIntervalOptions.day.splice(0, 1);
        } else if (dayInterval >= 1 && dayInterval < 10) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
        } else if (dayInterval >= 10 && dayInterval <= 31) {
          this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
        } else {
          this.$message.info("日期范围最多一个月");
          this.queryParams.startTime = moment(
            moment(val[1]).subtract(1, "month").calendar()
          ).format("YYYY-MM-DD");
          this.queryParams.endTime = moment(val[1]).format("YYYY-MM-DD");
          this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
          this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
          return;
        }
        this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
        this.queryParams.startTime = moment(val[0]).format("YYYY-MM-DD");
        this.queryParams.endTime = moment(val[1]).format("YYYY-MM-DD");
      } else {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        const enableOneMin = this.systemConfigMap?.configDataFrequency?.setValue != "1";
        if(enableOneMin) this.timeIntervalOptions.day.splice(0, 1);
        this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
        this.queryParams.startTime = moment().format("YYYY-MM-DD");
        this.queryParams.endTime = moment().format("YYYY-MM-DD");
      }
      this.initQuery();
    },
    // 测点切换逻辑
    changePoints: debounce(function () {
      // 更新Y轴单位
      this.unit = this.pointsData.filter((item) =>
        this.points.includes(item.point)
      );
      this.initQuery();
    }, 500),
    // 渲染组件图表
    renderModuleChart() {
      const InferentialDataInfo = clone(this.chartBaseData, true);
      const newOptions = clone(this.chartBaseOptions, true);
      // 计算最新两个测点当作显示的y轴
      const len = this.unit.length;
      const last = this.unit[len - 1]; // 从单位执行站中选出倒数第一个
      const penultimate = this.unit[len - 2]; // 从单位执行站中选出倒数第二个
      // console.log('unit', this.unit);
      const yUnit = [penultimate, last].reduce((acc, cur) => {
        if (!this.$isEmpty(cur)) acc.push(cur);
        return acc;
      }, []);
      // 克隆出一份新内存，重新组装测点排序数据
      const clonedUnit = clone(this.unit, true);
      const filterUnit = clonedUnit.slice(-2) || [];
      // 当前Y轴所显示的测点
      const yUnitByPoint = yUnit.map((item) => item.point);
      // 排除yUnit以外的数据
      const oddData = clonedUnit.filter(
        (item) => !yUnitByPoint.includes(item.point)
      );
      // 然后根据最新选择的两个测点排在前面
      const newUnit = [...filterUnit, ...oddData];
      const xData = InferentialDataInfo.map((item) => item.time);
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      // 取最新的两个作为Y轴单位
      if (this.$isEmpty(yUnit)) {
        newOptions.yAxis = this.makeYAxis(0, { name: "", type: "value" }, true);
      } else {
        const leftAndRightYAxis = filterUnit.map((item, index) => {
          return this.makeYAxis(
            0,
            {
              // name: legendList.find(legendItem => legendItem.point == item.point).unit,
              type: "value",
              yAxisIndex: index,
              alignTicks: true,
            },
            true
          );
        });
        const restYAxis = oddData.map((item, index) => {
          return this.makeYAxis(
            0,
            {
              // name: legendList.find(legendItem => legendItem.point == item.point).unit,
              type: "value",
              show: false,
              yAxisIndex: index + leftAndRightYAxis.length,
              alignTicks: true,
            },
            true
          );
        });
        newOptions.yAxis = [...leftAndRightYAxis, ...restYAxis];
      }
      const GroupDataByPoints = InferentialDataInfo.reduce((acc, cur) => {
        Object.keys(cur).forEach((item) => {
          if (!this.$isEmpty(item) && item != "time") {
            if (acc[item]) {
              acc[item].push(cur[item]);
            } else {
              acc[item] = [];
            }
          }
        });
        return acc;
      }, {});
      newOptions.series = this.points.map((item, index) => {
        const findItem = this.pointsData.find((o) => o.point == item);
        return this.makeSeries(
          0,
          newUnit.findIndex((o) => o.unit == findItem.unit),
          {
            name: findItem.label,
            data: GroupDataByPoints[item].map((o) => ({
              ...findItem,
              value: o,
            })),
          },
          "line"
        );
      });
      Object.assign(newOptions.tooltip, {
        formatter: (params) => {
          let customerHtml = "";
          customerHtml += `<div>${params[0].axisValue}</div>`;
          params.forEach((item) => {
            customerHtml += `<div>${item.marker}${
              item.seriesName
            }: ${this.getLabel(item.value, null)} ${
              item.data.unit ? "(" + item.data.unit + ")" : ""
            }</div>`;
          });
          return customerHtml;
        },
      });
      // 图表grid边距限制
      newOptions.grid = { left: "10%", right: "10%" };
      // 图例数据
      newOptions.legend.data = this.pointsData
        .filter((item) => this.points.includes(item.point))
        .map((o) => o.label);
      // 单个y轴时 图表占满 多y轴固定高度
      let height = $(".graph-chart").height() - 56;
      this.myChart.resize({ height });
      // 处理图表颜色
      const isDarkColor =
        this.navTheme == "dark" ? "rgba(255,255,255,.7)" : "#333";
      newOptions.legend.textStyle.color = isDarkColor;
      newOptions.legend.pageIconColor = isDarkColor;
      newOptions.legend.pageTextStyle.color = isDarkColor;
      newOptions.legend.itemGap = 16;
      newOptions.legend.itemWidth = 14;
      newOptions.legend.itemHeight = 14;
      // dataZoom
      newOptions.dataZoom[0].xAxisIndex = xData.map((_, index) => index);
      newOptions.dataZoom[1].xAxisIndex = xData.map((_, index) => index);
      console.log("newOptions", newOptions);
      this.myChart.setOption(newOptions, true);
    },
    // 渲染非组件图表
    renderNotModuleChart() {
      // 当前状态是否为Y轴平铺状态
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData } = this.chartBaseData;

      // 所有需要画的图例
      const legendList = flattenDeep(
        yData.map((item) => {
          return item.data.map((secondItem) => {
            return secondItem.data.map((_, thirdIndex) => {
              return {
                name: `${item.psName}/${secondItem.title}/${secondItem.pointName[thirdIndex]}`,
                point: secondItem.point[thirdIndex],
                pointName: secondItem.pointName[thirdIndex],
                unit: secondItem.unit[thirdIndex],
              };
            });
          });
        })
      );
      // 由于测点单位可能会和响应的数据的单位不一致，所以需要根据响应的数据来更新手动选择测点的单位
      this.unit.forEach((item) => {
        const findItem = legendList.find((o) => o.point == item.point) || {};
        item.unit = findItem.unit;
      });
      // 计算最新两个测点当作显示的y轴
      const len = this.unit.length;
      const last = this.unit[len - 1]; // 从单位执行站中选出倒数第一个
      const penultimate = this.unit[len - 2]; // 从单位执行站中选出倒数第二个
      // console.log('unit', this.unit);
      const yUnit = [penultimate, last].reduce((acc, cur) => {
        if (!this.$isEmpty(cur)) acc.push(cur);
        return acc;
      }, []);
      // 克隆出一份新内存，重新组装测点排序数据
      const clonedUnit = clone(this.unit, true);
      const filterUnit = clonedUnit.slice(-2) || [];
      // 当前Y轴所显示的测点
      const yUnitByPoint = yUnit.map((item) => item.point);
      // 排除yUnit以外的数据
      const oddData = clonedUnit.filter(
        (item) => !yUnitByPoint.includes(item.point)
      );
      // 然后根据最新选择的两个测点排在前面
      const newUnit = [...filterUnit, ...oddData];

      // 将数据扁平，组装成一维数组
      const flattenList = flattenDeep(
        yData.map((item) => {
          return item.data.map((secondItem) => {
            return secondItem.data.map((thirdItem, thirdIndex) => {
              return {
                name: `${item.psName}/${secondItem.title}/${secondItem.pointName[thirdIndex]}`,
                psName: item.psName, // 电站名称
                title: secondItem.title, // 设备名称
                unit: secondItem.unit[thirdIndex], // 单位
                point: secondItem.point[thirdIndex], // 测点
                pointName: secondItem.pointName[thirdIndex], // 测点名称
                deviceType: item.deviceType, // 设备类型
                psKey: secondItem.psKey, // 设备唯一标识
                data: thirdItem,
              };
            });
          });
        })
      );
      // 筛选出最后选择的测点所对应的数据
      const filterFlattenList = flattenList.filter((item) =>
        yUnitByPoint.includes(item.point)
      );
      // 从原数据中去除这两个测点的数据
      const oddFlattenList = flattenList.filter(
        (item) => !yUnitByPoint.includes(item.point)
      );
      // 系列数据
      newOptions.series = [...filterFlattenList, ...oddFlattenList].map(
        (item, index) => {
          return this.makeSeries(
            0,
            newUnit.findIndex((o) => o.unit == item.unit),
            {
              name: `${item.psName}/${item.title}/${item.pointName}`,
              data: item.data.map((o) => ({
                value: o,
                psName: item.psName, // 电站名称
                title: item.title, // 设备名称
                unit: item.unit, // 单位
                point: item.point, // 测点
                pointName: item.pointName, // 测点名称
                deviceType: item.deviceType, // 设备类型
                psKey: item.psKey, // 设备唯一标识
              })),
            },
            "line"
          );
        }
      );

      // 取最新的两个作为Y轴单位
      if (this.$isEmpty(yUnit)) {
        newOptions.yAxis = this.makeYAxis(0, { name: "", type: "value" }, true);
      } else {
        const leftAndRightYAxis = filterUnit.map((item, index) => {
          return this.makeYAxis(
            0,
            {
              // name: legendList.find(legendItem => legendItem.point == item.point).unit,
              type: "value",
              yAxisIndex: index,
              alignTicks: true,
            },
            true
          );
        });
        const restYAxis = oddData.map((item, index) => {
          return this.makeYAxis(
            0,
            {
              // name: legendList.find(legendItem => legendItem.point == item.point).unit,
              type: "value",
              show: false,
              yAxisIndex: index + leftAndRightYAxis.length,
              alignTicks: true,
            },
            true
          );
        });
        newOptions.yAxis = [...leftAndRightYAxis, ...restYAxis];
      }
      // x轴数据
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      // tooltip
      newOptions.tooltip = {
        className: "solar-eye-tooptip deep-anly-tooptip",
        triggerOn: "mousemove",
        enterable: true,
        confine: true,
        trigger: "axis",
        alwaysShowContent: false,
        appendToBody: true,
        position: (point, params, dom, rect, size) => {
          let content = document.getElementById("graph-chart");
          let minTop = content.scrollTop;
          let maxTop =
            content.scrollTop + content.offsetHeight - dom.offsetHeight - 30;
          let minLeft = 0;
          let maxLeft = content.offsetWidth - dom.offsetWidth;
          let top = 0;
          let left = 0;
          if (minTop <= point[1] && point[1] <= maxTop) {
            top = point[1];
          } else if (point[1] < minTop) {
            top = minTop;
          } else {
            top = maxTop;
          }
          if (minLeft < point[0] + 20 && point[0] + 20 <= maxLeft) {
            left = point[0] + 20;
          } else {
            left = point[0] - dom.offsetWidth - 20;
          }
          return { top: top, left: left };
        },

        formatter: (params) => {
          const totalData = params.map((item) => ({
            ...item.data,
            marker: item.marker,
          }));
          const groupMap = groupBy(totalData, "psName");
          const newGroupMap = Object.keys(groupMap).reduce((acc, cur) => {
            // 把对应的设备名归类
            acc[cur] = groupBy(groupMap[cur], "title");
            return acc;
          }, {});
          let customerHtml = "";
          customerHtml += `<div style="display: flex">`;
          for (const key in newGroupMap) {
            customerHtml += `<div style="display: flex;margin-right: 16px">`;
            for (const item in newGroupMap[key]) {
              customerHtml += "<div>";
              if (Object.keys(groupMap).length > 1) {
                customerHtml += `<div class="ps-name">${key}</div>`;
              }
              // customerHtml += `<div class="device-item">${item}</div>`;
              newGroupMap[key][item].forEach((every) => {
                // 如果是电表类型数据保留4位有效数字，其他两位
                const value = this.$isEmpty(every.value)
                  ? "--"
                  : parseFloat(every.value).toFixed(
                      every.deviceType == 7 ? 4 : 2
                    );
                customerHtml += `
                <div class="point-item">
                  ${every.marker}
                  ${every.pointName}: ${value} ${
                  every.unit ? "(" + every.unit + ")" : ""
                }
                </div>`;
              });
              customerHtml += `</div>`;
            }
            customerHtml += `</div>`;
          }
          customerHtml += `</div>`;
          const axisValue = params[0].axisValue;
          return `
              <div>
                <span>${axisValue}</span>
                ${customerHtml}
              </div>
            `;
        },
      };
      // 图表grid边距限制
      newOptions.grid = { left: "10%", right: "10%" };
      // 图例数据
      newOptions.legend.data = legendList;
      // 单个y轴时 图表占满 多y轴固定高度
      let height = $(".graph-chart").height() - 56;
      // dataZoom
      newOptions.dataZoom[0].xAxisIndex = xData.map((_, index) => index);
      newOptions.dataZoom[1].xAxisIndex = xData.map((_, index) => index);
      this.myChart.resize({ height });
      // 处理图表颜色
      const isDarkColor =
        this.navTheme == "dark" ? "rgba(255,255,255,.7)" : "#333";
      newOptions.legend.textStyle.color = isDarkColor;
      newOptions.legend.itemGap = 16;
      newOptions.legend.itemWidth = 14;
      newOptions.legend.itemHeight = 16;
      newOptions.legend.pageIconColor = isDarkColor;
      newOptions.legend.pageTextStyle.color = isDarkColor;
      console.log("newOptions", newOptions);
      this.myChart.setOption(newOptions, true);
    },
    // 日期区间限制
    disabledDate(current) {
      // 不能选择今天以后的
      return current && current > moment().endOf("day");
    },
    // 获取图表数据
    async getChartData() {
      let [startTime, endTime] = this.chosenRange;
      if (this.$isEmpty(this.chosenRange)) {
        startTime = moment().format("YYYY-MM-DD");
        endTime = moment().format("YYYY-MM-DD");
      }
      const { timeInterval } = this.queryParams;
      // 组件请求
      if (this.isModule) {
        const splitKeys = this.nowDetailParams.psKey.split("_");
        const psKey = initial(splitKeys).join("_");
        const params = {
          psKey,
          recordStartDate: startTime,
          recordEndDate: endTime,
          psId: this.psId,
          modelName: "InferentialDataInfo",
          type: timeInterval,
        };
        if (!this.$isEmpty(this.points)) {
          params.points = this.points.join(",");
        }
        const res = await getModuleDataApi(params);
        const {
          dataResult: { InferentialDataInfo },
        } = res.result_data;
        this.chartBaseData = InferentialDataInfo;
      } else {
        // 非组件请求
        const { deviceType, psKey } = this.nowDetailParams;
        let newPsKey = clone(psKey, true);
        // 如果是组串，特殊处理
        if (deviceType == "10") {
          const splitKeys = psKey.split("_");
          newPsKey = initial(splitKeys).join("_");
        }

        const params = {
          startTime,
          endTime,
          list: [
            {
              deviceType,
              pointList: this.pointsData
                .filter((item) => this.points.includes(item.point))
                .map((item, index) => ({
                  dataSort: index + 1,
                  deviceType,
                  key: item.point,
                  point: item.point,
                  pointName: item.label,
                  psKeys: newPsKey,
                  unit: item.unit,
                })),
              psId: this.psId,
              psKeys: [newPsKey],
              psName: this.psName,
            },
          ],
          timeInterval
        };
        const res = await insightToolsInsightAnalysisData(params);
        this.chartBaseData = res.result_data;
      }
    },
    //   数据显示类型切换
    async changeDataShowType() {
      await this.$nextTick();
      if (this.dataShowType == "table") {
        this.resolveTableData();
      } else {
        this.myChart.resize();
      }
    },
    // 处理数据
    resolveTableData() {
      if (this.isModule) {
        const firstObj = this.chartBaseData[0] || {};
        this.columnMap = Object.keys(firstObj).reduce(
          (acc, cur) => {
            if (!this.$isEmpty(cur) && cur != "time") {
              const findItem =
                this.pointsData.find((item) => item.point == cur) || {};
              acc[cur] = findItem.label;
            }
            return acc;
          },
          { time: "时间" }
        );
        console.log("columnMap", this.columnMap);
        this.tableData = clone(this.chartBaseData, true);
      } else {
        if (this.chartBaseData.yData && this.chartBaseData.yData.length) {
          // 电站名称列表
          let psNameList = Array.from(
            new Set(this.chartBaseData.yData.map((item) => item.psName))
          );
          // 相同电站集合
          let psList = {};
          psNameList.forEach((item) => {
            let obj = this.chartBaseData.yData.filter(
              (list) => list.psName === item
            );
            psList[item] = obj;
          });
          // 拼表头 拼数据
          this.disposeTableHeader(psList, this.chartBaseData.xData);
        } else {
          this.tableData = []; // 数据
          this.columnMap = []; // 表头
        }
      }
      this.$refs.graphTable.init(this.columnMap, this.tableData);
    },
    disposeTableHeader(data, xData) {
      this.columnMap = {
        time: "时间",
      };
      this.tableData = xData.map((item) => {
        return {
          time: item,
        };
      });

      let index = 0; // 标题名称
      for (let key in data) {
        let psName = key;
        if (data[key] && data[key].length) {
          data[key].forEach((item) => {
            if (item.data && item.data.length) {
              item.data.forEach((every) => {
                if (every.pointName && every.pointName.length) {
                  every.pointName.forEach((list, sort) => {
                    let name = `name${index}`;
                    let obj = {};
                    this.$set(
                      obj,
                      name,
                      psName +
                        "/" +
                        every.title +
                        "/" +
                        list +
                        "(" +
                        every.unit[sort] +
                        ")"
                    );
                    this.columnMap = Object.assign({}, this.columnMap, obj);
                    index++;
                    if (every.data && every.data.length) {
                      // 处理表体
                      this.disposeDataSource(name, every.data[sort]);
                    }
                  });
                }
              });
            }
          });
        }
      }
    },
    // 处理表体
    disposeDataSource(name, data) {
      this.tableData = this.tableData.map((item, index) => {
        let obj = item;
        // this.$set(obj, name, data[index]);
        obj[name] = data[index];
        return obj;
      });
    },
  },
};
</script>

<style scoped lang="less">
.graph-chart {
  color: @di-color-text-white;
}
</style>

<style lang="less">
.deep-anly-tooptip {
  max-width: 600px !important;
  max-height: 500px !important;
  overflow: auto;
}
</style>
