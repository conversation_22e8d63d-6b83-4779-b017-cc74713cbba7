<!-- 复检结果弹窗 -->
<template>
  <a-spin :spinning="loading" class="width-100" style="max-height: 605px;">
    <div class="height-100 width-100 detail-modal-2d detail-modal-bg-2d recheck-result ">
      <modal-title title="复检结果" class="modal-title">
        <div slot="right" class="flex-end">
          <svg-icon icon-class="close"
                    class="font-10 margin-r-8 cursor-pointer color-text-white"
                    @click="close"
          />
        </div>
      </modal-title>

      <div class="width-100 detail-content-area">
        <div class="flex-column width-100">
          <div class="detail-data-item width-100 flex-space-between item-bg">
            <div class="color-text-main lable">复检时间</div>
            <div class="color-text-white value">{{ data.executeTime }}</div>
          </div>
        </div>
        <div class="detail-data-item width-100 flex-space-between item-bg margin-t-8">
          <div class="color-text-main lable">组串位置</div>
          <div class="color-text-white value">{{ data.deviceName }}</div>
        </div>
        <div class="fault-list-content" v-if="data.taskStatus == 'finish'">
          <template v-if="faultDatas.length > 0">
            <div v-for="item in faultDatas" :key="item.id">
              <div class="split-line"></div>
              <div class="color-text-main maring-b-8">{{ item.faultTypeName + '(' + item.faultNo + ')'}}</div>
              <div class="flex-start flex-column width-100">
                <div class="flex-space-between">
                  <img v-if="item.infraredThumbnailPathName" class="fault-img" :src="item.infraredThumbnailPathName"  @click="previewImage(item, 0)"/>
                  <div v-else class="no-img-div div-center">
                    <img class="bg-img" :src="require('@/assets/images/error-image.png')">
                  </div>
                  <img v-if="item.visibleThumbnailPathName" class="fault-img" :src="item.visibleThumbnailPathName"  @click="previewImage(item, 1)"/>
                  <div v-else class="no-img-div div-center">
                    <img class="bg-img" :src="require('@/assets/images/error-image.png')">
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="split-line"></div>
            <div class="process-img-div">
              <img class="process-img" :src="getPng('normal')">
            </div>
          </template>
        </div>
        <div v-else-if="data.taskStatus == 'error'">
          <div class="split-line maring-b-12"></div>
          <div class="process-img-div error-img-div">
            <img class="process-img" :src="getPng('error')">
          </div>
          <div class="detail-data-item width-100 flex-space-between item-bg error-div">
            <div class="color-text-main lable">失败原因</div>
            <div class="color-text-white value">{{ data.errMsg }}</div>
          </div>
        </div>
        <template v-else>
          <div class="split-line"></div>
          <div class="process-img-div">
            <img v-if="data.flowSts" class="process-img" :src="getPng(getImageName(data.flowSts))">
          </div>
        </template>
      </div>
    </div>
  </a-spin>
</template>

<script>
import ModalTitle from "./ModalTitle";
import { getFaultList } from '@/api/2dMap/map';
import VueViewer from '@/mixins/VueViewer';
export default {
  name: "DeviceDetailPopup",
  components: { ModalTitle },
  mixins: [VueViewer],
  props: {},
  data() {
    return {
      loading: false,
      data: {},
      faultDatas:[]
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    async initData(data) {
      this.data = data;
      this.$forceUpdate();
      if(data.taskStatus == 'finish') {
        this.loading = true;
        this.faultDatas = [];
        getFaultList({taskId: data.taskId, currentPage: 1, pageSize: 100 }).then(res => {
          if(res.payload && res.payload.results && res.payload.results.length > 0) {
            this.faultDatas = res.payload.results;
          }
          this.loading = false;
        }).catch(() => { 
          this.loading = false;
        });
      }
    },
    getImageName (flowSts) {
      switch (flowSts) {
        case '0':
          return 'undo';
        case '1':
          return 'check';
        case '2':
          return 'upload';
        case '3':
          return 'diagnosis';
      }
    },
    previewImage(data, index) {   
      let images = [{ path: data.infraredPathName }, { path: data.visiblePathName }];
      if (!images[index].path) {
        return;
      } else if (index == 1 && !images[0].path) {
        images = [{ path: data.visiblePathName }];
        index = 0;
      }
      this.viewerImage({ images: images, index: index });
    },
    // 获取图片
    getPng (name) {
      return require(`@/assets/images/2dMap/uav/${name}.png`)
    }
  }
}
</script>

<style lang="less" scoped>
.recheck-result{
  width: 340px;
}
.fault-list-content {
  overflow: auto;
  padding-right: 12px;
  width: 324px;
  max-height: 287px;
  min-height: 140px;
}

.fault-img{
  width: 149px;
  height: 85px;
  border-radius: 4px;
  margin: 0 4px 0 4px;
}
.modal-title {
  margin: 4px 2px 0;
  width: calc(100% - 8px);
}
.split-line{
  height: 0px;
  border:1px dashed #376DA2;
  margin: 12px 0 6px;
}

.detail-data-item {
  background: #0F3B78;
  padding: 4px 8px;
  .lable {
    width: 64px;
  }
  .value {
    width: 216px;
    text-align: right;
  }
}

// .detail-data-item:last-of-type {
//   margin-bottom: 0;
// }

.detail-btn {
  padding: 0 8px;
  height: 24px;
  border-radius: 3px;
}

.detail-content-area {
  padding: 8px 16px 16px;
  height: calc(100% - 34px);
  max-height: 560px;
}

.title {
  width: 138px;
  height: 30px;
  background: url("../../../assets/images/2dMap/2d-booster-station-room-title-bg.png");
  background-size: cover;
}
.error-div {
  margin: 0 0 38px;
  .flowsts {
    text-align: center;
  }
}
.maring-b-8 {
  margin-bottom: 8px;
}
.maring-b-12 {
  margin-bottom: 12px;
}

.no-img-text {
  position: absolute;
  bottom: 4px;
  font-size: 12px;
  color: @di-color-text-gray;
}
.no-img-div {
  width: 150px;
  height: 85px;
  position: relative;
  background: #113871;
  border-radius: 4px;
  margin: 0 4px 0 4px;
  .bg-img {
    width: 46px;
    height: 40px;
  }
}

.process-img-div {
  padding: 60px 0 24px;
  margin: 0 -6px;
  .process-img {
    width: 320px;
    height: 183px;
  }
}
.error-img-div {
  padding: 4px 0 0;
}
</style>