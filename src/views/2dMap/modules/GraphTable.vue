<template>
  <div>
    <a-row type="flex" justify="end">
      <DiThrottleButton
        label="导出"
        @click="exportExcel"
        class="di-cancel-btn margin-t-12"
      />
    </a-row>
    <a-spin :spinning="loading">
      <vxe-table
        class="my-table margin-t-12"
        ref="xTable"
        :data="dataSource"
        resizable
        show-overflow
        border="none"
        :height="tableHeight"
        width="100%"
      >
        <vxe-table-column
          v-for="(item, index) in Object.keys(columnMap)"
          :key="item"
          :field="item.key"
          :title="columnMap[item]"
          min-width="20%"
          show-overflow="title"
          :fixed="index == 0 ? 'left' : null"
        >
          <template #default="{ row }">
            <span>{{ row[item] || "--" }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </a-spin>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import moment from "moment";

export default {
  name: "GraphTable",
  props: {},
  data() {
    return {
      dataSource: [],
      columnMap: {},
      loading: false,
    };
  },
  computed: {
    tableHeight() {
      return $(".graph-chart").height() - 100;
    },
  },
  methods: {
    init(columnMap, tableData) {
      this.loading = true;
      setTimeout(() => {
        this.dataSource = Object.freeze(tableData);
        this.columnMap = Object.freeze(columnMap);
        this.loading = false;
      }, 500);
    },
    exportExcel() {
      const tHeads = Object.values(this.columnMap);
      const tHeadKeys = Object.keys(this.columnMap);
      const tBodys = this.dataSource.reduce((acc, cur) => {
        const itemBody = tHeadKeys.reduce((inner, innerKey) => {
          inner.push(cur[innerKey]);
          return inner;
        }, []);
        acc.push(itemBody);
        return acc;
      }, []);
      const aoa = [tHeads, ...tBodys];
      var ws = XLSX.utils.aoa_to_sheet(aoa);
      /* create workbook and export */
      var wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      const currentDate = moment().format("YYYY年MM月DD日HH时mm分");
      XLSX.writeFile(wb, `${currentDate}.xlsx`);
    },
  },
};
</script>

<style scoped lang="less"></style>
