<!--光功率预测柜/今日预测曲线-->
<template>
  <div class="seven-day-chart"></div>
</template>
<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import echarts from "@/utils/enquireEchart";
import { itemStyleColor } from "@/views/analysis/device/config";
import ChartResizeMixins from "@/views/analysis/mixins/ChartResizeMixins";

export default {
  name: "SevenDayChart",
  props: {
    dataResult: PropTypes.array.def([]),
  },
  mixins: [ChartResizeMixins],
  data() {
    return {
      myChart: null,
      tipsData: [
        {
          key: "shortSevenPrecision",
          title: "短期7点精度",
          multiple: 1,
          toFixed: 1,
        },
        {
          key: "shortTwelvePrecision",
          title: "短期12点精度",
          multiple: 1,
          toFixed: 1,
        },
        {
          key: "superShortPrecision",
          title: "超短期精度",
          multiple: 1,
          toFixed: 1,
        },
        {
          key: "shortUploadRate",
          title: "短期上传率",
          multiple: 1,
          toFixed: 1,
        },
        {
          key: "superShortUploadRate",
          title: "超短期上传率",
          multiple: 1,
          toFixed: 1,
        },
      ],
    };
  },
  mounted() {
    const el = document.querySelector(".seven-day-chart");
    this.myChart = echarts.init(el);
    this.$once("hook:beforeDestroy", () => {
      echarts.dispose(this.myChart);
    });
  },
  watch: {
    dataResult: {
      handler() {
        this.myChart?.clear();
        const option = this.getOption() ?? {};
        this.myChart?.setOption(option, true);
      },
      deep: true,
    },
  },
  methods: {
    getOption() {
      return {
        grid: {
          left: "5%",
          right: "5%",
          top: "10%",
          bottom: "10%",
        },
        legend: {
          data: this.tipsData.map((item) => item.title),
          textStyle: {
            color: "rgba(255, 255, 255, 1)",
          },
        },
        tooltip: {
          trigger: "axis",
          triggerOn: "mousemove|click",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: itemStyleColor,
            },
          },
          className: "di-chart-tooltip",
          borderColor: "rgba(189, 226, 255, 0.4)",
          borderWidth: 1,
          formatter: (params) => {
            const newParams = params.filter((item) => item.axisIndex !== 1);
            let html = `<div class="title font-14">${newParams[0].axisValue}</div>`;
            html += '<div class="content">';
            newParams.forEach((item) => {
              html += `
                  <div  class="content-item">
                    <div class="flex-start">
                      ${item.marker}
                      <span class="tooltip-item-label">${
                        item.seriesName
                      }：</span>
                    </div>
                    <span>
                      <span class="color-text-white num-font-500">${
                        item.value?.toFixed(item.data?.toFixed ?? 2) ?? "--"
                      }</span>
                      <span class="content-unit">%</span>
                    </span>
                  </div>
              `;
            });
            html += "</div>";
            return html;
          },
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#8DCBFB",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(51, 98, 159, 0.4)",
            },
          },
          axisTick:{
            lineStyle:{
              color:"rgba(255, 255, 255, 0.7)",
              width: 2
            }
          },
          data: this.dataResult?.map((item) => item.time),
        },
        yAxis: [
          {
            type: "value",
            axisLabel: { color: "#8DCBFB" }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
            splitLine: {
              lineStyle: {
                color: "rgba(97, 161, 213, 0.19)",
              },
            },
            name: "%",
            nameTextStyle: {
              color: "#8DCBFB",
              padding: [0, 0, 0, -25],
            },
          },
        ],
        series: this.tipsData.map((item) => ({
          name: item.title,
          type: "bar",
          barCategoryGap: 60,
          // barWidth: 20,
          data: this.dataResult?.map((innerItem) => ({
            value: innerItem[item.key],
            toFixed: item.toFixed,
            multiple: item.multiple,
          })),
        })),
      };
    },
  },
};
</script>

<style scoped lang="less">
.seven-day-chart {
  width: 100%;
  height: 400px;
}
</style>
