<!--光功率预测柜/今日预测曲线-->
<template>
  <div class="ck-realtime-chart"></div>
</template>
<script>
import PropTypes from "ant-design-vue/es/_util/vue-types";
import echarts from "@/utils/enquireEchart";
import { itemStyleColor } from "@/views/analysis/device/config";
import ChartResizeMixins from "@/views/analysis/mixins/ChartResizeMixins";

export default {
  name: "CkRealTimeChart",
  props: {
    dataResult: PropTypes.array.def([]),
  },
  mixins: [ChartResizeMixins],
  data() {
    return {
      myChart: null,
      tipsData: [
        {
          key: "power",
          title: "并网功率",
          multiple: 1,
          toFixed: 2,
        },
        {
          key: "shortSeven",
          title: "短期07点30点预测",
          multiple: 1,
          toFixed: 2,
        },
        {
          key: "shortTwelve",
          title: "短期12点30分预测",
          multiple: 1,
          toFixed: 2,
        },
        {
          key: "superShortFifteen",
          title: "超短期15分钟",
          multiple: 1,
          toFixed: 2,
        },
        {
          key: "superShortTwoHundredAndForty",
          title: "超短期240分钟",
          multiple: 1,
          toFixed: 2,
        },
      ],
    };
  },
  watch:{
    dataResult: {
      handler(val) {
        this.myChart?.clear();
        const option = this.getOption() ?? {};
        this.myChart?.setOption(option, true);
      },
      deep: true,
    }
  },
  mounted() {
    const el = document.querySelector(".ck-realtime-chart");
    this.myChart = echarts.init(el);
    this.$once("hook:beforeDestroy", () => {
      echarts.dispose(this.myChart);
    });
  },
  methods: {
    getOption() {
      return {
        grid: {
          left: "5%",
          right: "5%",
          top: "10%",
          bottom: "10%",
        },
        legend: {
          data: this.tipsData.map((item) => item.title),
          textStyle: {
            color: "rgba(255, 255, 255, 1)",
          },
        },
        tooltip: {
          trigger: "axis",
          triggerOn: "mousemove|click",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: itemStyleColor,
            },
          },
          className: "di-chart-tooltip",
          borderColor: "rgba(189, 226, 255, 0.4)",
          borderWidth: 1,
          formatter: (params) => {
            const newParams = params.filter((item) => item.axisIndex !== 1);
            let html = `<div class="title font-14">${newParams[0].axisValue}</div>`;
            html += '<div class="content">';
            newParams.forEach((item) => {
              html += `
                  <div  class="content-item">
                    <div class="flex-start">
                      ${item.marker}
                      <span class="tooltip-item-label">${
                        item.seriesName
                      }：</span>
                    </div>
                    <span>
                      <span class="color-text-white num-font-500">${
                        item.value?.toFixed(item.data?.toFixed ?? 2) ?? "--"
                      }</span>
                      <span class="content-unit">MW</span>
                    </span>
                  </div>
              `;
            });
            html += "</div>";
            return html;
          },
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#8DCBFB",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(51, 98, 159, 0.4)",
            },
          },
          data: this.dataResult?.map((item) => item.time),
        },
        yAxis: [
          {
            type: "value",
            axisLabel: { color: "#8DCBFB" }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
            splitLine: {
              lineStyle: {
                color: "rgba(97, 161, 213, 0.19)",
              },
            },
            name: "MW",
            nameTextStyle: {
              color: "#8DCBFB",
              padding: [0, 0, 0, -25],
            },
          },
        ],
        series: this.tipsData.map((item) => ({
          name: item.title,
          type: "line",
          large: true,
          smooth: true,
          largeThreshold: 500,
          data: this.dataResult?.map((innerItem) => ({
            value: innerItem[item.key],
            toFixed: item.toFixed,
            multiple: item.multiple,
          })),
        })),
      };
    },
  },
};
</script>

<style scoped lang="less">
.ck-realtime-chart {
  width: 100%;
  height: 400px;
}
</style>
