<template>
  <a-spin :spinning="loading">
    <div
      class="image-marker-container"
      ref="container"
      :class="{
        'image-marker-container-not-all': pageType !== 'PAGE_TYPE_OVERVIEW'
      }"
      @wheel.prevent="handleWheel"
    >
      <div class="image-wrapper" :style="imageWrapperStyle" @mousedown="startDrag" ref="wrapper">
        <img
          :src="imageUrl"
          class="main-image"
          alt="main-img"
          ref="image"
          @load="initImagePosition"
          draggable="false"
          :style="{
            width: imageSize.width / 2 + 'px',
            height: imageSize.height / 2 + 'px'
          }"
        />
        <template v-for="(point, index) in deviceMarkersCopy">
          <div
            :key="`${point.id}-${pageType}-${index}`"
            class="point"
            :class="{ highlight: selectedPointId === point.id }"
            :style="pointStyle(point)"
            @click="(e) => selectPoint(e, point)"
            @mouseenter="onPointHover(point, true)"
            @mouseleave="onPointHover(point, false)"
            v-if="point"
          >
            <img
              class="point-icon"
              :class="`point-icon-hover-${point.isHover || false}`"
              :src="point.icon.iconUrl"
              :style="{
                width: point.icon.iconSize.width + 'px',
                height: point.icon.iconSize.height + 'px'
              }"
              draggable="false"
              alt="point-icon"
            />
            <div class="matrix-name point-text">
              <div class="map-left-icon"></div>
              {{ point.deviceName }}
              <div class="map-right-icon"></div>
            </div>
          </div>
        </template>
        <div class="booster-station boost-name" @click="() => $emit('activeTab', 'PAGE_TYPE_BOOSTER_STATION')" v-show="pageType !== 'PAGE_TYPE_BOOSTER_STATION' && !loading">升压站</div>
        <div
          class="map-alarm-container"
          :style="point"
          :key="alarmData.id"
          v-if="Object.keys(alarmData).length > 0 && pageType === 'PAGE_TYPE_OVERVIEW' && point.left != 0"
          @click="$emit('openRealtimeWindow', mapAlarmList[nowAlarmIndex])"
        >
          <div class="map-alarm-cyclic flex-start flex-column">
            <div class="map-alarm-window relative cursor-pointer" :class="`alarm-window-grade${alarmData.alarmGrade}`">
              <div class="width-100 alarm-map-img margin-b-8 relative" style="z-index: 1">
                <img class="alarm-map-img" :src="`${alarmData.fileUrlThumbnail}`" alt="alarm-img" />
                <div class="img-label flex-start">
                  <span class="font-12 color-text-main margin-l-12">
                    {{ alarmData.alarmReason }}
                  </span>
                </div>
              </div>
              <span class="font-14 num-font-500 color-text-white relative" style="z-index: 1">告警时间：{{ alarmData.alarmTime }}</span>
              <div class="alarm-window-bg window-bg${alarmData.alarmGrade}"></div>
            </div>
            <div class="width-100 flex-center">
              <div class="map-alarm-icon" :class="`alarm-icon-grade${alarmData.alarmGrade}`" />
            </div>
          </div>
        </div>
      </div>
      <div class="reset-btn" @click="centerImage(true)">复位</div>
    </div>
  </a-spin>
</template>

<script>
import { DeviceMarkerMixins } from '@/views/2dMap/mixins/deviceMarkerMixins';
import { AlarmMixins } from '@/views/2dMap/mixins/alarmMixins';
import { AlarmCyclicMixins } from '@/views/2dMap/mixins/customAlarmCycli';
import webSocket from '@/mixins/webSocket';
import { getModelData } from '@/api/2dMap/psOverview';
import Vue from 'vue';
import { PSA_INFO } from '@/store/mutation-types';
// import { boostDevices, twiceRoomDeviceKeys, volatageRoomDeviceKeys } from '@/views/2dMap/mixins/boostDevices';

const arr = ['PAGE_TYPE_OVERVIEW', 'PAGE_TYPE_PHOTOVOLTAIC_AREA'];
export default {
  name: 'ImageMarker',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    showDevice: {
      type: Boolean,
      default: true
    },
    isInPsOverview: {
      type: Boolean,
      default: true
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  mixins: [DeviceMarkerMixins, AlarmMixins, AlarmCyclicMixins, webSocket],
  data() {
    return {
      deviceMarkers: [],
      selectedPointId: null,
      scale: 1,
      minScale: 0.7,
      maxScale: 2,
      imagePos: { x: 0, y: 0 },
      imageSize: { width: 0, height: 0 },
      containerSize: { width: 0, height: 0 },
      isDragging: false,
      startX: 0,
      startY: 0,
      dragStartPos: { x: 0, y: 0 },
      psaInfo: Vue.ls.get(PSA_INFO),
      point: {
        left: 0,
        top: 0
      },
      alarmList: [],
      deviceMarkersCopy: [],
      loading: true,
      _dragTempPos: { x: 0, y: 0 }, // 新增临时变量
      _dragRaf: null, // 新增
      nowAlarmIndex: 0
    };
  },
  computed: {
    imageWrapperStyle() {
      const isBootStrame = this.pageType == 'PAGE_TYPE_BOOSTER_STATION';
      const width = isBootStrame ? 150 : 0;
      return {
        transform: `translate(${this.imagePos.x - width}px, ${this.imagePos.y + 50}px) scale(${this.scale})`,
        width: this.displayImageWidth + 'px',
        height: this.displayImageHeight + 'px'
      };
    },
    displayImageWidth() {
      return this.imageSize.width / 2;
    },
    displayImageHeight() {
      return this.imageSize.height / 2;
    },
    pointStyle() {
      return (point) => ({
        left: `${point.lat}px`,
        top: `${point.lng}px`,
        'z-index': point.isError ? 9999 : 1
      });
    }
  },
  watch: {
    pageType(newPage, oldPage) {
      this.centerImage(true);
      if ((arr.includes(newPage) && !arr.includes(oldPage)) || (!arr.includes(newPage) && arr.includes(oldPage))) {
        this.loading = true;
        this.deviceMarkers = [];
        this.deviceMarkersCopy = [];
        this.selectedPointId = null;
        this.imageSize = { width: 0, height: 0 };
        this.initImagePosition();
        this.initContainerSize();
      }
      // this.getDeviceMarkers();
    }
  },
  created() {},
  mounted() {
    this.initContainerSize();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    onPointHover(point, isHover) {
      if (!isHover && this.selectedPointId === point.id) this.selectedPointId = null;
      Object.assign(point, {
        isHover,
        icon: this.getMonitorIcon({
          isHover,
          isError: point.isError,
          isOffline: point.onlineStatus != 1
        })
      });
      // 如果需要响应式，确保 point 是响应式对象
      // 如果不是响应式，可以用 this.$set(this.deviceMarkers, idx, {...point}) 替换
    },
    async initContainerSize() {
      if (this.$refs.container) {
        this.containerSize = {
          width: this.$refs.container.clientWidth,
          height: this.$refs.container.clientHeight
        };
        this.ensureImageInView();
        await this.getMapAlarmCyclicData();
      }
    },
    handleResize() {
      this.initContainerSize();
    },
    async initImagePosition() {
      console.log('image load');
      this.imageSize = {
        width: this.$refs.image.naturalWidth,
        height: this.$refs.image.naturalHeight
      };
      // 初始居中图片
      await this.getMonitorDevice();
    },
    centerImage(isReset = false) {
      if (isReset) {
        this.scale = 1;
        this.imagePos = { x: 0, y: 0 };
        return;
      }
      this.imagePos = {
        x: (this.containerSize.width - this.displayImageWidth) / 2,
        y: (this.containerSize.height - this.displayImageHeight) / 2
      };
    },
    ensureImageInView() {
      // 确保图片不会移出可视区域
      const maxX = 0;
      const minX = this.containerSize.width - this.displayImageWidth;
      const maxY = 0;
      const minY = this.containerSize.height - this.displayImageHeight;

      this.imagePos = {
        x: Math.min(maxX, Math.max(minX, this.imagePos.x)),
        y: Math.min(maxY, Math.max(minY, this.imagePos.y))
      };
    },

    selectPoint(e, item) {
      // this.selectedPointId = item.id;
      this.$emit('deviceMarkerClick', e, item);
      // this.centerPoint(id)
    },
    centerPoint(id) {
      let point = this.deviceMarkers.find((p) => p.id === id);
      if (!point) point = this.BoostPosition;

      // 重置缩放到初始状态
      this.scale = 1;

      // 计算页面中心位置
      const centerX = this.containerSize.width / 2;
      const centerY = this.containerSize.height / 2;

      // 告警弹窗尺寸
      const alarmWindowHeight = 257;

      // 设置point位置
      this.point = {
        left: `${point.lat}px`,
        top: `${point.lng}px`
      };
      const isBootStrame = this.pageType == 'PAGE_TYPE_BOOSTER_STATION';
      const offsetX = isBootStrame ? 150 : 0;
      const offsetY = 50;

      // 目标位置：页面中心
      const targetX = centerX;
      const targetY = centerY + alarmWindowHeight / 2;

      this.imagePos = {
        x: targetX - point.lat + offsetX - 450, // 增加左偏移量
        y: targetY - point.lng - offsetY
      };
    },
    startDrag(e) {
      e.preventDefault();
      this.isDragging = true;
      this.dragStartPos = { x: e.clientX, y: e.clientY };
      this.startImagePos = { ...this.imagePos };
      this._dragTempPos = { ...this.imagePos };
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.endDrag);
    },
    handleDrag(e) {
      e.preventDefault();
      if (!this.isDragging) return;
      const dx = e.clientX - this.dragStartPos.x;
      const dy = e.clientY - this.dragStartPos.y;
      this._dragTempPos.x = this.startImagePos.x + dx;
      this._dragTempPos.y = this.startImagePos.y + dy;
      // 节流到下一帧再更新 imagePos
      if (!this._dragRaf) {
        this._dragRaf = requestAnimationFrame(() => {
          this.imagePos = { ...this._dragTempPos };
          this._dragRaf = null;
        });
      }
    },
    endDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.endDrag);
    },
    handleWheel(e) {
      const oldScale = this.scale;
      let newScale = this.scale * (e.deltaY > 0 ? 0.9 : 1.1);
      newScale = Math.max(this.minScale, Math.min(this.maxScale, newScale));
      if (newScale === this.scale) return;

      // 用 container 作为参考
      const rect = this.$refs.container.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      // 计算缩放前，鼠标在图片坐标系中的点
      const imgX = (mouseX - this.imagePos.x) / oldScale;
      const imgY = (mouseY - this.imagePos.y - 50) / oldScale; // 注意y轴偏移

      // 缩放后，调整 imagePos 使鼠标下的点不动
      this.imagePos.x = mouseX - imgX * newScale;
      this.imagePos.y = mouseY - imgY * newScale;

      this.scale = newScale;
      this.ensureImageInView();
    },
    movePositionToCenter(marker) {
      // this.centerImage();
      if (Array.isArray(marker) && marker.length == 0) {
        return;
      }
      this.selectedPointId = marker.id;
      this.centerPoint(marker.id);
      //    this.ensureImageInView();
    },
    async refreshAlarmMarkers(isClear = false) {
      this.loading = true;
      if (isClear) {
        this.deviceMarkers.forEach((marker) => {
          if (marker.isError) {
            marker.isError = false;
          }
        });
      }
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: 'alertList',
        needLuminousPower: 1
      });
      this.loading = false;
      // this.$emit("setAlarmNumTotal", res.result_data.length);
      // 筛选出设备最严重等级的告警
      this.alarmList = res.result_data.reduce((prev, cur) => {
        let index = prev.findIndex((el) => el.psKey == cur.psKey);
        if (index == -1) {
          prev.push(cur);
          return prev;
        } else {
          if (prev[index].alarmGrade > cur.alarmGrade) {
            prev[index] = cur;
          }
          return prev;
        }
      }, []);

      this.alarmList.forEach((item) => {
        if (item.deviceSubType && item.deviceType != '1' && item.deviceType != '4') {
          item.deviceType = item.deviceType + '-' + item.deviceSubType;
        }
      });
      let arr = this.alarmList.map((item) => item.psKey);
      // 遍历摄像头图标，将摄像头的告警信息赋值
      this.deviceMarkers.forEach((marker) => {
        if (arr.includes(marker.psKey)) {
          marker.isError = true;
          marker.icon = this.getMonitorIcon({
            isHover: false,
            isError: true,
            isOffline: marker.onlineStatus != 1
          });
        }
      });
      this.getDeviceMarkers();
    },
    moveMapToPsCenter() {},
    getMonitorIcon(config) {
      let iconSize = config.isHover ? this.monitorIconSizeHover : this.monitorIconSize;
      let iconUrl = this.getIcon('54', config.isHover, config.isError, config.isError ? false : config.isOffline);
      return {
        ...iconSize,
        iconUrl
      };
    },
    findPoint(psKey) {
      return this.deviceMarkers.find((item) => item.psKey == psKey);
    },
    getDeviceMarkers() {
      this.deviceMarkersCopy = this.deviceMarkers.filter((item) => {
        if (this.pageType === 'PAGE_TYPE_BOOSTER_STATION') {
          return item.areaType == 2;
        } else {
          return item.areaType == 1;
        }
      });
    },
    // 获取摄像头信息
    async getMonitorDevice() {
      this.deviceMarkers = [];
      let monitorList = [];
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: 'cameraList',
        areaType: this.pageType === 'PAGE_TYPE_BOOSTER_STATION' ? 2 : 1
      });
      monitorList = res.result_data.dataResult.cameraInfo;
      //monitorList = caramlist;
      this.loading = false;
      monitorList.map((item) => {
        let position = {
          longitude: 117.5569067895412 + Math.random() * 0.01,
          latitude: 31.936283550745518 + Math.random() * 0.01
        };
        if (item.coordinate) {
          let arr = item.coordinate.split(',');
          position.longitude = arr[0];
          position.latitude = arr[1];
        }
        let icon = this.getMonitorIcon({
          isHover: false,
          isError: false,
          isOffline: item.onlineStatus != 1
        });
        let marker = {};
        marker.icon = icon;
        marker.isError = false;
        marker.isOffline = item.onlineStatus != 1;
        marker.lat = position.latitude;
        marker.lng = position.longitude;
        marker.name = item.deviceName;
        marker.id = item.uuid;
        marker.deviceImg = item.fileUrlThumbnail;
        Object.assign(marker, item);
        marker.deviceType = '54';
        this.deviceMarkers.push(marker);
      });
      this.refreshAlarmMarkers();
    },
    getPositionByPsKey(psKey) {
      let marker = this.deviceMarkers.find((item) => item.psKey == psKey);
      if (!marker) {
        // if (volatageRoomDeviceKeys.includes(psKey)) {
        //   return boostDevices[3].positions;
        // }
        // if (twiceRoomDeviceKeys.includes(psKey)) {
        //   return boostDevices[1].positions;
        // }
        return {...this.BoostPosition, id: Math.random()};
      }
      return marker || [];
    },
    setDeviceMarkerCenter(marker) {
      const currentDevice = this.deviceMarkersCopy.find((item) => item.psKey === marker.psKey);
      const index = this.deviceMarkersCopy.findIndex((item) => item.psKey === marker.psKey);
      if (index === -1) return;
      this.selectedPointId = currentDevice.id;
      this.deviceMarkersCopy.splice(index, 1, { ...currentDevice, isHover: true });
      this.onPointHover(currentDevice, true);
    }
  }
};
</script>

<style scoped lang="less">
.image-marker-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  /* background-color: #f0f0f0; */
  background-image: url('../../../assets/images/2dMap/station-bg-all.webp');
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-marker-container-not-all {
  background: url('../../../assets/images/2dMap/station-bg.webp') no-repeat;
  background-size: contain;
}

.map-alarm-container {
  position: absolute;
  z-index: 9999;
  transform: translate(-50%, -100%);
}

.main-image {
  object-fit: cover;
}

.image-wrapper {
  position: absolute;
  transition: transform 0.3s ease;
  will-change: transform;
}

.booster-station {
  position: absolute;
  top: 97px;
  left: 600px;
  cursor: pointer;
}

.image-wrapper img.main-image {
  display: block;
  user-select: none;
  pointer-events: none;
}

.point {
  position: absolute;
  transform: translate(-50%, 0);
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  pointer-events: auto;

  &:hover {
    .point-text {
      display: block;
    }
  }

  .point-icon {
    display: block;
    /* 让缩放锚点为底部中心 */
    transform-origin: 50% 100%;
    transform: translateX(-50%) scale(1);
    transition: transform 0.3s;
    position: absolute;
    left: 50%;
    bottom: 0;
    z-index: 1;
    width: 32px;
    height: 84px;
    object-fit: contain;
  }

  .point-text {
    position: absolute;
    left: 50%;
    top: -108px;
    transform: translateX(-50%);
    display: none;
    z-index: 9999;
    will-change: transform, opacity;
  }
}

.point.highlight {
  .point-icon {
    transform: translateX(-50%) scale(1.3);
  }

  .point-icon-hover-true + .point-text {
    display: block;
    transform: translate(-50%, -100%);
  }

  .point-icon-hover-false + .point-text {
    display: none;
    transform: translateX(-50%) scale(1);
  }
}

.point.highlight,
.point:hover {
  z-index: 9999 !important;
}

.controls {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  color: white;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 10px;
}

.controls button {
  padding: 5px 10px;
  cursor: pointer;
  border: none;
  border-radius: 3px;
}

.controls button:hover {
  background-color: #ddd;
}

.reset-btn {
  position: absolute;
  left: 74%;
  bottom: 56px;
  border-radius: 4px;
  opacity: 1;
  /* 自动布局 */
  padding: 2px 10px;
  border: 1px solid #61a1d5;
  color: #61a1d5;
  cursor: pointer;
}
</style>
