<!--  -->
<template>
  <div class="pointer-events-none weather-area">
    <div
      :class="weatherItemClass"
      :style="item.style"
      :key="index"
      v-for="(item, index) in weatherMarkerList"
    ></div>
  </div>
</template>

<script>
import { EventBus } from "./eventBus";
import {
  getModelData,
  getWeatherEffectSwitch,
  // updateWeatherEffectSwitch,
} from "../../../api/2dMap/psOverview";
import Vue from "vue";
import { USER_INFO } from "../../../store/mutation-types";
import moment from "moment";

export default {
  name: "WeatherFullScreen",
  props: {},
  data() {
    return {
      weatherMarkerList: [
        // {
        //   time: 10,
        //   style: {
        //     left: '900px',
        //     height: '50px',
        //     width: '1px',
        //     'animation-duration': '100s',
        //   }
        // }
      ],
      userInfo: Vue.ls.get(USER_INFO),
      randomSeed: 0.6, // 随机程度
      blurRate: 0.1, // 模糊概率
      rainConfig: {
        class: "rain",
        count: 100, // 数量
        height: 30,
        width: 1.5,
        time: 0.5, // 动画时间
      },
      weatherItemClass: "rain",

      snowConfig: {
        class: "snow",
        count: 120, // 雪花数量
        height: 20,
        width: 20,
        time: 8, // 动画时间
      },
      psaInfo: null,
    };
  },
  created() {
    this.psaInfo = this.$ls.get("PSA_INFO");
    this.getWeatherEffect();
  },
  mounted() {
    EventBus.$on("messageSent", this.handleMessage);
  },
  beforeDestroy() {
    EventBus.$off("messageSent", this.handleMessage); // 移除特定事件的监听器
  },
  methods: {
    renderAnimation(config) {
      console.log(config);
      this.weatherMarkerList = [];
      this.weatherItemClass = config.class;
      const { innerWidth } = window;
      for (let i = 0; i < config.count; i++) {
        this.weatherMarkerList.push({
          style: {
            left: innerWidth * 1.2 * Math.random() + "px",
            "animation-duration": config.time * this.randomBySeed() + "s",
            "animation-delay": config.time * this.randomBySeed(1) + "s",
            width: config.width * this.randomBySeed() + "px",
            height: config.height * this.randomBySeed() + "px",
            filter: Math.random() < this.blurRate ? `blur(1px)` : "",
          },
        });
      }
    },
    handleMessage(e) {
      if (e) {
        this.getWeatherEffect();
      } else {
        this.removeAnimation();
      }
    },
    async getWeatherEffect() {
      if (!this.psaInfo) {
        return;
      }
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: "psInfo",
        recordDate: moment().format("YYYY-MM-DD"),
      });
      let e = res.result_data.dataResult.weatherSort;
      this.setWeather(e);
    },
    async setWeather(e) {
      let res = await getWeatherEffectSwitch({
        useraccount: this.userInfo.username || "",
      });
      if (res.result_data != 1) {
        return;
      }

      if (e == 1) {
        this.renderAnimation(this.rainConfig);
        return;
      }
      if (e == 3) {
        this.renderAnimation(this.snowConfig);
        return;
      }
      this.removeAnimation();
    },
    removeAnimation() {
      this.weatherMarkerList = [];
    },

    // 根据随机程度获取随机数
    // eg：.2随机程度 获取0.8~1.2的随机数
    randomBySeed(randomSeed = this.randomSeed) {
      return Math.random() > 0.5
        ? 1 + randomSeed * Math.random()
        : 1 - randomSeed * Math.random();
    },
  },
};
</script>

<style lang="less" scoped>
@keyframes move {
  from {
    transform: rotate(-15deg) translateY(0);
  }
  to {
    transform: rotate(-15deg) translateY(calc(100vh + 20px));
  }
}

.rain {
  width: 50px;
  height: 1px;
  background: #999999;
  position: absolute;
  bottom: 100%;
  transform-origin: 100% 50%;
  animation-name: move;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.snow {
  width: 30px;
  height: 30px;
  background: url(../../../assets/images/2dMap/snow.png) no-repeat;
  background-size: contain;
  position: absolute;
  bottom: 100%;
  transform-origin: 100% 50%;
  animation-name: move;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.weather-area {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
</style>
