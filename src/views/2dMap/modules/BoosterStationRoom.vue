<!-- 升压站/室内图 -->
<template>
  <div class="width-100 height-100">
    <div class="twice-device-room" v-if="roomType == 2">
      <div class="flex-center width-100 margin-b-40">
        <div class="title flex-center">
          <span class="color-text-white font-16">400V低压室</span>
        </div>
      </div>

      <div class="width-100 flex-center">
        <div class="flex-space-between low-voltage-devices">
          <div
            class="device-item cursor-pointer"
            @click="clickDevice(item)"
            :class="getDeviceItemBgClass(item)"
            v-for="(item, index) in devicesLowVoltage"
            :key="index"
          >
            <img :src="item.src" class="device-img" />
            <div class="label-bg" :class="getErrorLabelClass(item)">
              <div class="label flex-center">
                <span class="color-text-white">{{ item.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-center width-100 margin-b-40">
        <div class="title flex-center">
          <span class="color-text-white font-16">35kV高压室</span>
        </div>
      </div>

      <div class="width-100 flex-center">
        <div class="high-voltage-devices flex-space-between">
          <div
            :class="{ 'robot-img-high-move': isRobotMove }"
            class="robot-img-high"
          >
            <svg-icon :iconClass="robotStatus" class="robot-status" />
            <img
              class="robot"
              :src="robotSrcPowerDistribution"
              alt="配电楼机器人"
            />
          </div>
          <div
            class="device-item cursor-pointer"
            @click="clickDevice(item)"
            :class="getDeviceItemBgClass(item)"
            v-for="(item, index) in devicesHighVoltage"
            :key="index"
          >
            <img :src="item.src" class="device-img-high-voltage" />
            <div class="label-bg" :class="getErrorLabelClass(item)">
              <div class="label flex-center">
                <span class="color-text-white">{{ item.label }}</span>
              </div>
            </div>
          </div>
          <svg-icon
            @click="startRobotExecute"
            @mouseenter="robotMouseEnter"
            @mouseleave="robotMouseLeave"
            :icon-class="robotExecuteClass"
            class="robot-execute-35kv"
            :class="{
              'pointer-events-none': robotExecuteClass === 'execute-disabled',
              'cursor-pointer': robotExecuteClass !== 'execute-disabled',
            }"
          />
        </div>
      </div>
    </div>
    <div class="power-distribution-room" v-if="roomType == 3">
      <div class="flex-center width-100 margin-b-40">
        <div class="title flex-center">
          <span class="color-text-white font-16">二次设备室</span>
        </div>
      </div>
      <div class="devices-area flex-space-around flex-column">
        <div class="path">
          <div
            class="robot-img-distribution-room"
            :class="{ 'robot-move-recyle-twice': isRobotMove }"
          >
            <svg-icon :iconClass="robotStatus" class="robot-status" />
            <img
              class="robot"
              :src="robotSrcTwiceRoom"
              alt="二次设备间机器人"
            />
          </div>
          <div class="point1"></div>
          <div class="point2"></div>
          <div class="line-1"></div>
          <div class="line-2"></div>
          <svg-icon
            @click="startRobotExecute"
            @mouseenter="robotMouseEnter"
            @mouseleave="robotMouseLeave"
            :icon-class="robotExecuteClass"
            class="robot-execute-twice"
            :class="{
              'pointer-events-none': robotExecuteClass === 'execute-disabled',
              'cursor-pointer': robotExecuteClass !== 'execute-disabled',
            }"
          />
        </div>

        <div class="width-100 flex-space-between device-area-item">
          <div
            class="device-item-div"
            v-for="(item, index) in devicesTwice1"
            :key="index"
          >
            <div
              class="device-item"
              v-if="index != 5"
              :class="getDeviceItemBgClass(item)"
              @click="clickDevice(item)"
            >
              <img :src="item.src" class="device-img" />
              <div class="label-bg" :class="getErrorLabelClass(item)">
                <div class="label flex-center">
                  <span class="color-text-white">{{ item.label }}</span>
                </div>
              </div>
            </div>

            <div
              class="device-item"
              v-else
              :class="getDeviceItemBgClass(item)"
              id="Row1Column5"
            >
              <img
                :src="item.src"
                v-if="!isMouseInRow1Column5"
                class="device-img"
              />
              <div class="device-btns width-100 flex-center" v-else>
                <div
                  class="device-btn cursor-pointer margin-r-12 flex-center"
                  @click="
                    clickDevice({
                      shadowPsKey: '107353_9999_1_9',
                      psKey: '',
                      label: '充电柜',
                    })
                  "
                >
                  <span class="margin-t-4"> 充电柜 </span>
                </div>
                <div
                  class="device-btn cursor-pointer flex-center"
                  @click="
                    clickDevice({
                      shadowPsKey: '',
                      psKey: '107353_33_10008348_1',
                      label: '直流屏',
                    })
                  "
                  :class="getDeviceBtnClass('107353_33_10008348_1')"
                >
                  <span class="margin-t-4"> 直流屏 </span>
                </div>
              </div>
              <div class="label-bg" :class="getErrorLabelClass(item)">
                <div class="label flex-center">
                  <span class="color-text-white">{{ item.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="width-100 flex-space-between device-area-item">
          <div
            class="device-item cursor-pointer"
            v-for="(item, index) in devicesTwice2"
            :class="getDeviceItemBgClass(item)"
            @click="clickDevice(item)"
            :key="index"
          >
            <img :src="item.src" class="device-img" />
            <div class="label-bg" :class="getErrorLabelClass(item)">
              <div class="label flex-center">
                <span class="color-text-white">{{ item.label }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="width-100 flex-space-between device-area-item line-2">
          <div
            class="device-item-div cursor-pointer"
            :class="getDeviceItemBgClass(item)"
            v-for="(item, index) in devicesTwice3"
            :key="index"
          >
            <div
              class="device-item"
              v-if="index != 3"
              @click="clickDevice(item)"
            >
              <img :src="item.src" class="device-img" />
              <div class="label-bg" :class="getErrorLabelClass(item)">
                <div class="label flex-center">
                  <span class="color-text-white">{{ item.label }}</span>
                </div>
              </div>
            </div>

            <div
              class="device-item"
              v-else
              :class="getDeviceItemBgClass(item)"
              id="Row3Column4"
            >
              <img
                :src="item.src"
                v-if="!isMouseInRow3Column4"
                class="device-img"
              />
              <div class="device-btns width-100 flex-center" v-else>
                <div
                  class="device-btn cursor-pointer margin-r-12 flex-center"
                  @click="
                    clickDevice({
                      shadowPsKey: '107353_9999_1_24',
                      psKey: '',
                      label: '紧急控制柜',
                    })
                  "
                >
                  <span class="margin-t-4"> 紧急控制柜 </span>
                </div>
                <div
                  class="device-btn cursor-pointer flex-center"
                  @click="
                    clickDevice({
                      shadowPsKey: '',
                      psKey: '107353_13_10006995_2',
                      label: '解列装置',
                    })
                  "
                  :class="getDeviceBtnClass('107353_13_10006995_2')"
                >
                  <span class="margin-t-4"> 解列装置 </span>
                </div>
              </div>
              <div class="label-bg" :class="getErrorLabelClass(item)">
                <div class="label flex-center">
                  <span class="color-text-white">{{ item.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getModelData,
  getBoosterRobotStatus,
  startRobotExecuteApi,
} from "@/api/2dMap/psOverview";
import { boosterRoomDevice } from "../mixins/boostDevices";
import { PSA_INFO } from "@/store/mutation-types";
import { debounce } from "lodash";

export default {
  name: "BoosterStationRoom",
  props: {
    roomType: {
      type: [Number, String],
      default: 2,
    },
  },
  data() {
    return {
      robotSrcTwiceRoom: require("@/assets/images/2dMap/2d-robot-twice-room.png"),
      robotSrcPowerDistribution: require("@/assets/images/2dMap/2d-robot-power-distribution.png"),

      devicesHighVoltage: boosterRoomDevice.devicesHighVoltage,
      devicesLowVoltage: boosterRoomDevice.devicesLowVoltage,
      // 分成3行 自适应布局
      devicesTwice1: boosterRoomDevice.devicesTwice1,
      devicesTwice2: boosterRoomDevice.devicesTwice2,
      devicesTwice3: boosterRoomDevice.devicesTwice3,

      isMouseInRow1Column5: false,
      isMouseInRow3Column4: false,
      domRow1Column5: null,
      domRow3Column4: null,
      selectedPsKey: "",
      isRobotMove: false,
      robotPsKeyTwice: "107353_55_0098_11",
      robotPsKeyDistribution: "107353_55_0098_3",
      alarmBtnColors: ["#744255", "#80573D", "#4D467C", "#5A6A70"],
      alarmPsKeys: [],
      alarms: [],
      robotStatus: "", // 机器人状态
      robotExecuteClass: "execute-normal",
      robotTimer: null,
      canExecuteStatusMap: "robot-fault,robot-idle,robot-charging", // 当状态为空闲、故障、充电时，支持点击“立即执行”
    };
  },
  computed: {
    psaInfo() {
      return this.$ls.get(PSA_INFO);
    },
  },
  watch: {
    roomType: {
      handler: function (val) {
        this.refreshRobotMove();
        this.robotTimer && clearInterval(this.robotTimer);
        this.robotTimer = setInterval(() => {
          this.refreshRobotMove();
        }, 5 * 60 * 1000);
        this.getAlarmDevice();
        this.selectedPsKey = "";
        if (val == 3) {
          this.$nextTick(() => {
            this.addEvent();
          });
        }
      },
      immediate: true,
    },
  },

  methods: {
    async getAlarmDevice() {
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: "alertList",
        areaType: 2,
        monitorType: 1,
        subAreaType: this.roomType,
      }).catch((e) => {
        this.$emit("setLoading", false);
        return;
      });
      this.alarmPsKeys = res.result_data.map((item) => item.psKey);

      this.alarms = res.result_data.reduce((prev, cur) => {
        let index = prev.findIndex((el) => el.psKey == cur.psKey);
        if (index == -1) {
          prev.push(cur);
          return prev;
        } else {
          if (prev[index].alarmGrade > cur.alarmGrade) {
            prev[index] = cur;
          }
          return prev;
        }
      }, []);
      // console.log(this.alarms);
    },
    getErrorLabelClass(item) {
      let el = this.alarms.find((e) => e.psKey == item.psKey);
      // console.log(el);
      return el ? "label-bg-error-" + el.alarmGrade : "";
    },
    getDeviceItemBgClass(item) {
      let el = this.alarms.find((e) => e.psKey == item.psKey);
      let str = "";
      if (el) {
        str += "device-item-error-" + el.alarmGrade;
      }
      if (item.psKey != "" && item.psKey == this.selectedPsKey) {
        str += " selected-device-bg";
      }
      return str;
    },
    getDeviceBtnClass(psKey) {
      let el = this.alarms.find((e) => e.psKey == psKey);
      return el ? "device-btn-error-" + el.alarmGrade : "";
    },
    changeSelectPsKey(psKey) {
      this.selectedPsKey = psKey;
    },
    clickDevice(item) {
      let params = {
        psKey: item.psKey,
        shadowPsKey: item.shadowPsKey || "",
        deviceName: item.label,
        isError: this.alarmPsKeys.includes(item.psKey),
        deviceType: "",
      };
      // isError参数根据psKey筛选，so shadowPsKey不会为true
      if (params.isError) {
        let temp = this.alarms.find((el) => el.psKey == item.psKey);
        Object.assign(params, temp);
      }
      // 107353_9999_1_4：光功率预测柜
      if (params.shadowPsKey === "107353_9999_1_4") {
        this.$emit("deviceClickInBoostByPower", null, params);
        return;
      }
      this.$emit("deviceClickInMap", null, params);
    },

    async refreshRobotMove() {
      let res = await getBoosterRobotStatus({
        psKeys:
          this.roomType == 3
            ? this.robotPsKeyTwice
            : this.robotPsKeyDistribution,
      });
      this.isRobotMove = res.result_data[0].runStatus == 1;
      this.robotStatus = this.getRobotStatus(res.result_data[0]);
      this.robotExecuteClass = this.getRobotExecuteClass(res.result_data[0]);
    },
    addEvent() {
      this.domRow1Column5 = document.getElementById("Row1Column5");
      this.domRow1Column5.addEventListener(
        "mouseenter",
        (e) => (this.isMouseInRow1Column5 = true)
      );
      this.domRow1Column5.addEventListener(
        "mouseleave",
        (e) => (this.isMouseInRow1Column5 = false)
      );

      this.domRow3Column4 = document.getElementById("Row3Column4");
      this.domRow3Column4.addEventListener(
        "mouseenter",
        (e) => (this.isMouseInRow3Column4 = true)
      );
      this.domRow3Column4.addEventListener(
        "mouseleave",
        (e) => (this.isMouseInRow3Column4 = false)
      );
    },
    // 机器人状态
    getRobotStatus(statusInfo = {}) {
      if (statusInfo.onlineStatus == "0") return "robot-offline";
      if (statusInfo.faultStatus == "1") return "robot-fault";
      const statusEnum = {
        0: "robot-idle",
        1: "robot-running",
        2: "robot-charging",
        3: "robot-charging",
      };
      return statusEnum[statusInfo.runStatus];
    },
    // 机器人执行按钮类
    getRobotExecuteClass() {
      const disabledStatusMap = "robot-running,robot-offline";
      if(!this.robotStatus) return "execute-disabled";
      if (disabledStatusMap.indexOf(this.robotStatus) > -1) {
        return "execute-disabled";
      }
      return "execute-normal";
    },
    robotMouseEnter() {
      if (this.canExecuteStatusMap.indexOf(this.robotStatus) > -1) {
        this.robotExecuteClass = "execute-hover";
      }
    },
    robotMouseLeave() {
      if (this.canExecuteStatusMap.indexOf(this.robotStatus) > -1) {
        this.robotExecuteClass = "execute-normal";
      }
    },
    // 机器人执行
    startRobotExecute: debounce(async function () {
      this.robotExecuteClass = "execute-disabled";
      const { roomType, robotPsKeyTwice, robotPsKeyDistribution } = this;
      const psKey = roomType == 3 ? robotPsKeyTwice : robotPsKeyDistribution;
      await startRobotExecuteApi({ psKey });
    }, 500),
  },
  beforeDestroy() {
    if (this.roomType == 3) {
      this.domRow1Column5.removeEventListener(
        "mouseenter",
        (e) => (this.isMouseInRow1Column5 = true)
      );
      this.domRow1Column5.removeEventListener(
        "mouseleave",
        (e) => (this.isMouseInRow1Column5 = false)
      );
      this.domRow3Column4.removeEventListener(
        "mouseenter",
        (e) => (this.isMouseInRow3Column4 = true)
      );
      this.domRow3Column4.removeEventListener(
        "mouseleave",
        (e) => (this.isMouseInRow3Column4 = false)
      );
    }
    clearInterval(this.robotTimer);
    this.robotTimer = null;
  },
};
</script>

<style lang="less" scoped>
.selected-device-bg {
  background: rgba(133, 202, 255, 0.3) !important;
}

@keyframes moveRecyleHigh {
  0% {
    left: 0;
    top: 100%;
  }
  10% {
    left: 0;
    top: 0;
  }
  50% {
    left: 100%;
    top: 0;
  }
  60% {
    left: 100%;
    top: 100%;
  }
  100% {
    left: 0;
    top: 100%;
  }
}

@keyframes moveRecyleTwice {
  0% {
    left: 0;
    top: 100%;
  }
  25% {
    left: 100%;
    top: 100%;
  }
  37.5% {
    left: 100%;
    top: 50%;
  }
  62.5% {
    left: 0;
    top: 50%;
  }
  75% {
    left: 0;
    top: 0;
  }
  100% {
    left: 100%;
    top: 0;
  }
}

.power-distribution-room {
  width: 72%;
  left: 74px;
  height: 75vh;
  bottom: 120px;
  position: absolute;

  .devices-area {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;
    height: calc(100% - 90px);

    .path {
      width: 100%;
      height: 66.66%;
      position: absolute;
      bottom: 0;
      left: 0;
      border-top: 1px dashed @di-color-text-main;
      border-bottom: 1px dashed @di-color-text-main;

      .line-1 {
        position: absolute;
        height: 50%;
        width: 100%;
        left: 0;
        bottom: 0;
        border-top: 1px dashed @di-color-text-main;
        border-right: 1px dashed @di-color-text-main;
      }

      .line-2 {
        position: absolute;
        height: 50%;
        width: 100%;
        left: 0;
        top: 0;
        border-left: 1px dashed @di-color-text-main;
      }

      .point1 {
        position: absolute;
        left: 0;
        bottom: 0;
        border-radius: 50%;
        width: 8px;
        height: 8px;
        background: @di-color-text-main;
        transform: translate(-50%, 50%);
      }

      .point2 {
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 50%;
        width: 8px;
        height: 8px;
        background: @di-color-text-main;
        transform: translate(50%, -50%);
      }

      .robot-execute-twice {
        width: 60px;
        height: 60px;
        position: absolute;
        top: 100%;
        left: 0;
        transform: translate(-50%, 50%);
        z-index: 2;
      }
    }

    .device-area-item {
      height: 33.3333%;
      padding: 36px;
    }
  }

  .robot-img-distribution-room {
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 10;

    .robot-status {
      width: 58px;
      height: 38px;
      position: absolute;
      top: -22px;
      transform: translate(-50%, -50%);
    }

    .robot {
      width: 66px;
      height: 66px;
      position: absolute;
      transform: translate(-50%, -50%);
    }
  }

  .robot-move-recyle-twice {
    animation: 280s infinite linear moveRecyleTwice;
  }
}

.device-item-div {
  width: 130px;
  height: 166px;
  position: relative;
}

.device-item {
  width: 130px;
  height: 166px;
  position: relative;

  .device-img {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%);
    width: 58.54px;
    height: 166px;
  }

  .device-img-high-voltage {
    width: 92px;
    height: 166px;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%);
  }

  .device-btns {
    height: 115px;

    .device-btn {
      width: 40px;
      height: 105px;
      border-radius: 4px;
      background: #1b4981;
      margin-top: 10px;
      writing-mode: vertical-rl;
      color: rgba(189, 197, 212, 1);
      letter-spacing: 4px;
    }

    .device-btn:hover {
      background: #366cb0;
    }

    .device-btn-error {
      background: #744255;
    }

    .device-btn-error-1 {
      background: rgba(116, 66, 85, 1);
    }

    .device-btn-error-2 {
      background: #80573d;
    }

    .device-btn-error-3 {
      background: #4d467c;
    }

    .device-btn-error-4 {
      background: #5a6a70;
    }

    .device-btn-error-1:hover {
      background: #9f5e67;
    }

    .device-btn-error-2:hover {
      background: #a87453;
    }

    .device-btn-error-3:hover {
      background: #6e66ac;
    }

    .device-btn-error-4:hover {
      background: #74868d;
    }
  }

  .label-bg {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 130px;
    height: 42.17%;
    opacity: 1;
    background: linear-gradient(
      180deg,
      rgba(0, 144, 255, 0) 0%,
      rgba(0, 113, 255, 0.49) 100%
    );
    display: flex;

    .label {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      font-size: 14px;
      height: 44px;
      font-weight: 500;
      line-height: 22px;
      text-align: center;
      margin-bottom: 0;
    }
  }

  .label-bg-error {
    background: linear-gradient(
      180deg,
      rgba(129, 0, 0, 0) 0%,
      rgba(129, 0, 0, 0.6) 100%
    );
  }

  .label-bg-error-1 {
    background: linear-gradient(
      180deg,
      rgba(129, 0, 0, 0) 0%,
      rgba(129, 0, 0, 0.6) 100%
    );
  }

  .label-bg-error-2 {
    background: linear-gradient(
      180deg,
      rgba(187, 81, 16, 0) 0%,
      rgba(187, 81, 16, 0.6) 100%
    );
  }

  .label-bg-error-3 {
    background: linear-gradient(
      180deg,
      rgba(83, 74, 146, 0) 0%,
      rgba(83, 74, 146, 0.6) 100%
    );
  }

  .label-bg-error-4 {
    background: linear-gradient(
      180deg,
      rgba(89, 111, 118, 0) 0%,
      rgba(89, 111, 118, 0.6) 100%
    );
  }
}

.device-item-error-1:hover {
  background: rgba(255, 133, 133, 0.12) !important;
}

.device-item-error-2:hover {
  background: rgba(187, 81, 16, 0.12) !important;
}

.device-item-error-3:hover {
  background: rgba(83, 74, 146, 0.12) !important;
}

.device-item-error-4:hover {
  background: rgba(145, 162, 168, 0.12) !important;
}

.device-item:hover {
  background: rgba(133, 202, 255, 0.12);
}

.title {
  width: 138px;
  height: 30px;
  background: url("../../../assets/images/2dMap/2d-booster-station-room-title-bg.png");
  background-size: cover;
}

.twice-device-room {
  position: absolute;
  left: 44px;
  bottom: 12vh;
  width: 76%;
  height: 75vh;

  .high-voltage-devices {
    box-sizing: border-box;
    border: 1px dashed #85caff;
    width: 96%;
    height: 282px;
    padding: 5vh 28px;
    position: relative;

    .robot-img-high {
      position: absolute;
      left: 0;
      top: 100%;
      z-index: 10;

      .robot-status {
        width: 58px;
        height: 38px;
        position: absolute;
        top: -55px;
        transform: translate(-50%, -50%);
      }

      .robot {
        width: 66px;
        height: 66px;
        position: absolute;
        transform: translate(-50%, -50%);
      }
    }

    .robot-img-high-move {
      animation: moveRecyleHigh 200s infinite linear;
    }

    .robot-execute-35kv {
      width: 60px;
      height: 60px;
      position: absolute;
      top: 100%;
      left: 0;
      transform: translate(-50%, 39%);
      z-index: 2;
    }
  }

  .low-voltage-devices {
    box-sizing: border-box;
    width: 70%;
    margin-bottom: 8vh;
  }
}
</style>
