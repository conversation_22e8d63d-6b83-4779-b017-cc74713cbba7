<!-- 设备详情弹窗 -->
<template>
  <a-spin :spinning="loading" class="height-100 width-100">
    <div class="height-100 width-100 detail-modal-2d detail-modal-bg-2d detail-device-popup-small">
      <modal-title :title="(isMonitor ? monitorInfo.deviceName : deviceDetailData[0].value)" class="modal-title">

<!--        摄像头弹窗marker-->
        <div slot="left" class="detail-status flex-center margin-l-8"
             v-if="(isMonitor && (monitorInfo.onlineStatus == 0) || monitorAlarmList.length >0)"
        :class="monitorAlarmList.length >0 ? ('alarm-marker-bg-' + monitorAlarmList[0].alarmGrade ) : 'bg-offline' "
        >
          <span class="color-text-white font-12 ">{{ monitorAlarmList.length >0 ? grades[Number(monitorAlarmList[0].alarmGrade) - 1] : '离线' }}</span>
        </div>

<!--        设备弹窗marker-->
        <div slot="left" class="detail-status flex-center margin-l-8"
             v-if="!isMonitor"
        :class="isError ? ('alarm-marker-bg-' + alarmInfo.alarmGrade ) : 'bg-normal'  "
        >
          <span class="color-text-white font-12 ">{{(isError ? grades[Number(alarmInfo.alarmGrade) - 1] : '正常') }}</span>
        </div>
        <div slot="right" class="flex-end" >
          <div class="border-main margin-r-16 detail-btn flex-center cursor-pointer"
               v-if="!isMonitor"
               @click="goDetail">
            <span class="color-text-main font-12">详 情</span>
            <svg-icon icon-class="2d-double-arrow" class="color-text-main margin-l-4 font-10 "  />
          </div>
          <svg-icon icon-class="close"
                    class="font-10 margin-r-8 cursor-pointer color-text-white"
                    @click="close"
          />
        </div>
      </modal-title>
      <div class="width-100 detail-content-area">
        <div class="width-100 detail-top">
          <img class="device-detail-img flex-shrink-0 margin-r-12" :src="isMonitor ? monitorInfo.deviceImg|| noDataDevice : alarmInfo.squarePicUrl || noDataDevice" />
          <div class="flex-center flex-column width-100" >
            <div class="detail-data-item width-100 flex-space-between item-bg"
                 v-for="(item,index) in (isMonitor ? deviceDetailDataMonitor : (isError ? deviceDetailDataAlarm : deviceDetailData))" :key="index">
              <span class="color-text-main">{{ item.label }}</span>
              <div class="flex-start">
                <span
                      style="text-align: right"
                :style="{color:item.value == '待处理' ? '#FFA56E' : '#fff'}"
                >{{ item.value || '--' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="width-100 margin-t-16 margin-b-8 tip-area" v-if="!isMonitor && isError">
          <div class="detail-data-item width-100 flex-space-between item-bg width-100">
            <div class="flex-start flex-shrink-0">
              <svg-icon
                icon-class="alarm-tip"
                class="font-20 margin-r-4 tip-icon color-text-main"
              />
              <span class="color-text-main">风险提示</span>
            </div>
            <span class="color-text-white margin-l-40">{{ alarmInfo.riskWarning || '--' }}</span>
          </div>
          <div class="detail-data-item width-100 flex-space-between item-bg width-100">
            <div class="flex-start flex-shrink-0">
              <svg-icon
                icon-class="alarm-detail"
                class="font-20 margin-r-4 tip-icon color-text-main"
              />
              <span class="color-text-main">处理建议</span>
            </div>
            <span class="color-text-white margin-l-40" style="white-space: pre-wrap">{{ alarmInfo.opinion || '--' }}</span>

          </div>

        </div>


        <a-radio-group class="margin-t-16 margin-b-12"
                       v-if="isMonitor"
                       :value="activeTab" @change="changeTab">
          <a-radio-button :value="0">
            实时画面
          </a-radio-button>
          <a-radio-button :value="1" :disabled="alarmDisabled">
            告警详情
          </a-radio-button>
        </a-radio-group>

        <div class="flex-center"
             :class=" liveDatas.length == 2 || monitorAlarmList.length > 1 ?  'live-area-two' : 'live-area' "
             v-if="activeTab == 0 && isMonitor">
          <div v-for="(item,index) in liveDatas" :key="index"
               :class="{'margin-l-12':index == 1,'width-height-100':liveDatas.length != 2 && monitorAlarmList.length <= 1 ,'live-item-two':liveDatas.length == 2 || monitorAlarmList.length > 1}">
            <Live :url="item.liveUrl[0]" v-if="monitorInfo.onlineStatus == 1 && item.liveUrl.length" :psKey="item.psKey" />
            <div class="width-100 height-100 flex-center flex-column"  v-else>
              <div class="no-config"></div>
              <span class="font-14 color-text-second margin-t-4" v-if="monitorInfo.onlineStatus == 0">设备离线</span>
              <span class="font-14 color-text-second margin-t-4" v-if="item.liveUrl.length == 0 && monitorInfo.onlineStatus == 1">未配置</span>
            </div>
          </div>
        </div>

        <div class="alarm-area flex-start flex-row-wrap"
             v-if="activeTab == 1 && isMonitor"
             :style="{width:monitorAlarmList.length > 1 || liveDatas.length == 2 ? '600px' : '434px'}">
          <div class="flex-center width-100" v-if="monitorAlarmList.length == 0">
            <div class="no-data"></div>
          </div>
          <div class="flex-start flex-column alarm-item"
               :class=" monitorAlarmList.length > 1 || liveDatas.length == 2 ? 'margin-b-12' : '' "
               v-for="(item,index) in monitorAlarmList"
               :style="{width:monitorAlarmList.length > 1 || liveDatas.length == 2  ? '290px' : '434px'}"
               :key="index">
            <img :src="item.fileUrlThumbnail" @click="previewImage(item)" class="alarm-img" />
            <div class="width-100 flex-start flex-column relative">

              <di-throttle-button class="alarm-item-btn"
                                  :disabled="item.alarmStatus == '03'"
                                  label="确认"
                                   @click="confirmAlarm(index)"></di-throttle-button>

              <div class="flex-start margin-t-12 width-100" v-for="(el,elIndex) in item.infos" :key="elIndex">
                <svg-icon :icon-class="el.icon" class="font-20 margin-r-4" />
                <span class="color-text-white ">{{ el.label }}</span>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </a-spin>
</template>

<script>
import ModalTitle from "./ModalTitle";
import {affirmForSensor, getModelData} from "../../../api/2dMap/psOverview";
import Live from "../../../components/Live";
import {pLayLiveApi} from "../../../api/common";
import VueViewer from "@/mixins/VueViewer";

export default {
  name: "DeviceDetailPopup",
  components: {Live, ModalTitle},
  mixins:[VueViewer],
  data() {
    return {
      loading:false,
      isError:false,
      isMonitor:false,
      offline:false,
      alarmDisabled:false,
      liveDatas:[
        {
          liveUrl:[]
        }
      ],
      monitorURL:'',
      alarmInfo: {
        fileUrlThumbnail:''
      },
      monitorInfo:null,
      noDataDevice:require('@/assets/images/2dMap/no-data-device.png'),
      psKey:'',
      activeTab:0,
      deviceDetailData:[
        {
          label:'设备名称',
          key:'deviceName',
          value:null,
        },{
          label:'设备型号',
          key:'deviceModel',
          value:null
        },{
          label:'生产厂家',
          key:'maker',
          value:null
        }
      ],
      grades:['I类','II类','III类','IV类'],
      deviceDetailDataMonitor:[
        {
          label:'设备名称',
          key:'deviceName',
          value:null,
        },{
          label:'设备型号',
          key:'model',
          value:null
        },{
          label:'生产厂家',
          key:'factory',
          value:null
        },{
          label:'设备状态',
          key:'onlineStatus',
          value:null
        }
      ],
      deviceDetailDataAlarm:[
        {
          label:'故障类型',
          key:'alarmName',
          value:null,
        },{
          label:'发生时间',
          key:'alarmTime',
          value:null
        },{
          label:'处理状态',
          key:'alarmStatus',
          value:null
        },{
          label:'故障原因',
          key:'alarmReason',
          value:null
        }
      ],
      monitorAlarmInfo:[
        {
          label:'',
          icon:'2d-alarm-monitor',
        },{
          label:'',
          icon:'2d-alarm-warning',
        },{
          label:'',
          icon:'2d-alarm-time',
        }
      ],
      psaInfo:null,
      monitorAlarmList:[]
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  methods: {
    close(){
      this.$emit('close')
    },
    goDetail(){
      this.$emit('close')
      this.$emit('goDetailPopupBig', this.alarmInfo)
    },
    changeTab(e){
      this.activeTab = e.target.value
      if(this.activeTab  == 1){
        this.getMonitorAlarm()
      }
    },
    async confirmAlarm(index){
      if(this.monitorAlarmList[index].alarmStatus == '03'){
        return
      }

      this.$confirm({
        title: "是否确认",
        content: '确认后该告警信息表示已完成',
        centered: true,
        onOk: async () => {
          await affirmForSensor({
            psId:this.psaInfo.psId,
            id:this.monitorAlarmList[index].id,
            handleStatus:'03'
          })

          this.monitorAlarmList.splice(index,1)
          if(this.monitorAlarmList.length == 0){
            this.alarmDisabled = true
            this.activeTab = 0
          }
          this.$emit('refreshMapAlarm')
          this.$message.success("操作成功");
        },
      });

    },
    getDeviceImg(){
      if(this.isMonitor){
        return this.monitorInfo.fileUrlThumbnail
      } else {
        return this.alarmInfo.fileUrlThumbnail || this.noDataDevice
      }
    },
    previewImage(item){
      console.log(item)
      this.viewerImage({ images: [{ path: item.fileUrlThumbnail }] });
      // 烟火识别放大图片
      if(item.remark == '82'){
        let timer = setTimeout(()=>{
          this.$viewer.zoom(2)
          clearTimeout(timer)
          timer = null
        },750)
      }
    },
    async getMonitorAlarm(){
      let res = await getModelData({
        psId:this.psaInfo.psId,
        modelName:'alertList',
        monitorType:2,
        psKey:this.psKey
      })
      if(res.result_data.length == 0){
        this.alarmDisabled = true
        return
      } else {
        this.activeTab  = 1
      }
      this.monitorAlarmList = res.result_data.map(item=>{
        let monitorAlarmInfo = JSON.parse(JSON.stringify(this.monitorAlarmInfo))
        monitorAlarmInfo[0].label = item.deviceName
        monitorAlarmInfo[1].label = item.alarmReason
        monitorAlarmInfo[2].label = '发生时间：' + item.alarmTime
        return {
          src:item.src,
          id:item.id,
          alarmStatus:item.alarmStatus,
          alarmGrade:item.alarmGrade,
          remark:item.remark,
          fileUrlThumbnail:item.fileUrlThumbnail,
          infos:monitorAlarmInfo
        }
      })
    },
    async getUrl(){
      let res = await getModelData({
        psId:this.psaInfo.psId,
        modelName:'sonCameraList',
        uuid:this.monitorInfo.uuid
      })
      let arr  = res.result_data.dataResult.sonCameraInfo
      res = await pLayLiveApi({
        psaId:this.psaInfo.psaList[0],
        psId:this.psaInfo.psId,
        psKeys:arr.map(x => x.pskey).join(',')
      })
      if(res.length == 0){
        this.$message.error('暂无直播地址')
        return
      }
      this.liveDatas = res || []

    },
    async refreshData(deviceInfo){

      this.loading = true
      this.psKey = deviceInfo.psKey
      this.isError = deviceInfo.isError
      if(deviceInfo.deviceType == '54'){
        this.monitorInfo = deviceInfo
        this.isMonitor = true
        this.isError = false
        this.deviceDetailDataMonitor.map(item=>{
          item.value = deviceInfo[item.key]
        })
        this.deviceDetailDataMonitor[3].value = deviceInfo.onlineStatus == 1 ? '在线' : '离线'
        if(deviceInfo.onlineStatus == 1 ){
          this.getUrl()
        }
        this.loading = false
        this.getMonitorAlarm()
        return
      }

      if(this.isError){
        this.alarmInfo = deviceInfo
        this.deviceDetailDataAlarm.map(item=>{
          item.value = deviceInfo[item.key]
        })
        this.deviceDetailDataAlarm[2].value = deviceInfo.alarmStatus == '01' ? '待处理' :  '已派发'
        this.deviceDetailData[0].value = deviceInfo.deviceName
        this.loading = false
        return
      }

      let res = await getModelData({
        psId:this.psaId,
        psKey:this.psKey ,
        modelName:'deviceBaseInfo',
      }).catch(e=>{
        this.loading = false
        return
      })
      let data = res.result_data
      this.deviceDetailData.map(item=>{
        item.value = data[item.key]
      })
      this.$forceUpdate()
      this.loading = false
    }
  }
}
</script>

<style lang="less" scoped>
.tip-area{
  width: 479px;
}
.detail-device-popup-small{
  min-width: 466px;
  max-height:656px;
}
.alarm-area{
  height: 343px;
  overflow-y: scroll;
}
.tip-icon{
  width: 20px;
  height: 20px;
}
.modal-title{
  margin: 4px 2px 0;
  width: calc(100% - 8px);
}
.alarm-item-btn{
  position: absolute;
  top: 12px;
  right: 8px;
}
.detail-status{
  height: 18px;
  border-radius: 9px;
  padding: 0 8px;
}
.fault-cause{
  width: 100%;
  padding: 8px;
  background: #0F3B78;
}
.live-area{
  width: 434px;
  height: 244px;
}

.live-area-two{
  width: 600px;
  height: 163px;
}
.live-item-two{
  width: 294px;
  height: 100%;
}

.bg-normal{
  background: linear-gradient(180deg, #75C3FF 0%, #006EC7 100%);
}

.bg-abnormal{
  background: linear-gradient(180deg, #F07072 0%, #8C1B1D 100%);
}
.bg-offline{
  background: linear-gradient(180deg, #A4A4A4 0%, #6C6C6C 100%);
}

.alarm-img{
  width: 100%;
  height: 244px;
}

.alarm-item:nth-child(2n){
  margin-left: 12px;
}

.device-detail-img{
  width: 168px;
}
.detail-data-item{
  background: #0F3B78;
  padding: 4px 8px;
  margin-bottom: 8px;
}
.detail-data-item:last-of-type{
  margin-bottom: 0;
}

.detail-btn{
  padding: 0 8px;
  height: 24px;
  border-radius: 3px;
}

.detail-content-area{
  padding:8px 16px 24px;
  .detail-top{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;


  }
}

.title{
  width: 138px;
  height: 30px;
  background: url("../../../assets/images/2dMap/2d-booster-station-room-title-bg.png");
  background-size: cover;
}

</style>