<!-- 2d页面入口 -->
<template>
  <div class="map-2d width-100 height-100 relative" id="2dMap">
    <div class="left-area flex-start pointer-events-none transform-top-left" :style="{ transform: `scale(${indicatorScale})` }">
      <div class="height-100 flex-column left-top-menu" v-if="pageType != PAGE_TYPE_PS_OVERVIEW">
        <div class="breadcrumbs">
          <p>
            <span class="font-16 color-text-main">在线监测</span>

            <span class="margin-l-8 cursor-pointer pointer-events-auto" @click="clickBread">
              <svg-icon icon-class="arrow" class="font-16 color-text-main margin-r-8" />
              <span class="color-text-highlight font-16">
                {{ $dict.getDictValue(pageType, pageTypeTabs) }}
              </span>
            </span>
            <span v-if="monitorType != null">
              <svg-icon icon-class="arrow" class="font-16 color-text-main margin-r-8" />
              <span class="color-text-highlight font-16">{{ getBreadLevel3Data() }}</span>
            </span>
          </p>
        </div>
      </div>
      <Transition name="slide-fade-left" appear>
        <div class="height-100 left-indicators pointer-events-auto" key="leftIndicators" v-if="showLeft && pageType == PAGE_TYPE_PS_OVERVIEW">
          <indicator-item title="电站信息" class="margin-b-44" iconClass="ps-info" ref="PsInfos" @callBack="indicatorCallBack" component-name="PsInfos" height="210">
            <div class="width-100 flex-end">
              <div class="limit-bg flex-center margin-r-16" v-if="isPsLimit">
                <span class="color-text-white">限电中</span>
              </div>
            </div>
          </indicator-item>
          <indicator-item title="故障统计" iconClass="runing-status" ref="DeviceRunningStatus" class="margin-b-44" component-name="DeviceRunningStatus" height="250" />
          <indicator-item title="智能设备规模" ref="InductionDeviceScale" iconClass="hard-equipment" component-name="InductionDeviceScale" height="308" />
        </div>
      </Transition>

      <div class="height-100"></div>
      <div class="left-area-bg">
        <div
          class="pointer-events-auto"
          :class="pageType == PAGE_TYPE_PS_OVERVIEW ? (showLeft ? 'fold-left cursor-pointer ' : 'fold-left-arrow-right cursor-pointer') : 'no-fold-left'"
          @click="handleLeftFold"
        ></div>
        <div class="left-corner" v-show="showLeft || pageType != PAGE_TYPE_PS_OVERVIEW">
          <svg-icon icon-class="2d-corner-decorate" class="corner-1" />
          <svg-icon icon-class="2d-corner-decorate" class="corner-2 rotate-270" />
        </div>
      </div>
    </div>

    <div class="right-area transform-top-right" :style="{ transform: `scale(${indicatorScale})` }">
      <Transition name="slide-fade-right" appear>
        <div class="right-real-time-alarm" key="right-real-time-alarm" v-if="showAlarm">
          <indicator-item
            title="实时异常告警"
            component-name="RealTimeAlarm"
            @callBack="indicatorCallBack"
            :data-params="{ alarmId: alarmId, isFlying: isFlying }"
            @refreshMapAlarm="refreshMapAlarm"
            :dockId="dockId"
            ref="realTimeAlarm"
            iconClass="warning"
            title-position="right"
            height="920"
          >
            <div class="width-100 flex-space-between">
              <div class="flex-center close-alarm cursor-pointer border-main cursor-pointer" @click="handleAlarmShow(false)">
                <svg-icon icon-class="close" class="close-alarm-icon color-text-main" />
              </div>
              <div class="alarm-num-in-title num-font-700 margin-r-8 flex-center">
                <span class="color-text-white">
                  {{ alarmTotalCount }}
                </span>
              </div>
            </div>
          </indicator-item>
        </div>
      </Transition>

      <Transition name="slide-fade-right" appear>
        <div class="right-indicators" key="right-indicators" v-if="showRight && !showAlarm">
          <!--电站概览-->
          <template v-if="pageType == PAGE_TYPE_PS_OVERVIEW">
            <indicator-item
              title="大门监控"
              ref="fireMonitor"
              :data-params="{ areaType: null, deviceSubType: 50 }"
              component-name="ImportantMonitorVideo"
              height="300"
              class="margin-b-12"
              icon-class="door-monitor"
              title-position="right"
              :videoDisplayNum="1"
            />
            <indicator-item
              title="火情监控"
              ref="fireMonitor"
              :data-params="{ areaType: null, deviceSubType: 50 }"
              component-name="ImportantMonitorVideo"
              height="300"
              class="margin-b-12"
              icon-class="fire-monitor"
              title-position="right"
              :videoDisplayNum="1"
            />
            <indicator-item
              title="周界防范"
              ref="perimeterMonitor"
              :data-params="{ areaType: null, deviceSubType: 51 }"
              component-name="ImportantMonitorVideo"
              height="300"
              icon-class="perimeter-monitor"
              title-position="right"
              :videoDisplayNum="1"
            />
          </template>
          <!--光伏区-->
          <template v-if="PAGE_TYPE_PHOTOVOLTAIC_AREA == pageType">
            <template v-if="monitorType == null">
              <indicator-item
                title="监控设备情况"
                :data-params="{ areaType: 1 }"
                iconClass="monitor"
                key="MonitorDeviceCondition1"
                component-name="MonitorDeviceCondition"
                height="121"
                class="margin-b-12"
                title-position="right"
              />
              <indicator-item
                title="火情监控"
                key="fireMonitor2"
                :data-params="{ areaType: 1, deviceSubType: 50 }"
                component-name="ImportantMonitorVideo"
                height="300"
                class="margin-b-12"
                icon-class="fire-monitor"
                title-position="right"
              />
              <indicator-item
                title="周界防范"
                key="perimeterMonitor2"
                :data-params="{ areaType: 1, deviceSubType: 51 }"
                component-name="ImportantMonitorVideo"
                height="300"
                class="margin-b-12"
                icon-class="perimeter-monitor"
                title-position="right"
              />
              <indicator-item
                title="告警列表"
                component-name="AlarmList"
                ref="AlarmList2"
                height="200"
                key="AlarmList2"
                :data-params="{ areaType: 1, monitorType: 2, noFile: 1 }"
                @callBack="indicatorCallBack"
                icon-class="alarm"
                title-position="right"
              >
                <div class="width-100 flex-end">
                  <div class="alarm-num-in-title num-font-700 margin-r-8 flex-center">
                    <span class="color-text-white">
                      {{ realTimeAlarmNum }}
                    </span>
                  </div>
                </div>
              </indicator-item>
            </template>
          </template>
          <!--升压站-->
          <template v-if="pageType == PAGE_TYPE_BOOSTER_STATION">
            <template v-if="monitorType == null">
              <indicator-item
                title="监控设备情况"
                :data-params="{ areaType: 2 }"
                iconClass="monitor"
                key="MonitorDeviceCondition2"
                component-name="MonitorDeviceCondition"
                height="121"
                class="margin-b-12"
                title-position="right"
              />
              <indicator-item
                title="火情监控"
                key="fireMonitor3"
                :data-params="{ areaType: 2, deviceSubType: 50 }"
                component-name="ImportantMonitorVideo"
                height="300"
                class="margin-b-12"
                icon-class="fire-monitor"
                title-position="right"
              />
              <indicator-item
                title="周界防范"
                key="perimeterMonitor3"
                :data-params="{ areaType: 2, deviceSubType: 51 }"
                component-name="ImportantMonitorVideo"
                height="300"
                class="margin-b-12"
                icon-class="perimeter-monitor"
                title-position="right"
              />
              <indicator-item
                title="告警列表"
                key="AlarmList5"
                ref="AlarmList5"
                @callBack="indicatorCallBack"
                iconClass="alarm"
                :data-params="{ areaType: 2, monitorType: 2, noFile: 1 }"
                component-name="AlarmList"
                title-position="right"
                height="200"
              >
                <div class="width-100 flex-end">
                  <div class="alarm-num-in-title num-font-700 margin-r-8 flex-center">
                    <span class="color-text-white">
                      {{ realTimeAlarmNum }}
                    </span>
                  </div>
                </div>
              </indicator-item>
            </template>
          </template>
        </div>
      </Transition>

      <div class="right-area-bg">
        <div
          class="fold-right"
          @click="handleRightFold"
          :class="pageType == PAGE_TYPE_PS_OVERVIEW ? (showRight ? 'has-fold cursor-pointer' : 'fold-right-arrow-left cursor-pointer') : 'no-fold'"
        ></div>
        <div class="right-corner" v-show="showRight">
          <svg-icon icon-class="2d-corner-decorate" class="corner-3 rotate-90" />
          <svg-icon icon-class="2d-corner-decorate" class="corner-4 rotate-180" />
        </div>
      </div>
    </div>

    <div class="page-bgs pointer-events-none">
      <div class="left-page-bg" v-if="showLeft"></div>
      <div class="right-page-bg" v-if="showRight || showAlarm"></div>
      <div class="top-page-bg"></div>
      <div class="bottom-page-bg"></div>
    </div>

    <a-modal
      v-model="everyPopupShow"
      :closable="false"
      :maskClosable="false"
      centered
      destroyOnClose
      :footer="null"
      :zIndex="999"
      class="device-detail-modal-small"
      width=""
      :bodyStyle="modalStyleDevicePopupSmall"
      :getContainer="getDom"
      title=""
    >
      <device-detail-popup ref="device-detail-small" @refreshMapAlarm="refreshMapAlarm" @goDetailPopupBig="showDetailModal" @close="everyPopupShow = false" />
    </a-modal>
    <!--底部操作栏-->
    <div class="bottom-menu flex-start">
      <div v-if="monitorType != null" @click="goBack" class="tab-item flex-center text-blod">
        <span>返回</span>
      </div>

      <div v-else class="width-100 flex-start">
        <div
          v-for="(item, index) in pageTypeTabs"
          :key="index"
          class="tab-item flex-center text-blod"
          :class="{ 'active-tab': pageType == item.value }"
          @click="activeTab(item.value)"
          v-has="item.auth"
        >
          <span>{{ item.label }}</span>
        </div>
      </div>
    </div>
    <weather-full-screen ref="weatherInMap" :alarmType="mapWeatherAlarmType" />
    <a-modal
      v-model="detailModalShow"
      width="65%"
      :closable="false"
      :maskClosable="false"
      centered
      destroyOnClose
      :footer="null"
      :zIndex="1000"
      :bodyStyle="modalStyle"
      :getContainer="getDom"
      title=""
      on-ok="handleOk"
    >
      <device-detail-drawer :nowDetailParams="nowDetailParams" @refreshMapAlarm="refreshMapAlarm" @close="detailModalShow = false" />
    </a-modal>
    <a-modal v-model="recheckShow" :width="340" :closable="false" :maskClosable="false" centered :footer="null" :zIndex="999" :bodyStyle="recheckModalStyle" :getContainer="getDom" title="">
      <recheck-result ref="recheckResult" @close="recheckShow = false" />
    </a-modal>

    <a-modal v-model="weatherPopupShow" :width="344" :closable="false" :maskClosable="false" centered :footer="null" :zIndex="999" :bodyStyle="weatherAlarmModalStyle" :getContainer="getDom" title="">
      <weather-popup ref="weatherPopup" :alarmInfo="weatherAlarmInfo" @close="weatherPopupShow = false" />
    </a-modal>

    <live-window ref="liveWindow" v-if="showLiveWindow" @addTask="addTask" @endTask="endTask" @close="showLiveWindow = false" />
  </div>
</template>

<script>
import IndicatorItem from './modules/IndicatorItem';
import LiveWindow from './modules/LiveWindow';
import CustomMap from './modules/CustomMap.vue';
import DeviceDetailDrawer from './modules/DeviceDetailDrawer';
import RecheckResult from './modules/RecheckResult';

const PAGE_TYPE_PS_OVERVIEW = 'PAGE_TYPE_OVERVIEW'; // 电站概览
const PAGE_TYPE_PHOTOVOLTAIC_AREA = 'PAGE_TYPE_PHOTOVOLTAIC_AREA'; // 光伏区
const PAGE_TYPE_BOOSTER_STATION = 'PAGE_TYPE_BOOSTER_STATION'; // 升压站
const PAGE_TYPE_WIND_FARM = 'PAGE_TYPE_WIND_FARM'; // 风电场
const PAGE_TYPE_COLLECTING_LINE = 'PAGE_TYPE_COLLECTING_LINE'; // 集电线路

const DEVICE_MONITOR = 'DEVICE_MONITOR'; // 设备监测
const ENVIRONMENT_MONITOR = 'ENVIRONMENT_MONITOR'; // 环境监测

const normalHeight = 1080; // 原始高度 此高度下展示UI尺寸

import ButtonTab from '../../components/com/ButtonTab';
import DeviceDetailPopup from './modules/DeviceDetailPopup';
import { getDockList, addTask, endTask } from '@/api/2dMap/map';
import { boostDevices } from './mixins/boostDevices';
import WeatherFullScreen from './modules/WeatherFullScreen';
import WeatherPopup from './modules/WeatherPopup';
import { listForSensorApi } from '@/api/health/AlarmEvents';
import { getNewListByAuth } from '@/utils/util';
import { clone } from 'xe-utils';
import { mapState } from 'vuex';
import { EventBus } from './modules/eventBus';

export default {
  name: 'index',
  components: {
    WeatherPopup,
    WeatherFullScreen,
    DeviceDetailPopup,
    IndicatorItem,
    ButtonTab,
    DeviceDetailDrawer,
    RecheckResult,
    LiveWindow,
    CustomMap
  },
  data() {
    return {
      PAGE_TYPE_PS_OVERVIEW: PAGE_TYPE_PS_OVERVIEW,
      PAGE_TYPE_PHOTOVOLTAIC_AREA: PAGE_TYPE_PHOTOVOLTAIC_AREA,
      PAGE_TYPE_BOOSTER_STATION: PAGE_TYPE_BOOSTER_STATION,
      DEVICE_MONITOR: DEVICE_MONITOR,
      ENVIRONMENT_MONITOR: ENVIRONMENT_MONITOR,

      pageType: PAGE_TYPE_PS_OVERVIEW, // 页面类型
      monitorType: null, // 页面状态

      doubleArrowSrc: require('@/assets/images/2dMap/double-arrow.png'),

      pageTypeTabs: [
        {
          label: '全站概览',
          value: PAGE_TYPE_PS_OVERVIEW,
          auth: 'ps_overView'
        },
        {
          label: '风电场',
          value: PAGE_TYPE_WIND_FARM,
          auth: 'wind_farm'
        },
        {
          label: '光伏区',
          value: PAGE_TYPE_PHOTOVOLTAIC_AREA,
          auth: 'photovoltaic_area'
        },
        {
          label: '升压站',
          value: PAGE_TYPE_BOOSTER_STATION,
          auth: 'boot_station'
        },
        {
          label: '集电线路',
          value: PAGE_TYPE_COLLECTING_LINE,
          auth: 'collecting_line'
        }
      ],
      generateElectricityOptions: [
        {
          label: '日',
          value: 1
        },
        {
          label: '月',
          value: 2
        },
        {
          label: '年',
          value: 3
        }
      ],
      monitorTypeTabs: [
        {
          label: '设备监测',
          value: DEVICE_MONITOR,
          auth: 'photovoltaic_area:device'
        },
        {
          label: '环境监测',
          value: ENVIRONMENT_MONITOR,
          auth: 'photovoltaic_area:enviroment'
        }
      ],

      deviceTypes: [
        {
          label: '箱变',
          value: '6-4',
          zoomMin: 16,
          icon: '2d-left-transformer'
        },
        {
          label: '逆变器',
          value: '1',
          zoomMin: 17,
          icon: '2d-left-inverter'
        },
        {
          label: '汇流箱',
          value: '4',
          zoomMin: 19,
          icon: '2d-left-combiner'
        },
        {
          label: '组串',
          value: '10',
          zoomMin: 22,
          icon: '2d-left-string'
        }
      ],

      showLeft: true,
      showRight: true,
      // showAlarm: false,
      detailModalShow: false,
      isInBoosterStationRoom: false,
      showDevice: false,
      recheckShow: false,
      isPsLimit: false,
      weatherPopupShow: false, // 天气告警弹窗
      weatherAlarmInfo: {}, // 天气告警弹窗详情
      mapWeatherAlarmType: '', // 地图天气预警 ''表示没有
      mapAlarmList: [], // 地图轮播告警数据
      cyclicAlarmTimer: null, // 地图轮播定时器
      psOverviewRefreshTimer: null, // 电站概览数据刷新 5min
      mapAlarmIconRefreshTimer: null, // 地图告警图标刷新 5min
      roomType: 2,
      everyPopupShow: false, //点击位置的弹窗show
      anchorX: 0,
      anchorY: 0,
      realTimeAlarmNum: 0,
      nowAlarmIndex: 0,
      // totalAlarmNum: 0,
      activeDeviceType: null,
      nowDetailParams: {},
      areaData: [], // 方阵列表
      drawIconDeviceType: ['54', '4', '1', '6-4', '10'],
      modalStyle: {
        width: '100%',
        height: '605px',
        padding: 0
      },
      recheckModalStyle: {
        padding: 0
      },
      weatherAlarmModalStyle: {
        padding: 0,
        borderRadius: '4px'
      },
      modalStyleDevicePopupSmall: {
        padding: 0
      },
      indicatorScale: 1,
      mapZoom: 15,
      psaInfo: null,
      alarmId: undefined,
      showLiveWindow: false,
      dockId: '',
      droneId: '',
      activeDateType: 1,
      boosterPositions: [
        { latitude: 31.95549118828706, longitude: 117.57232342247744 },
        { latitude: 31.954478868949124, longitude: 117.57058379248721 }
      ],
      photovolataicAreaPositions: [
        { latitude: 31.95549118828706, longitude: 117.57232342247744 },
        { latitude: 31.954478868949124, longitude: 117.57058379248721 }
      ],
      taskId: '',
      deviceId: '',
      backPageType: '', // 点击底部tab 返回时 返回的页面类型
      isFlying: false,
      prevShowRight: null, // 上一步是否显示右侧
      imageUrl: require('@/assets/images/2dMap/map.webp')
    };
  },
  created() {
    EventBus.$on('handleAlarmShowNotAnimation', this.handleAlarmShowNotAnimation);
    EventBus.$on('handleAlarmShow', this.handleAlarmShow);
    this.pageTypeTabs = getNewListByAuth(this.pageTypeTabs);
    if (this.pageTypeTabs.length > 0) {
      this.pageType = this.pageTypeTabs[0].value;
    }
    setTimeout(() => {
      this.activeTab(this.pageType, this.pageType != PAGE_TYPE_BOOSTER_STATION);
    }, 500);
    this.psaInfo = this.$ls.get('PSA_INFO');
    // this.getDockList();
  },
  mounted() {
    // 初始化高度监听
    window.addEventListener('resize', this.resizeIndicatorSize);
    let height = document.documentElement.clientHeight;
    this.indicatorScale = (height - 64 * this.$util.getZoom()) / (normalHeight - 64 * this.$util.getZoom());
    this.initPsOverview();
    this.initMapAlarmIconRefresh();
  },
  computed: {
    ...mapState({
      showAlarm: (state) => state.app.showAlarm,
      alarmTotalCount: (state) => state.app.alarmTotalCount
    })
  },
  beforeDestroy() {
    window.addEventListener('remove', this.resizeIndicatorSize);
    clearInterval(this.cyclicAlarmTimer);
    this.cyclicAlarmTimer = null;
    clearInterval(this.mapAlarmIconRefreshTimer);
    this.mapAlarmIconRefreshTimer = null;
    clearInterval(this.psOverviewRefreshTimer);
    this.psOverviewRefreshTimer = null;
    this.$store.commit('app/SET_SHOW_ALARM', false);
    EventBus.$off('handleAlarmShowNotAnimation', this.handleAlarmShowNotAnimation);
    EventBus.$off('handleAlarmShow', this.handleAlarmShow);
  },
  methods: {
    // 更改发电情况选择
    changeGenerateType(value) {
      this.$refs.GenerateElectricityCondition.refreshData(value);
    },

    getMapZoom(zoom) {
      this.mapZoom = zoom;
      this.deviceTypes.map((item) => {
        if (item.zoomMin > zoom && this.activeDeviceType == item.value) {
          this.changeDeviceType(this.activeDeviceType);
        }
      });
    },

    recheck(data) {
      this.showLiveWindow = true;
      this.dockId = data.dockId;
      this.droneId = data.droneId;
      this.$nextTick(() => {
        this.$refs.liveWindow.getUrl(this.dockId, data.psKey, 'flyURL1');
        this.$refs.liveWindow.getUrl(this.droneId, data.psKey, 'flyURL2');
        this.$refs.liveWindow.showLiveBtn = true;
        this.$refs.liveWindow.isInFlight = false;
      });
    },
    // 初始化电站概览 接口刷新
    initPsOverview() {
      this.initPsOverviewRefresh();
      this.checkIsWeatherAlarm();
    },

    async checkIsWeatherAlarm() {
      let res = await listForSensorApi({
        alarmStatus: '01',
        handleStatus: '01',
        size: 10,
        curPage: 1,
        alarmRemarkList: ['85']
      });
      const { pageList } = res.result_data;
      if (pageList.length == 0) {
        this.weatherPopupShow = false;
      } else {
        this.weatherPopupShow = true;
        this.weatherAlarmInfo = {
          src: pageList[0].fileDetail ? pageList[0].fileDetail.path : '',
          text: pageList[0].weather ? pageList[0].weather.type : null,
          time: pageList[0].happenTime,
          id: pageList[0].id
        };
      }
    },

    // 初始化地图告警图标刷新
    initMapAlarmIconRefresh() {
      this.mapAlarmIconRefreshTimer = setInterval(() => {
        this.refreshMapAlarm();
      }, 5 * 60 * 1000);
    },

    clickBread() {
      this.monitorType = null;
      this.isInBoosterStationRoom = false;
      this.$refs.map.removeMonitorLayers();
    },

    clearPsOverviewTimers() {
      clearInterval(this.psOverviewRefreshTimer);
      this.psOverviewRefreshTimer = null;
      this.closeMapRecyic();
    },

    setPsLimit(e) {
      this.isPsLimit = e;
    },

    closeMapRecyic() {
      this.$refs.map?.stopCyclic();
    },

    initPsOverviewRefresh() {
      clearInterval(this.psOverviewRefreshTimer);
      this.psOverviewRefreshTimer = null;
      this.psOverviewRefreshTimer = setInterval(() => {
        if (this.showLeft) {
          // this.$refs.PsInfos.refreshData();
          this.activeDateType = 1;
          this.$refs.DeviceRunningStatus.refreshData();
          this.$refs.DailyGenerateElectricityTrend.refreshData();
          this.$refs.GenerateElectricityCondition.refreshData(1);
        }
        if (this.showRight && !this.showAlarm) {
          this.$refs.InductionDeviceScale.refreshData();
          // this.$refs.fireMonitor.refreshData();
          // this.$refs.perimeterMonitor.refreshData();
          // this.$refs.ElectricQuantityLoss.refreshData();
        }
      }, 5 * 60 * 1000);
    },

    refreshMapAlarm() {
      console.log('刷新地图报警');
      if (!this.isInBoosterStationRoom) {
        this.$refs.map.refreshAlarmMarkers(true);
      }
      // 环境监测告警确认后 刷新当前界面的告警列表指标卡

      if (this.monitorType == ENVIRONMENT_MONITOR && this.pageType == PAGE_TYPE_BOOSTER_STATION && this.$refs.AlarmList5) {
        this.$refs.AlarmList5.refreshData();
      }

      if (this.monitorType == ENVIRONMENT_MONITOR && this.pageType == PAGE_TYPE_PHOTOVOLTAIC_AREA && this.$refs.AlarmList2) {
        this.$refs.AlarmList2.refreshData();
      }

      if (this.monitorType == null && this.pageType == PAGE_TYPE_PHOTOVOLTAIC_AREA && this.$refs.AlarmList1) {
        this.$refs.AlarmList1.refreshData();
      }
      if (this.monitorType == null && this.pageType == PAGE_TYPE_BOOSTER_STATION && this.$refs.AlarmList3) {
        this.$refs.AlarmList3.refreshData();
      }
      if (this.monitorType == DEVICE_MONITOR && this.pageType == PAGE_TYPE_PHOTOVOLTAIC_AREA && this.$refs.AlarmList4) {
        this.$refs.AlarmList4.refreshData();
      }
    },

    openRealtimeWindowFromMap(item) {
      this.prevShowRight = clone(this.showRight, true);
      this.handleAlarmShow(true, item);
    },

    openRealtimeWindow(item) {
      this.alarmId = item ? item.id : undefined;
      this.closeMapRecyic();
      this.showRight = false;
      setTimeout(() => {
        // this.showAlarm = true;
        this.$store.commit('app/SET_SHOW_ALARM', true);
      }, 350);
    },

    // 地图设备/摄像头点击回调
    deviceClickInMap(e, data) {
      if (!data.isError && data.deviceType != '54') {
        this.nowDetailParams = {
          psKey: data.psKey,
          shadowPsKey: data.shadowPsKey || '',
          deviceName: data.deviceName || data.name,
          deviceType: data.deviceType
        };
        this.detailModalShow = true;
        return;
      }
      this.everyPopupShow = true;

      this.$nextTick(() => {
        if (this.$refs['device-detail-small']) {
          this.$refs['device-detail-small'].refreshData(data);
        } else {
          let timer = setTimeout(() => {
            clearTimeout(timer);
            timer = null;
            this.$refs['device-detail-small'].refreshData(data);
          }, 500);
        }
      });
    },

    // 地图点击方阵 右侧交互
    areaLayerClick(e, matrix) {
      // 如果是从电站概览
      this.backPageType = this.pageType;
      let index = this.areaData.findIndex((item) => item.id == matrix.id);
      this.activeTab(PAGE_TYPE_PHOTOVOLTAIC_AREA, false);
      this.activeMonitorType(DEVICE_MONITOR);
      this.$nextTick(() => {
        this.$refs.MatrixList && this.$refs.MatrixList.changeMatrix(index, true);
      });
    },

    // 点击升压站设备
    boostDeviceClick(e, marker) {
      if (marker.psKey) {
        this.nowDetailParams = {
          psKey: marker.psKey,
          deviceName: marker.name,
          deviceType: ''
        };
        this.detailModalShow = true;
      } else {
        if (marker.roomType || marker.name == '综合楼' || marker.name == '库房') {
          return;
        }

        this.nowDetailParams = {
          psKey: marker.psKey,
          deviceName: marker.name,
          belong110kVPointKey: marker.belong110kVPointKey || null,
          deviceType: ''
        };
        this.detailModalShow = true;
      }
    },

    // 告警列表选中地图设备
    selectAlarm(e) {
      this.$refs.map.setDeviceMarkerCenter(e);
    },

    setAlarmNum(e) {
      this.realTimeAlarmNum = e.num;
    },
    setAlarmNumTotal(num) {
      // this.totalAlarmNum = num;
    },

    getAreaData(e) {
      this.areaData = e.map((item, index) => {
        return {
          ...item,
          grade: 0
        };
      });
    },

    showDetailModal(e) {
      this.nowDetailParams = {
        psKey: e.psKey,
        deviceName: e.deviceName,
        deviceType: e.deviceType
      };
      this.detailModalShow = true;
    },

    // 指标的回调方法统一回调
    indicatorCallBack(e) {
      this[e.type](e.params);
    },

    clickBooterRoom(e) {
      this.activeMonitorType(DEVICE_MONITOR);
      this.backPageType = this.pageType;
      this.activeTab(PAGE_TYPE_BOOSTER_STATION, false);
      this.$nextTick(() => {
        this.$refs.AlarmList4.setRoom(e);
      });
      this.goStationRoom(e);
    },

    // type 2 配电楼 3 二次设备室
    goStationRoom(params) {
      this.roomType = params.value;
      if (this.roomType == 1) {
        this.isInBoosterStationRoom = false;
      } else {
        this.isInBoosterStationRoom = true;
      }
    },

    changeMatrix(e) {
      this.$refs.map.acticeAreaClick(e.area.uniqueIndex);
    },

    // 初始化高度监听
    resizeIndicatorSize(e) {
      let height = e.target.innerHeight;
      this.indicatorScale = (height - 64 * this.$util.getZoom()) / (normalHeight - 64 * this.$util.getZoom());
    },

    handleLeftFold() {
      if (this.pageType != PAGE_TYPE_PS_OVERVIEW) {
        this.showLeft = false;
        return;
      }
      this.showLeft = !this.showLeft;
    },
    handleRightFold() {
      if (this.pageType == PAGE_TYPE_PS_OVERVIEW) {
        this.prevShowRight = clone(this.showRight, true);
        // this.showAlarm = false;
        this.$store.commit('app/SET_SHOW_ALARM', false);
        setTimeout(() => {
          this.showRight = !this.showRight;
        }, 350);
      }
    },

    activeMonitorType(type) {
      if (type == ENVIRONMENT_MONITOR) {
        this.$refs.map.addMonitorLayers();
        // 清除告警的设备
        this.$refs.map?.setDeviceMarksAlarmOpacity(0);
        // 还原对环境监测的摄像头告警
        this.monitorType && this.$refs.map?.setEnvironmentMonitorAlarm('display');
      } else {
        this.$refs.map.removeMonitorLayers();
        // 还原告警的设备
        this.monitorType && this.$refs.map?.setDeviceMarksAlarmOpacity(1);
        // 清除对环境监测的摄像头告警
        this.$refs.map?.setEnvironmentMonitorAlarm('none');
      }
      this.monitorType = type;
      this.isInBoosterStationRoom = false;
    },

    changeDeviceType(type, zoom) {
      if (this.activeDeviceType == type) {
        this.activeDeviceType = undefined;
        // this.$refs.map.setZoom(zoom)
        this.$refs.map.setDeviceMarkersHighlight(type, false);
      } else {
        this.$refs.map.setDeviceMarkersHighlight(this.activeDeviceType, false);
        this.activeDeviceType = type;
        this.$refs.map.setZoom(zoom);
        this.$refs.map.setDeviceMarkersHighlight(type, true);
      }
    },
    getBreadLevel3Data() {
      if (this.isInBoosterStationRoom) {
        return this.roomType == 3 ? '二次设备室' : '配电楼';
      } else {
        return this.$dict.getDictValue(this.monitorType, this.monitorTypeTabs);
      }
    },
    handleAlarmShow(value, alarm) {
      if (!value) {
        // this.showAlarm = false;
        this.$store.commit('app/SET_SHOW_ALARM', false);
        setTimeout(() => {
          this.showRight = this.prevShowRight;
        }, 350);
      } else {
        this.prevShowRight = clone(this.showRight, true);
        this.openRealtimeWindow(alarm);
      }
    },
    // 其他路由跳转至“在线监测”，打开告警
    handleAlarmShowNotAnimation() {
      this.showRight = false;
      this.prevShowRight = true;
      this.alarmId = undefined;
      this.closeMapRecyic();
    },
    activeTab(type, move = true) {
      if (this.pageType == type) {
        return;
      }
      this.$store.commit('app/SET_SHOW_ALARM', false);

      this.pageType = type;
    },
    // 返回
    goBack() {
      if (this.backPageType) {
        this.activeTab(this.backPageType);
        this.backPageType = '';
      }
      if (this.monitorType === ENVIRONMENT_MONITOR) {
        // 环境检测时返回需要重置告警的设备
        this.$refs.map.setDeviceMarksAlarmOpacity(1);
      } else if (this.monitorType === DEVICE_MONITOR) {
        // 设备检测时返回需要重置对环境告警的摄像头
        this.$refs.map.setEnvironmentMonitorAlarm('display');
      }
      this.monitorType = null;
      this.isInBoosterStationRoom = false;
      this.$refs.map.removeMonitorLayers();
    },
    // 获取机库id
    getDockList() {
      let params = { page: 1, pageSize: 10, psId: this.psaInfo.psId };
      getDockList(params).then((res) => {
        if (res.payload && res.payload.list && res.payload.list.length > 0) {
          let arr = res.payload.list.filter((item) => {
            return !!item.status;
          });
          if (arr.length > 0) {
            this.dockId = arr[0].deviceSn;
            this.droneId = arr[0].childDeviceSn;
          }
        }
      });
    },
    // 新建无人机定点复检任务
    addTask(deviceId) {
      let params = {
        deviceId: deviceId,
        psaId: this.psaInfo.psaList[0],
        dockId: this.dockId
      };
      addTask(params).then((res) => {
        this.taskId = res.payload.taskId;
        this.deviceId = deviceId;
        localStorage.setItem('lastDeviceId', deviceId);
        this.$refs.map.flightId = res.payload.flightId;
        this.$refs.map.droneId = res.payload.droneId;
        this.$refs.map.getAirLine(true, res.payload.waylineId);
        this.$refs.liveWindow.isInFlight = true;
      });
    },
    // 结束无人机定点复检任务
    endTask() {
      endTask({ taskId: this.taskId }).then((res) => {
        this.$refs.map.isEndTask = true;
        this.$refs.map.cleanLine(true);
        this.$refs.liveWindow.showLiveBtn = false;
      });
    },
    setRecheckShow(data) {
      this.recheckShow = true;
      this.$nextTick(() => {
        this.$refs.recheckResult.initData(data.data);
      });
    },
    getDom() {
      return document.getElementById('2dMap');
    },
    // 点击无人机图标打开直播弹框
    openLiveWindow() {
      if (!this.showLiveWindow) {
        this.recheck({
          psKey: this.deviceId,
          dockId: this.dockId,
          droneId: this.droneId
        });
        this.$nextTick(() => {
          this.$refs.liveWindow.isInFlight = true;
        });
      }
    },
    // 关闭直播弹窗并刷新无人机复检任务状态
    closeLiveWindow() {
      if (this.$refs.realTimeAlarm) {
        this.$refs.realTimeAlarm.getListTaskStatus();
      }
      if (this.showLiveWindow && this.$refs.liveWindow) {
        this.$refs.liveWindow.showLiveBtn = false;
        this.$refs.liveWindow.flyURL2 = '';
      }
    },
    getDroneVideo() {
      let liveWindow = this.$refs.liveWindow;
      if (this.showLiveWindow && liveWindow && !liveWindow.flyURL2 && !liveWindow.loading) {
        liveWindow.getUrl(this.droneId, this.deviceId, 'flyURL2');
      }
    },
    // 切换页面返回后，展示正在飞行的航线
    showFlyingLine(data) {
      this.deviceId = data.deviceId;
      this.recheck(data);
      this.$nextTick(() => {
        this.$refs.liveWindow.isInFlight = true;
      });
    },
    // 无人机是否正在飞行
    setIsFlying(val) {
      this.isFlying = val;
    }
  }
};
</script>

<style lang="less">
.every-popup {
  max-width: 632px !important;
  max-height: 656px !important;
  padding: 0 !important;

  .ant-tooltip-content {
    width: 100%;
    height: 100%;

    .ant-tooltip-inner {
      width: 100%;
      height: 100%;
      padding: 0;
    }
  }

  .ant-tooltip-arrow {
    display: none !important;
  }
}

.map-2d {
  .ant-modal {
    border-radius: 4px;
    overflow: hidden;
    padding-bottom: 0px !important;
  }
}

.ant-radio-button-wrapper-disabled {
  border: #3063a5 1px solid !important;
}
</style>

<style lang="less" scoped>
.slide-fade-left-leave-to {
  transform: translateX(-368px);
  opacity: 0;
}

.slide-fade-left-enter {
  transform: translateX(-368px);
}

.slide-fade-right-enter-active,
.slide-fade-left-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-right-leave-active,
.slide-fade-left-leave-active {
  transition: all 0.3s linear;
}

.slide-fade-right-leave-to {
  transform: translateX(368px);
  opacity: 0;
}

.slide-fade-right-enter {
  transform: translateX(368px);
}

.transform-top-left {
  transform-origin: top left;
}

.transform-top-right {
  transform-origin: top right;
}

.anchor-div-for-popup {
  position: fixed;
  width: 1px;
  height: 1px;
}

.close-alarm {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  font-size: 12px;

  .close-alarm-icon {
    transform: scale(0.8);
    transform-origin: center;
  }
}

.alarm-num-in-title {
  height: 21px;
  border-radius: 9px;
  line-height: 18px;
  text-align: center;
  padding: 0 6px;
  background: linear-gradient(180deg, #fa8c8c 0%, #e44242 43%, #86688d 100%);
  box-sizing: border-box;
  border: 0.5px solid @di-color-text-white;
  z-index: 1;
}

.active-menu {
  background: url('../../assets/images/2dMap/2d-menu-active.svg') !important;
  position: relative;
}

.active-menu-bar::after {
  content: ' ';
  position: absolute;
  width: 52px;
  height: 2px;
  left: 50%;
  transform: translate(-50%);
  bottom: 1px;
  background: #bbe0fe;
}

.left-menu {
  width: 129px;
  height: 41px;
  background: url('../../assets/images/2dMap/2d-menu.svg');
  margin-bottom: 16px;
}

.left-menu:hover {
  background: url('../../assets/images/2dMap/2d-menu-hover.png');
  background-size: contain;
  color: #bde2ff;
}

.left-device-type-menu {
  position: absolute;
  left: 50px;
  top: 50%;
  transform: translateY(-50%);

  .left-device-type-menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
}

.left-menu:last-of-type {
  margin-bottom: 0;
}

.bottom-menu {
  position: fixed;
  bottom: 44px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;

  .tab-item {
    cursor: pointer;
    width: 136px;
    height: 50px;
    margin-right: 16px;
    background: url('../../assets/images/2dMap/2d-bottom-tab.svg');
    background-size: cover;
    color: #61a1d5;

    span {
      margin-bottom: 4px;
    }
  }

  .tab-item:hover {
    background: url('../../assets/images/2dMap/2d-bottom-tab-hover.svg');
    color: #85caff;
  }

  .tab-item:last-of-type {
    margin-right: 0;
  }

  .active-tab {
    background: url('../../assets/images/2dMap/2d-bottom-tab-active.svg') !important;
    color: #fff !important;

    span {
      margin-bottom: 2px;
    }
  }
}

.left-area-bg {
  z-index: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;

  .left-corner {
    position: absolute;
    left: 30px;
    top: 0;
    height: 1010px;
    font-size: 33px;

    .corner-1 {
      left: 0;
      position: absolute;
      top: 12px;
    }

    .corner-2 {
      left: 0;
      position: absolute;
      bottom: 36px;
    }
  }
}

.fold-left {
  width: 32.56px;
  height: 1006px;
  background-image: url('../../assets/images/2dMap/2d-fold.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.fold-left-arrow-right {
  width: 32.56px;
  height: 1006px;
  background-image: url('../../assets/images/2dMap/2d-fold-arrow-right.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.fold-left-arrow-right:hover {
  width: 32.56px;
  height: 1006px;
  background-image: url('../../assets/images/2dMap/2d-fold-arrow-right-hover.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.fold-left:hover {
  background: url('../../assets/images/2dMap/2d-fold-hover.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.no-fold-left {
  background-image: url('../../assets/images/2dMap/2d-no-fold.png');
}

.fold-right {
  width: 32.56px;
  height: 1006px;
  background-size: contain;
  background-repeat: no-repeat;
}

.has-fold {
  background-image: url('../../assets/images/2dMap/2d-expend.png');
}

.has-fold:hover {
  background-image: url('../../assets/images/2dMap/2d-expend-hover.png');
}

.fold-right-arrow-left {
  background-image: url('../../assets/images/2dMap/2d-fold-arrow-right.png');
  transform: rotateY(180deg);
}

.fold-right-arrow-left:hover {
  background-image: url('../../assets/images/2dMap/2d-fold-arrow-right-hover.png');
  transform: rotateY(180deg);
}

.no-fold {
  background-image: url('../../assets/images/2dMap/2d-no-expend.png');
}

.right-area-bg {
  z-index: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;

  .right-corner {
    position: absolute;
    right: 30px;
    top: 0;
    height: 1010px;
    font-size: 33px;

    .corner-3 {
      right: 0;
      position: absolute;
      top: 12px;
    }

    .corner-4 {
      right: 0;
      position: absolute;
      bottom: 36px;
    }
  }
}

.left-area {
  margin-top: 64px;
  position: fixed;
  left: 0;
  top: 0;
  height: calc(100% - 64px);
  z-index: 10;
}

.right-area {
  margin-top: 64px;
  position: fixed;
  right: 0;
  top: 0;
  height: calc(100% - 64px);
  z-index: 10;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.left-indicators {
  padding: 36px 0;
  margin-left: 48px;
  height: 100%;
  width: 368px;
}

.right-real-time-alarm {
  padding: 36px 0 56px;
  margin-right: 48px;
  height: 100%;
  width: 368px;
}

.right-indicators {
  padding: 36px 0 56px;
  margin-right: 48px;
  height: 100%;
  width: 368px;
}

.page-bgs {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;

  .bottom-line-bg-left {
    background: url('../../assets/images/2dMap/2d-bottom-bg-left.svg');
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    width: 958px;
    height: 20px;
    left: 0;
    bottom: 0;
    z-index: 2;
  }

  .bottom-line-bg-right {
    background: url('../../assets/images/2dMap/2d-bottom-bg-right.png');
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    width: 736px;
    height: 20px;
    right: 0;
    bottom: 0;
    z-index: 2;
  }

  .bottom-line-bg-center {
    background: url('../../assets/images/2dMap/2d-bottom-bg-center.svg');
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    width: 968px;
    height: 38px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    z-index: 2;
  }

  .left-page-bg {
    width: 637px;
    height: 100%;
    position: absolute;
    left: 0;
    background: linear-gradient(270deg, rgba(5, 32, 68, 0) 0%, rgba(5, 32, 68, 0.5048) 25%, #102746 53%, #052145 100%);
  }

  .right-page-bg {
    width: 637px;
    height: 100%;
    position: absolute;
    right: 0;
    transform: rotate(180deg);
    background: linear-gradient(270deg, rgba(5, 32, 68, 0) 0%, rgba(5, 32, 68, 0.5048) 25%, #102746 53%, #052145 100%);
  }

  .bottom-page-bg {
    height: 105px;
    width: 100%;
    position: absolute;
    bottom: 0;
    transform: rotate(180deg);
    background: linear-gradient(0deg, rgba(5, 32, 68, 0) 0%, #21344f 100%);
  }

  .top-page-bg {
    height: 172px;
    width: 100%;
    position: absolute;
    top: 0;
    background: linear-gradient(0deg, rgba(5, 32, 68, 0) 0%, #052145 100%);
  }
}

.fold-btn {
  position: fixed;
  left: 416px;
  z-index: 10;
  top: 200px;
}

.left-top-menu {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .page-type {
    z-index: 10;
    top: 220px;
    margin-left: 50px;
  }

  .breadcrumbs {
    margin: 30px 0 0 50px;
  }
}

.limit-bg {
  background: linear-gradient(180deg, #ffa56e 0%, #b6561b 100%);
  height: 18px;
  border-radius: 9px;
  padding: 0 8px;
}

.alarm-label {
  width: 64px;
  height: 20px;
  border-radius: 10px;
  padding: 0px 8px;
  background: #311723;
  color: #ffd4d4;
}

.map {
  z-index: 0;
}
</style>
