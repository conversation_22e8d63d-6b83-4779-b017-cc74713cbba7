<!-- 实时监测/电站概览/电站信息指标卡 -->
<template>
  <div class="flex-start flex-column">
    <div class="ps-title flex-space-between margin-t-16 width-100 linear-bg-border">
      <div class="flex-start">
        <svg-icon icon-class="ps-icon" class="font-18" />
        <p :title="psaData.stationName || '--'" class="color-text-white text-ellipsis font-16 margin-l-8 ps-name-label">{{ psaData.stationName || '--' }}</p>
      </div>
      <span class="color-text-main font-14 num-font flex-shrink-0">并网时间：{{  psaData.gridConnTime || '--' }}</span>
    </div>
    <div class="width-100 flex-space-between margin-t-16">
      <div class="scale-icon flex-start flex-column margin-l-12">
        <img class="scale-img" :src="getGif('photovoltaic-panel')"/>
        <span class="color-text-white num-font text-blod scale-num num-font-700">{{ $util.exchangeFixed(psaData.gridConnCap,0) }}</span>
        <span class="color-text-white font-14 color-text-gray">MW</span>
        <span class="color-text-white font-14 color-text-main">并网容量</span>
      </div>
      <div class="flex-start flex-column">
        <div class="flex-space-between ps-info-item linear-bg-border">
          <div class="flex-start margin-l-12">
            <svg-icon icon-class="2d-ps-info-marker" class="info-icon" />
            <span class="color-text-main margin-l-4 font-14">集电线</span>
          </div>
          <div class="flex-start">
            <span class="color-text-white font-20 num-font-500">{{ $util.exchangeFixed(psaData.windSpeed,0) }}</span>
            <span class="color-text-gray font-12 margin-l-4">条</span>
          </div>
        </div>
        <div class="flex-space-between ps-info-item linear-bg-border">
          <div class="flex-start margin-l-12">
            <svg-icon icon-class="2d-ps-info-marker" class="info-icon" />
            <span class="color-text-main margin-l-4 font-14">箱变</span>
          </div>
          <div class="flex-start">
            <span class="color-text-white font-20 num-font-500">{{ $util.exchangeFixed(psaData.windSpeed,0) }}</span>
            <span class="color-text-gray font-12 margin-l-4">台</span>
          </div>
        </div>
        <div class="flex-space-between ps-info-item linear-bg-border">
          <div class="flex-start margin-l-12">
            <svg-icon icon-class="2d-ps-info-marker" class="info-icon" />
            <span class="color-text-main margin-l-4 font-14">风机</span>
          </div>
          <div class="flex-start">
            <span class="color-text-white font-20 num-font-500">{{ $util.exchangeFixed(psaData.windSpeed,0) }}</span>
            <span class="color-text-gray font-12 margin-l-4">台</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {getModelData} from "../../../api/2dMap/psOverview";
import moment from "moment";
import { textColor } from "../../../utils/color";

export default {
  name: "PsInfos",
  props: {},
  data() {
    return {
      psaData:{
        "planeRadiation": null,//倾斜面辐射
        "weather": null,
        "tempNight": null,
        "tempDay": null,
        "waterLevel": null,
        "recordDate": null,
        "conditionIdDay": null,
        "slantRadiation": null,//瞬时辐照度
        "stationName": null,
        "gridConnCap": null, //并网容量
        "windSpeed":null, // 风速
      },
      psaInfo:null,
      textColor:textColor
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.refreshData()
  },
  methods: {
    async refreshData(){
      this.$emit('setLoading',true)
      let res = await getModelData({
        psId:this.psaInfo.psId,
        modelName:'psInfo',
        recordDate:moment().format('YYYY-MM-DD')
      }).catch(e=>{
        this.$emit('setLoading',false)
        return
      })
      this.psaData = res.result_data.dataResult

      this.$emit('callBack',{
        type:'setPsLimit',
        // params:true
        params:res.result_data.dataResult.elecLimitStatus == 1
      })

      this.$emit('setLoading',false)
    },
    getSrc(type){
      return require(`@/assets/images/weather/W${type}.png`)
    },
    getPng (name) {
      return require(`@/assets/images/2dMap/${name}.png`)
    },
    getGif (name){
      return require(`@/assets/images/2dMap/${name}.gif`)
    }

  }
}
</script>

<style lang="less" scoped>
.ps-title{
  height: 33px;
  box-sizing: border-box;
  padding: 0 4px 0 14px;
  .ps-name-label{
    width: 150px;
    font-weight: 400;
    margin-bottom:0 ;
  }
}
.weather-img{
  width: 20px;
  height: 20px;
}
.scale-icon{
  .scale-img{
    width: 110px;
    height: 74px;
  }
  .scale-num{
    font-size: 24px;
    line-height: 22px;
  }
}
.ps-info-item{
  width: 223px;
  height: 33px;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 0 4px 0 0;
  .info-icon{
    font-size: 20px;
  }
}
.ps-info-item:last-of-type{
  margin-bottom: 0;
}

</style>