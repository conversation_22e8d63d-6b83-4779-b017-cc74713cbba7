<!-- 实时监测/电站概览 实时消息告警 -->
<template>
  <div class="height-100 list-area padding-t-16">
    <a-radio-group
      v-model="alarmType"
      class="flex-start width-100 margin-b-8"
      @change="changeAlarmTypeHandler"
      :disabled="alarmRadioLoading"
    >
      <a-radio-button
        v-for="item in alarmTypeList"
        class="alarm-type-item"
        :value="item.value"
        :key="item.value"
      >
        <span>{{ item.label }}</span>
        <span style="margin-left: 1px">{{ item.num }}</span>
      </a-radio-button>
    </a-radio-group>
    <div
      v-for="(item, index) in alarmList"
      :key="index"
      @click="alarmCardClick(item)"
      class="flex-start flex-column margin-b-10 alarm-list-item"
    >
      <div class="width-100 alarm-img">
        <img :src="item.fileUrlThumbnail" class="width-height-100" alt="" />
        <div class="img-label flex-start">
          <span class="font-14 color-text-main margin-l-12">
            {{ item.alarmReason }}
          </span>
        </div>
      </div>

      <div class="flex-space-between width-100 margin-t-8">
        <div class="flex-start">
          <span class="color-text-white font-16 margin-r-4">
            {{
              getLabel(item.type === "event" ? item.eventDesc : item.deviceName)
            }}
          </span>
          <div
            v-if="item.type !== 'event'"
            class="alarm-marker flex-center"
            :class="'alarm-marker-bg-' + Number(item.alarmGrade)"
          >
            <span class="color-text-white font-14">{{
              grades[Number(item.alarmGrade) - 1]
            }}</span>
          </div>
        </div>
        <span class="font-14 color-text-white">
          {{ item.type === "event" ? item.remarkName : item.alarmName }}
        </span>
      </div>

      <div class="flex-space-between width-100 margin-t-4">
        <span class="color-text-main font-14">{{ item.alarmTime }}</span>
        <div class="flex-start">
          <di-throttle-button
            class="di-cancel-btn export-btn"
            size="small"
            label="复检"
            @click="recheck(item)"
            v-if="canFlyRemarks.includes(item.remark)"
          />
          <di-throttle-button
            class="di-cancel-btn export-btn"
            size="small"
            @click="recheckResult(item)"
            v-if="
              canFlyRemarks.includes(item.remark) &&
              ['progress', 'finish', 'error'].includes(item.taskStatus)
            "
            label="复检结果"
          />
          <div
            @click="confirmAlarm(item, index)"
            v-if="item.alarmStatus == '01'"
            class="alarm-btn flex-center cursor-pointer"
            v-has="'overview:alarmInfo_confirm'"
          >
            <span>
              {{ item.deviceType == "54" ? "确认" : "待处理" }}
            </span>
          </div>
          <span
            class="color-text-gray margin-l-8"
            v-if="item.alarmStatus == '03'"
            >已派发</span
          >
          <div
            v-if="item.type === 'event'"
            @click.stop="opticalPowerConfirm(item, index)"
            class="alarm-btn flex-center cursor-pointer"
            v-has="'overview:alarmInfo_confirm'"
          >
            确认
          </div>
        </div>
      </div>

      <img class="alarm-list-item-bg pointer-events-none" :src="itemBgURL" />
    </div>

    <infinite-loading :identifier="infiniteId" v-if="alarmList.length !== 1" @infinite="refreshData" ref="infiniteLoading">
      <div slot="spinner">
        <a-spin size="large" class="spin margin-t-16" style="height: 100px"></a-spin>
      </div>
      <div slot="no-more">
        <p class="no-more-data color-text-white">无更多数据</p>
      </div>
      <div slot="no-results" v-if="alarmList.length == 0" class="flex-center margin-t-70">
        <!--        <img class="no-data-img" :src="noDataImg" />-->
<!--        <p class="no-data-text color-text-white">暂无数据</p>-->
        <div class="no-data"/>
      </div>
    </infinite-loading>
  </div>
</template>

<script>
import InfiniteLoading from "vue-infinite-loading";
import {
  getModelData,
  affirmForSensor,
  opticalPowerConfirmApi,
} from "../../../api/2dMap/psOverview";
import { lastTask } from "@/api/2dMap/map";
import { getDockList } from "@/api/2dMap/map";
import { alarmTypeList } from "../constants";

export default {
  name: "RealTimeAlarm",
  components: {
    InfiniteLoading,
  },
  props: {
    dataParams: {
      type: Object,
      default: () => {
        return {
          alarmId: undefined,
        };
      },
    },
  },
  data() {
    return {
      alarmList: [],
      itemBgURL: require("@/assets/images/2dMap/2d-alarm-list-item-bg.png"),
      infiniteId: new Date(),
      // currentPage:1,
      grades: ["I类", "II类", "III类", "IV类"],
      arr: [],
      deviceAlarmNames: ["故障停机", "通讯中断", "低效缺陷", "隐患运行"],
      environmentalAlarmNames: [
        "烟火识别",
        "水情识别",
        "周界入侵",
        "天气预警",
        "站容站貌",
      ],
      behavioralAlarmNames: ["日常行为", "作业规范", "人员安全"],
      alarmTypeList,
      alarmType: "all",
      canFlyRemarks:["3", "4"],
      alarmRadioLoading: false
    };
  },
  created() {
    this.psaInfo = this.$ls.get("PSA_INFO");
    this.$emit("setLoading", false);
    this.getRealtimeAlertCount();
  },
  methods: {
    async refreshData(loadMore) {
      this.alarmRadioLoading = true;
      const { alarmTypes, alarmRemarks } =
        this.alarmTypeList.find((o) => o.value === this.alarmType) ?? {};
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: "realtimeAlert",
        operateType: 2,
        currentPage: this.alarmList.length / 20 + 1,
        pageSize: 20,
        alarmId: this.dataParams.alarmId,
        alarmTypes,
        alarmRemarks,
      }).catch(()=>{
        this.alarmRadioLoading = false;
      });
      this.alarmRadioLoading = false;
      this.alarmList = this.alarmList.concat(res.result_data);
      this.getListTaskStatus();
      // this.currentPage +=1
      if (loadMore) {
        const length = res.result_data.length;
        if (length < 20) {
          loadMore.complete();
          if (length != 0) {
            loadMore.loaded();
          }
        } else {
          loadMore.loaded();
        }
      }
    },
    recheck(item) {
      this.stopEvent();
      if (this.dataParams.isFlying) {
        this.$message.warning("无人机正在执行其他巡检任务，请稍后再试");
        return;
      }
      let params = { page: 1, pageSize: 10, psId: this.psaInfo.psId };
      getDockList(params).then((res) => {
        if (res.payload && res.payload.list && res.payload.list.length > 0) {
          let arr = res.payload.list.filter((item) => {
            return !!item.status;
          });
          if (arr.length > 0) {
            if (!arr[0].powerEnable) {
              this.$message.warning("无人机电量不足，请稍后再试");
            } else if (arr[0].modeCode == "4") {
              this.$message.warning("无人机正在执行其他巡检任务，请稍后再试");
            } else {
              this.$emit("callBack", {
                type: "recheck",
                params: {
                  psKey: item.psKey,
                  dockId: arr[0].deviceSn,
                  droneId: arr[0].childDeviceSn,
                },
              });
            }
          } else {
            this.$message.warning("机库离线，请稍后再试");
          }
        } else {
          this.$message.warning("机库离线，请稍后再试");
        }
      });
    },
    recheckResult(item) {
      this.stopEvent();
      lastTask({ deviceId: item.psKey }).then((res) => {
        let data = res.payload;
        if (res.payload.errMsg) {
          item.taskStatus = "error";
        } else if (res.payload.flowSts == "4") {
          item.taskStatus = "finish";
        } else {
          item.taskStatus = "progress";
        }
        item.errMsg = data.errMsg;
        item.flowSts = data.flowSts;
        item.taskId = data.taskId;
        item.executeTime = data.executeTime;
        this.$forceUpdate();
        this.$emit("callBack", {
          type: "setRecheckShow",
          params: {
            data: item,
          },
        });
      });
    },
    alarmCardClick(item) {
      if (item.type === "event") return;
      // 跳转到设备诊断
      if (
        ["01", "03"].includes(item.alarmStatus) &&
        this.deviceAlarmNames.includes(item.alarmName)
      ) {
        this.gotoHealthPage(item, "/health/safe");
        // 跳转到环境诊断
      } else if (
        item.alarmStatus == "01" &&
        this.environmentalAlarmNames.includes(item.alarmName)
      ) {
        this.gotoHealthPage(item, "/health/environment");
      } else if (
        item.alarmStatus == "01" &&
        this.behavioralAlarmNames.includes(item.alarmName)
      ) {
        this.gotoHealthPage(item, "/health/behavioral");
      }
    },
    // 确认告警
    confirmAlarm(item, index) {
      if (item.deviceType != "54") {
        return;
      }
      this.stopEvent();
      this.$confirm({
        title: "是否确认",
        content: "确认后该告警信息表示已完成",
        centered: true,
        onOk: async () => {
          await affirmForSensor({
            psId: this.psaInfo.psId,
            id: item.id,
            handleStatus: "03",
          });
          this.alarmList.splice(index, 1);
          this.$emit("callBack", {
            type: "refreshMapAlarm",
            params: {},
          });
          this.getRealtimeAlertCount();
          this.$store.commit("app/DECREASE_GLOBAL_ALARM_COUNT");
          this.$message.success("操作成功");
        },
      });
    },
    // 跳转至智能诊断页面
    gotoHealthPage(data, routeName) {
      window.comeFromAlarm = true;
      this.$router.push({
        name: routeName,
        params: {
          tabName: data.alarmName,
          handleStatus: data.alarmStatus,
          id: data.id,
          deviceType: data.deviceTypeShow,
        },
      });
    },
    // 无人机故障告警获取任务状态
    getListTaskStatus() {
      this.alarmList.forEach((item) => {
        item.loading = false;
        if (this.canFlyRemarks.some( o => o==item.remark )) {
          lastTask({ deviceId: item.psKey }).then((res) => {
            if (res.payload && res.payload.taskId) {
              let data = res.payload;
              if (res.payload.errMsg) {
                item.taskStatus = "error";
              } else if (res.payload.flowSts == "4") {
                item.taskStatus = "finish";
              } else {
                item.taskStatus = "progress";
              }
              item.errMsg = data.errMsg;
              item.flowSts = data.flowSts;
              item.taskId = data.taskId;
              item.executeTime = data.executeTime;
            } else {
              item.taskStatus = "none";
            }
            this.$forceUpdate();
          });
        }
      });
    },
    // 阻止冒泡方法
    stopEvent(e) {
      e = e || window.event;
      if (e.stopPropagation) {
        // W3C阻止冒泡方法
        e.stopPropagation();
      } else {
        e.cancelBubble = true; // IE阻止冒泡方法
      }
    },
    async opticalPowerConfirm(item, index) {
      this.$confirm({
        title: "是否确认",
        content: "确认后该告警信息表示已完成",
        centered: true,
        onOk: async () => {
          this.alarmList.splice(index, 1);
          await opticalPowerConfirmApi({ id: item.id, affirmStatus: 1 });
          this.$message.success("操作成功");
          this.getRealtimeAlertCount();
          this.$store.commit("app/DECREASE_GLOBAL_ALARM_COUNT");
        },
      });
    },
    // 告警类型切换时重置查询
    changeAlarmTypeHandler() {
      this.alarmList = [];
      this.$nextTick(() => {
        this.$refs.infiniteLoading.$emit("$InfiniteLoading:reset");
      });
    },
    getRealtimeAlertCount() {
      getModelData({
        psId: this.psaInfo.psId,
        modelName: "realtimeAlertCount",
        alarmId: this.dataParams.alarmId,
        operateType: 2,
      }).then((res) => {
        this.alarmTypeList.forEach((item) => {
          item.num = res.result_data[item.value];
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  //display: none;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: #1a426f;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: transparent;
}

.alarm-type-item {
  width: 25%;
  text-align: center;
  padding: 0;
}
.alarm-btn {
  background: linear-gradient(
    180deg,
    rgba(235, 128, 62, 0.15) 0%,
    rgba(235, 128, 62, 0.67) 100%
  );
  box-sizing: border-box;
  border: 1px solid #ffc7a5;
  height: 25px;
  border-radius: 4px;
  padding: 0 10px;
  color: #ffc7a5;
  margin-left: 8px;
}
.alarm-btn:hover {
  background: linear-gradient(
    180deg,
    rgba(235, 128, 62, 0.35) 0%,
    rgba(235, 128, 62, 0.74) 100%
  );
  box-sizing: border-box;
  border: 1px solid #FFC7A5;
  color: #FFE7D9;
}

.alarm-btn-gray {
  background: linear-gradient(
    180deg,
    rgba(235, 128, 62, 0.15) 0%,
    rgba(235, 128, 62, 0.67) 100%
  );
  border: 1px solid rgba(255, 199, 165, 0.4);
  height: 25px;
  border-radius: 4px;
  padding: 0 10px;
  color: rgba(255, 199, 165, 0.4);
}

.list-area {
  overflow-y: scroll;
}

.alarm-list-item {
  width: 100%;
  height: 286px;
  background-image: url("../../../assets/images/2dMap/2d-alarm-list-item-bg.png");
  background-size: cover;
  box-sizing: border-box;
  position: relative;
  padding: 12px 12px 16px;
  cursor: pointer;
  .alarm-img {
    z-index: 0;
    width: 338px;
    height: 198px;
    position: relative;
    .img-label {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 44px;
      background: linear-gradient(
        180deg,
        rgba(0, 17, 35, 0) 0%,
        rgba(0, 17, 35, 0.7) 100%
      );
    }
  }
  .alarm-list-item-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }
}
.alarm-list-item:hover {
  background-image: url("../../../assets/images/2dMap/2d-alarm-list-item-bg-hover.png");
}
.spin {
  width: 100%;
  height: 60px;
}
button + button {
  margin-left: 8px;
}
</style>
