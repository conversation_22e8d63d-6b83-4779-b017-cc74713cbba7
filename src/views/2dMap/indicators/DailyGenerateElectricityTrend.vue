<!-- 实时监测/电站概览/日发电趋势 指标卡 -->
<template>
  <div class="relative">
    <div id="chartDailyGenerateElectricityTrend" class="width-100 height-100">
    </div>
    <div class="fake-legend flex-start">

    </div>
  </div>

</template>

<script>

import echarts from '@/utils/enquireEchart'
import {getModelData} from "../../../api/2dMap/psOverview";
import moment from "moment";
import {chartColor, textColor} from "../../../utils/color";

export default {
  name: "DailyGenerateElectricityTrend",
  props: {},
  data() {
    return {
      option:{
        //你的代码
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops:[{
                  offset: 0, color: 'rgba(64, 170, 255, 0)' // 0% 处的颜色
                },{
                  offset: 1, color: 'rgba(64, 170, 255, 0.4)' // 100% 处的颜色
                }],
                global: false, // 缺省为 false
              },
            }
          },
          className:'di-chart-tooltip',
          borderColor: 'rgba(189, 226, 255, 0.4)',
          borderWidth:1,
          formatter: params => {
            return `<div class="title font-14">${params[0].axisValue}</div>
                    <div class="content">
                      <div  class="content-item">
                        <div class="flex-start">
                        ${params[0].marker}
                        <span class="tooltip-item-label">${params[0].seriesName}：</span>
                        </div>
                        <span>
                        <span class="color-text-white">${(params[0].value ==0 ? 0 :(params[0].value || '--'))}</span>
                        <span class="content-unit">万kWh</span>
                        </span>
                      </div>
                      <div  class="content-item">
                        <div class="flex-start">
                        ${params[1].marker}
                        <span class="tooltip-item-label">${params[1].seriesName}：</span>
                        </div>
                        <span>
                          <span class="color-text-white">${(params[1].value ==0 ? 0 :(params[1].value || '--'))}</span>
                          <span class="content-unit">MW</span>
                        </span>
                      </div>
                    </div>`
          }
        },
        legend: [
          {
            itemHeight: 13,
            itemWidth: 13,
            borderRadius:0,
            icon:'rect',
            itemStyle:{
              borderRadius:0,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops:[{
                  offset: 0, color: 'rgba(64, 207, 255, 1)' // 0% 处的颜色
                },{
                  offset: 1, color: 'rgba(64, 170, 255, 0.3)' // 100% 处的颜色
                }],
                global: false, // 缺省为 false
              },
            },
            right:'50%',
            align: 'left',
            textStyle: {
              color: textColor.main,
              lineHeight:14,
              number: 12
            },
            selectedMode: false,
            data: [{ name: '日发电量' }] ,
            top: '14'
          },{
            itemHeight: 13,
            itemWidth: 13,
            align: 'left',
            icon: 'image://' + require('@/assets/images/2dMap/2d-chart-daily-generate-legend.png'),
            left:'50%',
            textStyle: {
              color: textColor.main,
              number: 12,
            },
            itemStyle:{
            },
            selectedMode: false,
            data: [{ name: '功率' }] ,
            top: '14'
          },
        ],
        grid: {
          bottom: 10,
          top: "50",
          left: "20",
          right: "10",
          containLabel: true,
        },
        xAxis: {
          data: [],
          axisTick: { show: false },
          axisLine: {
            show: true,
            lineStyle:{
              color:chartColor.splitLine
            }
          },
          axisLabel: {
            color: textColor.main
          },

        },
        yAxis: [
          {
            name: '万kWh',
            nameTextStyle: {
              align: 'right',
              color: textColor.main
            },
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color:chartColor.splitLine
              }
            },
            min: 0,
            max: 100, // 左侧y轴最大值
            // 两个y轴的刻度必须整除一个相同的数才能重合
            interval: 100, // 间距等分为5等分
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              color: textColor.main
            }
          },
          {
            name: 'MW',
            nameTextStyle: {
              align: 'left',
              color: textColor.main
            },
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color:chartColor.splitLine
              }
            },
            min: 0,
            max: 100, // 左侧y轴最大值
            // 两个y轴的刻度必须整除一个相同的数才能重合
            interval: 100, // 间距等分为5等分
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              color: textColor.main
            }
          }
        ],
        series: [
          {
            type: 'bar',
            name:'日发电量',
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops:[{
                    offset: 0, color: '#40CFFF' // 0% 处的颜色
                  },{
                    offset: 1, color: '#40AAFF' // 100% 处的颜色
                  }],
                  global: false, // 缺省为 false
                },
              }
            },
            data: []
          },
          {
            type: 'line',
            name:'功率',
            yAxisIndex: 1,
            symbol:'image://' + require('@/assets/icons/2dMap/2d-chart-daily-generate-marker.png'),
            symbolSize:4,
            lineStyle:{
              width:.7,
            },
            itemStyle: {
              normal: {
                color: '#FF9F63' // 折线的颜色
              }
            },
            data: []
          }
        ]
      },
      psaInfo:null,

    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  mounted() {
    this.refreshData()
  },
  methods: {
    changeFixed(arr,type) {
      return arr.map(item =>item[type]).map(item => {
          if(item == null) {
            return null
          } else {
            return  this.$util.exchangeFixed(item/(type == 'realTimeGen' ? 10000 : 1000),4)
          }
        });
    },
    async refreshData(){
      this.$emit('setLoading',true)
      let res = await getModelData({
        psId:this.psaInfo.psId,
        modelName:'dailyPowerTrend',
        recordDate:moment().format('YYYY-MM-DD')
        // recordDate:'2024-06-21'
      }).catch(e=>{
        this.$emit('setLoading',false)
        return
      })
      let data = res.result_data.dataResult.realTimePowerGen
      let arrGen = this.changeFixed(data,'realTimeGen');
      let arrPower = this.changeFixed(data,'realTimePwr') 

      // 等待后端数据接通 替换
      // let arrGen = VALUE;
      // let arrPower = VALUE.map(item => item * Math.random() * 2);

      let maxY1 = 1;
      let maxY2 = 1;
      // y轴最大值转换为数组最大值同数量级并且首位＋1 的 后续全为0的格式 例如 58=>60   2839=>3000
      const arr = [1e1, 1e2, 1e3, 1e4, 1e5, 1e6,1e7,1e8];
      if(arrGen.length){
        for (let item of arr) {
          if (Math.max(...arrGen) <= item) {
            maxY1 = (Math.floor((Math.max(...arrGen) + (item / 10)) / (item / 10))) * (item / 10);
            break;
          }
        }
      }
      if(arrPower.length){
        for (let item of arr) {
          if (Math.max(...arrPower) <= item) {
            maxY2 = (Math.floor((Math.max(...arrPower) + (item / 10)) / (item / 10))) * (item / 10);
            break;
          }
        }
      }

      this.option.yAxis[0].max = maxY1
      this.option.yAxis[0].interval = maxY1 / 5
      this.option.yAxis[1].max = maxY2
      this.option.yAxis[1].interval = maxY2 / 5

      this.option.series[0].data = arrGen
      this.option.series[1].data = arrPower
      this.option.xAxis.data = data.map(item=>item.time)

      if(this.myChart == null){
        let chartDom = document.getElementById('chartDailyGenerateElectricityTrend');
        this.myChart = echarts.init(chartDom);
      }
      this.myChart.setOption(this.option,true);
      this.$emit('setLoading',false)
    }
  }
}
</script>

<style lang="less" scoped>
.fake-legend{
  position: absolute;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
}
</style>