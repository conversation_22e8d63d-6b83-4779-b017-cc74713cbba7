<!-- 实时监测 监控设备情况 指标卡 -->
<template>
  <div class="padding-t-16">
    <div class="monitor-condition-content">
      <div class="flex-start flex-column content-left">
        <span class="num-font-700 font-20 color-text-white">{{ dataSource.onlineNum }}</span>
        <span class="color-text-main font-14">在线数量</span>
      </div>

      <div class="flex-start flex-column content-center">
        <span class="num-font-700 font-20 color-text-white">{{ dataSource.offlineNum + dataSource.onlineNum }}</span>
        <span class="color-text-main font-14">监控设备</span>
      </div>

      <div class="flex-start flex-column content-right">
        <span class="num-font-700 font-20 color-text-white">{{ dataSource.offlineNum }}</span>
        <span class="color-text-main font-14">离线数量</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getModelData } from '../../../api/2dMap/psOverview';
import { DEVICE_TYPE_ENUM } from '@/utils/util';

export default {
  name: 'MonitorDeviceCondition',
  props: {
    dataParams: {
      type: Object,
      default: () => {
        return {
          areaType: 1
        };
      }
    }
  },
  data() {
    return {
      dataSource: {
        offlineNum: 0,
        onlineNum: 0,
        psaInfo: null
      }
    };
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.refreshData();
  },
  methods: {
    async refreshData() {
      this.$emit('setLoading', true);
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: 'sensorScale',
        deviceType: DEVICE_TYPE_ENUM.CAMERA,
        areaType: this.dataParams.areaType
      }).catch((e) => {
        this.$emit('setLoading', false);
        return;
      });
      let data = res.result_data.dataResult?.sensorStatus?.[0] || {};
      this.dataSource.onlineNum = data.onlineNum || 0;
      this.dataSource.offlineNum = data.offlineNum || 0;
      this.$emit('setLoading', false);
    }
  }
};
</script>

<style lang="less" scoped>
.monitor-condition-content {
  width: 100%;
  height: 74px;
  background: url('../../../assets/images/2dMap/2d-monitor-condition-bg.png');
  background-size: cover;
  position: relative;

  .content-left {
    position: absolute;
    left: 28px;
    top: 8px;
  }

  .content-center {
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 8px;
  }

  .content-right {
    position: absolute;
    right: 28px;
    top: 8px;
  }
}
</style>