<!-- 实时监测/电站概览/发电情况 指标卡 -->
<template>
  <div id="chartGenerateElectricityCondition" class="width-100 height-100">
  </div>
</template>

<script>
import echarts from '@/utils/enquireEchart'
import {textColor,chartColor} from "../../../utils/color";
import { getModelData } from "../../../api/2dMap/psOverview";
import moment from "moment";
export default {
  name: "GenerateElectricityCondition",
  props: {},
  data() {
    return {
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops:[{
                  offset: 0, color: 'rgba(64, 170, 255, 0)' // 0% 处的颜色
                },{
                  offset: 1, color: 'rgba(64, 170, 255, 0.4)' // 100% 处的颜色
                }],
                global: false, // 缺省为 false
              },
            }
          },
          className:'di-chart-tooltip',
          borderColor: 'rgba(189, 226, 255, 0.4)',
          borderWidth:1,
          formatter: params => {
            return `<div class="title font-14">${params[0].axisValue}</div>
                    <div class="content">
                      <div  class="content-item">
                        <div class="flex-start">
                        ${params[0].marker}
                        <span class="tooltip-item-label">${params[0].seriesName}：</span>
                        </div>
                        <span>
                        <span class="color-text-white">${(params[0].value ==0 ? 0 :(params[0].value || '--'))}</span>
                        <span class="color-text-gray content-unit">万kWh</span>
                        </span>
                      </div>

                    </div>`
          }
        },
        grid:{
          bottom: 10,
          top: "50",
          left: "20",
          right: "10",
          containLabel: true,
        },
        xAxis: {
          data: [],
          axisTick: { show: false },
          axisLine: {
            show: true,
            lineStyle:{
              color:chartColor.splitLine
            }
          },
          axisLabel: {
            color: textColor.main
          },

        },
        yAxis: {
          name: '万kWh',
          nameTextStyle: {
            align: 'right',
            color: textColor.main
          },
          splitLine: {
            show: true,
            lineStyle: {
              color:chartColor.splitLine
            }
          },
          axisTick: { show: false },
          axisLine: { show: false },
          axisLabel: {
            show: true,
            color: textColor.main
          }
        },
        color: [textColor.main],
        series: [
          {
            name: '发电量',
            type: 'pictorialBar',
            barCategoryGap: '50%',
            symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
            itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 1, color: '#0C5696' // 100% 处的颜色
                  }, {
                    offset: 0, color: '#7ACEF9' // 0% 处的颜色
                  }]
                }
            },
            emphasis: {
              itemStyle: {
                opacity: 1
              }
            },
            data: [],
            z: 10,
          },
          {
            name: 'glyph',
            type: 'pictorialBar',
            barGap: '-100%',
            symbol:'image://' + require('@/assets/icons/2dMap/2d-chart-generate-marker.png') ,
            symbolPosition: 'end',
            symbolSize: 6,
            symbolOffset: [0, '-120%'],
            label: {
              show: false,
              color: textColor.white,
              position: 'top'
            },
            data: []
          }
        ]
      },
      myChart:null,
      psaInfo:null
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  mounted() {
    this.refreshData(1)
  },
  beforeDestroy() {
    // 重置发电情况的日期类型
    this.$emit("callBack", 1);
  },
  methods: {
    async refreshData(value = 1){
      this.$emit('setLoading',true)
      let res = await getModelData({
        psId:this.psaInfo.psId,
        psaId:this.psaInfo.psaList[0],
        modelName:'psGeneration',
        recordDate:moment().format('YYYY-MM-DD')
      }).catch(e=>{
        this.$emit('setLoading',false)
        return
      })
      let data = res.result_data.dataResult
      let key = value == 1 ? 'dailyGen' : (value == 2 ? 'monthlyGen' : 'yearlyGen')
      let dataSource = value == 1 ? data[key] : (value == 2 ? data[key] : data[key])
      let seriesData = dataSource.map(item => (item[key] || item[key] == 0) ? (Number(item[key]/10000)).toFixed(4) : '--')
      this.option.series[0].data = seriesData
      this.option.series[1].data = seriesData
      this.option.xAxis.data = dataSource.map(item => {
        if(value == 1){
          return moment(item.time).format('MM-DD')
        }
        if(value == 2){
          return moment(item.time).format('YYYY-MM')
        }
        if(value == 3){
          return moment(item.time).format('YYYY')
        }
      })
      if(seriesData.every(item => item == '--')){
        this.option.grid.left = '50'
      } else {
        this.option.grid.left = '20'
      }

      if(this.myChart == null){
        let chartDom = document.getElementById('chartGenerateElectricityCondition');
        this.myChart = echarts.init(chartDom);
      }
      this.myChart.setOption(this.option,true);
      this.$emit('setLoading',false)
    }
  }
}
</script>
