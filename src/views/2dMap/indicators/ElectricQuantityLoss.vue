<!-- 实时监测/电站概览/电量损失 指标卡 -->

<template>
  <div class="width-100 height-100" id="lossPower">
    <div class="loss-type flex-center">

      <div class="loss-type-legend-left-area">
        <div v-for="(item,index) in lossType"
             @click="changeLossType(index)"
             :class="{'active-type':index == activeIndex}"
             class="loss-type-item flex-center cursor-pointer" :key="index">
          <div
            :style="{background:index == activeIndex ? item.marker :'#D8D8D8'}"
            class="loss-marker margin-r-4">
          </div>
          <div
            class="font-12 margin-r-8"
            :style="{'color':index == activeIndex ? colorMain : '#A1AAB6'}">
          {{ item.label }}</div>
        </div>
      </div>

<!--      <div v-for="(item,index) in lossType"-->
<!--           :class="{'active-type':index == activeIndex}"-->
<!--           class="loss-type-item flex-start" :key="index">-->
<!--        <div-->
<!--          :style="{background:index == activeIndex ? item.marker :'#D8D8D8'}"-->
<!--          class="loss-marker margin-r-4">-->
<!--        </div>-->
<!--        <span-->
<!--          class="font-12 margin-r-8"-->
<!--          :style="{'color':index == activeIndex ? item.marker : '#A1AAB6'}">-->
<!--          {{ item.label }} {{ item.data[item.data.length - 1] }} kWh</span>-->
<!--      </div>-->

    </div>
    <div id="chartElectricQuantityLoss" class="width-100 loss-chart">
    </div>
  </div>
</template>

<script>
import echarts from '@/utils/enquireEchart'
import {textColor,chartColor} from "../../../utils/color";
import { getModelData } from "../../../api/2dMap/psOverview";
import moment from "moment";
export default {
  name: "ElectricQuantityLoss",
  props: {},
  data() {
    return {
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops:[{
                  offset: 0, color: 'rgba(64, 170, 255, 0)' // 0% 处的颜色
                },{
                  offset: 1, color: 'rgba(64, 170, 255, 0.4)' // 100% 处的颜色
                }],
                global: false, // 缺省为 false
              },
            }
          },
          className:'di-chart-tooltip',
          borderColor: 'rgba(189, 226, 255, 0.4)',
          borderWidth:1,
          formatter: params => {
            return `<div class="title font-14">${params[0].axisValue}</div>
                    <div class="content">
                      <div  class="content-item">
                        <div class="flex-start">
                        ${params[0].marker}
                        <span class="tooltip-item-label">${params[0].seriesName}：</span>
                        </div>
                        <span>
                        <span class="color-text-white">${(params[0].value ==0 ? 0 :(params[0].value || '--'))}</span>
                        <span class="color-text-gray content-unit">kWh</span>
                        </span>
                      </div>

                    </div>`
          }
        },
        grid:{
          bottom: 10,
          top: "30",
          left: "20",
          right: "10",
          containLabel: true,
        },
        xAxis: {
          data: [],
          axisTick: { show: false },
          axisLine: {
            show: true,
            lineStyle:{
              color:chartColor.splitLine
            }
          },
          axisLabel: {
            color: textColor.main,
            rotate:40
          },

        },
        yAxis: {
          name: 'kWh',
          nameTextStyle: {
            align: 'right',
            color: textColor.main
          },
          splitLine: {
            show: true,
            lineStyle: {
              color:chartColor.splitLine
            }
          },
          axisTick: { show: false },
          axisLine: { show: false },
          axisLabel: {
            show: true,
            color: textColor.main
          }
        },
        color: [textColor.main],
        series: [
          {
            type:'line',
            name:'故障',
            symbol:'rect' ,
            symbolPosition: 'end',
            symbolSize: 3.5,
            showSymbol:true,
            showAllSymbol:true,
            symbolRotate:45,
            label:'show',
            lineStyle:{
              color: '',
              width:.5,
            },
            itemStyle:{
              color: ''
            },
            areaStyle: {   //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops:[{
                  offset: 0, color: 'rgba(38, 142, 235, 0.55)' // 0% 处的颜色
                },{
                  offset: 1, color: 'rgba(38, 142, 235, 0)' // 100% 处的颜色
                }],
                global: false, // 缺省为 false
              }
            },
            data: []
          }
        ]
      },
      myChart:null,
      psaId:null,
      colorMain:textColor.main,
      lossType:[
        {
          label: '故障损失',
          key: 'faultLoss',
          marker: 'rgba(240, 112, 114, 1)',
          markerLinear: 'rgba(240, 112, 114, 0.55)',
          markerLinearEnd: 'rgba(240, 112, 114, 0)',
          data: []
        },
        {
          label: '低效损失',
          key: 'InefficientLoss',
          marker: 'rgba(64, 170, 255, 1)',
          markerLinear: 'rgba(64, 170, 255, 0.55)',
          markerLinearEnd: 'rgba(64, 170, 255, 0)',
          data: []
        },
        {
          label: '灰尘损失',
          key: 'dustLoss',
          marker: 'rgba(192, 203, 207, 1)',
          markerLinear: 'rgba(192, 203, 207, 0.55)',
          markerLinearEnd: 'rgba(192, 203, 207, 0)',
          data: []
        },
        {
          label: '限电损失',
          key: 'powerRatLoss',
          marker: 'rgba(255, 165, 110, 1)',
          markerLinear: 'rgba(255, 165, 110, 0.55)',
          markerLinearEnd: 'rgba(255, 165, 110, 0)',
          data: []
        },
        {
          label: '设备损耗',
          key: 'deviceLoss',
          marker: 'rgba(243, 214, 102, 1)',
          markerLinear: 'rgba(243, 214, 102, 0.55)',
          markerLinearEnd: 'rgba(243, 214, 102, 0)',
          data: []
        },
        {
          label: '夜间损耗',
          key: 'nightLoss',
          marker: 'rgba(200, 193, 255, 1)',
          markerLinear: 'rgba(200, 193, 255, 0.55)',
          markerLinearEnd: 'rgba(200, 193, 255, 0)',
          data: []
        }
      ],
      activeIndex:0,
      timer:null

    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.refreshData()
  },
  mounted() {
    this.initMouseListen()
  },
  destroyed() {
    this.pauseRecyle()
  },
  computed:{
    lossTypeLeft(){
      return this.lossType.filter((item,index) => index <=2)
    },
    lossTypeRight(){
      return this.lossType.filter((item,index) => index >=3)
    },
  },
  methods: {
    async refreshData(){
      this.pauseRecyle()
      this.activeIndex =0
      this.$emit('setLoading',true)
      let res = await getModelData({
        psId:this.psaInfo.psId,
        psaId:this.psaInfo.psaList[0],
        modelName:'powerLoss',
        recordDate:moment().format('YYYY-MM-DD')
      }).catch(e=>{
        this.$emit('setLoading',false)
        return
      })
      let data = res.result_data.dataResult.powerLoss

      this.lossType = this.lossType.map(item => {
        item.data = data.map(el => this.$util.exchangeFixed(el[item.key],2))
        return item
      })
      this.option.xAxis.data = data.map(item => item.date == null ? null : moment(item.date).format('MM-DD'))

      this.setOptionDataByActive()

      if(this.myChart == null){
        let chartDom = document.getElementById('chartElectricQuantityLoss');
        this.myChart = echarts.init(chartDom);
      }
      this.activeIndex = 0

      this.myChart.setOption(this.option,true);
      this.recyleData()
      this.$emit('setLoading',false)
    },
    changeLossType(index){
      this.activeIndex = index
      this.setOptionDataByActive()
      this.myChart.setOption(this.option,true)
    },
    initMouseListen(){
      let dom = document.getElementById('lossPower')
      dom.addEventListener('mouseenter',this.pauseRecyle)
      dom.addEventListener('mouseleave',this.recyleData)
    },
    pauseRecyle(){
      clearInterval(this.timer)
      this.timer = null
    },
    setOptionDataByActive(){
      this.option.series[0].data = this.lossType[this.activeIndex].data
      this.option.series[0].name = this.lossType[this.activeIndex].label
      this.option.series[0].lineStyle.color = this.lossType[this.activeIndex].marker
      this.option.series[0].itemStyle.color = this.lossType[this.activeIndex].marker
      this.dealColorAndGrid()
    },

    dealColorAndGrid(){
      if(this.option.series[0].data.every(item=> item == '--')){
        this.option.grid.left = 40
        // 纯空值渐变会报错
        this.option.series[0].areaStyle.color = '#eeeeee'
      } else {
        this.option.grid.left = 20
        this.option.series[0].areaStyle.color = {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops:[{
            offset: 0, color: this.lossType[this.activeIndex].markerLinear // 0% 处的颜色
          },{
            offset: 1, color: this.lossType[this.activeIndex].markerLinearEnd // 100% 处的颜色
          }],
          global: false, // 缺省为 false
        }
      }
    },
    recyleData(){
      clearInterval(this.timer)
      this.timer = null
      this.timer = setInterval(()=>{
        if(this.activeIndex == 5){
          this.activeIndex = 0
        } else {
          this.activeIndex += 1
        }
       this.setOptionDataByActive()
        this.myChart.setOption(this.option,true);
      },15 * 1000)
    }
  }
}
</script>

<style lang="less" scoped>
.loss-type-legend-left-area{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}
.loss-marker{
  width: 8px;
  height: 8px;
  border-radius: 8px;
}
.loss-chart{
  height: calc(100% - 50px);
}

.loss-type{
  padding: 4px;
}
.loss-type-item{
  width: 33%;
}
</style>
