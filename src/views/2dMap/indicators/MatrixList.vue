<!-- 实时监测 光伏区 电站列表 -->
<template>
  <div class="width-100 height-100 dark-scroll">
    <div class="matrixs">
      <div class="matrix-item flex-center cursor-pointer"
           :style="getStyle(index)"
           :class=" 'grade-hover-' + (item.grade) "
           @click="changeMatrix(index,false)"
           v-for="(item,index) in dataParams.areaData" :key="index">
        <span class="font-14">
          {{ item.name }}
        </span>
        <svg-icon v-if="item.grade" icon-class="fault-tip" class="fault-tip"/>
      </div>
    </div>

    <alarm-list ref="alarmList"
                @callBack="callBack"
                :data-params="{areaType:1,monitorType:1}"
                @setMatrixHighlight="setMatrixHighlight"
                class="alarm-list" />
  </div>
</template>

<script>
import AlarmList from "./AlarmList";
import {textColor} from "../../../utils/color";
import IndicatorItem from "../modules/IndicatorItem";
export default {
  name: "MatrixList",
  components:{
    IndicatorItem,
    AlarmList
  },
  props: {
    dataParams:{
      type:Object,
      default:()=>{
        return {
          areaData:[],
        }
      }
    }
  },
  data() {
    return {
      activeIndex:undefined,
      psaInfo:null,
      alarmNum:0,
      gradeColor:[
        {
          textColor:textColor.main,
          activeColor:textColor.highlight,
        },{
          textColor:'#FF8D8F',
          activeColor:'#FFDDDE',
        },{
          textColor:'#FFA56E',
          activeColor:'#FFE4D3',
        },{
          textColor:'#C8C1FF',
          activeColor:'#E9E6FF',
        },{
          textColor:'#C0CBCF',
          activeColor:'#C0CBCF',
        },
      ]
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.$emit('setLoading',false)
  },
  methods: {
    changeMatrix(index,fromMap = false){
      console.log(index)
      if(fromMap){
        this.activeIndex = index
        this.$nextTick(()=>{
          this.$refs.alarmList.filterByArea(this.dataParams.areaData[index].uniqueIndex)
        })
        return
      }
      if(this.activeIndex == index){
        this.activeIndex = undefined
        this.$refs.alarmList.filterByArea()
        return
      }
      this.activeIndex = index;
      this.$refs.alarmList.filterByArea(this.dataParams.areaData[index].uniqueIndex)
      this.$emit('callBack',{
        type:'changeMatrix',
        params:{
          area:this.dataParams.areaData[index]
        }
      })
    },
    callBack(e){
      this.$emit('callBack',e)
      if(e.type == 'setAlarmNum'){
        this.alarmNum = e.params.num
      }
    },
    // 根据告警列表 对方阵列表颜色状态设置
    setMatrixHighlight(alarmData){
      let arr = alarmData.reduce((prev,cur)=>{
        let index = prev.findIndex(el => el.parentUnitPsKey == cur.parentUnitPsKey)
        if(index == -1){
          prev.push(cur)
          return prev
        } else {
          if(prev[index].alarmGrade > cur.alarmGrade){
            prev[index] = cur
          }
          return prev
        }
      },[])
      this.dataParams.areaData.map(item=>{
        let temp = arr.find(el => el.parentUnitPsKey == item.uniqueIndex)
        if(temp){
          item.grade = temp.alarmGrade
        } else {
          item.grade = 0
        }
      })
    },
    getStyle(index){
      let { grade } = this.dataParams.areaData[index]
      grade = Number(grade)
      if(index == this.activeIndex){
        return {
          color:this.gradeColor[grade].activeColor,
          background:`url(${require(`../../../assets/images/2dMap/matrixBg/${ grade == 0 ? 'normal' : 'grade-' + grade  }-selected.svg`)})` ,
        }
      }
      return {
        color:this.gradeColor[grade].textColor,
        background:`url(${require(`../../../assets/images/2dMap/matrixBg/${ grade == 0 ? 'normal' : 'grade-' + grade  }.png`)})`,
        'background-position': 'center',
      }
    },
  }
}
</script>

<style lang="less" scoped>

.title {
  height: 32px;
  position: relative;
  .title-label{
    position: relative;
    z-index: 10;
  }
  .title-icon{
    font-size: 20px;
    margin: 0 6px;
    z-index: 10;
  }
  .title-slot{
    position: relative;
    z-index: 10;
  }
  .left-bg{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #0661AE 0%, rgba(5, 97, 175, 0) 100%);
    border-left: 2px solid #BDE2FF;
    z-index: 0;
    .line-1{
      position: absolute;
      right: 0;
      top: 0;
      background: linear-gradient(90deg, rgba(11, 94, 163, 0) 0%, #0B5EA3 100%);
      height: 1px;
      width: 97px;
    }
    .line-2{
      position: absolute;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, rgba(11, 94, 163, 0) 0%, #0B5EA3 100%);
      height: 1px;
      width: 97px;
    }
  }
}


.grade-hover-0:hover{
  color:@di-color-text-main !important;
  background: url("../../../assets/images/2dMap/matrixBg/normal-hover.svg") !important;
  background-position:center!important;
}

.grade-hover-1:hover{
  color:#FF8D8F !important;
  background: url("../../../assets/images/2dMap/matrixBg/grade-1-hover.svg") !important;
  background-position:center!important;
}

.grade-hover-2:hover{
  color:#FFA56E !important;
  background: url("../../../assets/images/2dMap/matrixBg/grade-2-hover.svg") !important;
  background-position:center!important;
}

.grade-hover-3:hover{
  color:#C8C1FF !important;
  background: url("../../../assets/images/2dMap/matrixBg/grade-3-hover.svg") !important;
  background-position:center!important;
}

.grade-hover-4:hover{
  color: #C0CBCF !important;
  background: url("../../../assets/images/2dMap/matrixBg/grade-4-hover.svg") !important;
  background-position:center!important;
}

.alarm-num-in-title{
  height: 18px;
  border-radius: 9px;
  line-height: 18px;
  text-align: center;
  padding: 0 6px;
  background: linear-gradient(180deg, #FA8C8C 0%, #E44242 43%, #86688D 100%);
  box-sizing: border-box;
  border: 0.5px solid @di-color-text-white;
  z-index: 1;
}


.matrixs{
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  .matrix-item{
    width: 16%;
    margin-right: .6%;
    margin-bottom: 1%;
    height: 38px;
    border-radius: 2px;
    box-sizing: border-box;
    position: relative;

    .fault-tip{
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      font-size: 12px;
    }
  }
  .matrix-item:nth-child(6n){
    margin-right: 0;
  }
}

.alarm-list{
  height: 250px;
  width: 100%;
}

</style>