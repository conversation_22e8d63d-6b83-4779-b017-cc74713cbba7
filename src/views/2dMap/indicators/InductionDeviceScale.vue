<!-- 实时监测/电站概览/感应设备规模指标卡 -->
<template>
  <div>
    <div class="flex-center margin-t-16">
      <div class="flex-start flex-column margin-r-40 cursor-pointer" @click="activeType(0)" :class="activeIndex == 0 ? 'active-status' : ''">
        <div class="status-img total-img"></div>
        <span class="font-14 color-text-second statistics-text">总数</span>
        <div>
          <span class="color-text-main num-font-700 status-num">{{ totalNum }}</span>
          <span class="color-text-white font-14 color-text-gray">个</span>
        </div>
      </div>
      <div class="flex-start flex-column margin-r-40 cursor-pointer" @click="activeType(1)" :class="activeIndex == 1 ? 'active-status' : ''">
        <div class="status-img online-img"></div>
        <span class="font-14 color-text-second statistics-text">在线</span>
        <div>
          <span class="color-text-green num-font-700 status-num">{{ onlineNum }}</span>
          <span class="color-text-white font-14 color-text-gray">个</span>
        </div>
      </div>
      <div class="flex-start flex-column cursor-pointer" @click="activeType(2)" :class="activeIndex == 2 ? 'active-status' : ''">
        <div class="status-img offline-img"></div>
        <span class="font-14 color-text-second statistics-text">离线</span>
        <div>
          <span class="color-text-red num-font-700 status-num">{{ offlineNum }}</span>
          <span class="color-text-white font-14 color-text-gray">个</span>
        </div>
      </div>
    </div>

    <div class="flex-start width-100 flex-row-wrap device-area">
      <div class="device-item linear-bg-border flex-start" :key="item.deviceType" :class="{ 'margin-b-16': index < 6 }" v-for="(item, index) in deviceData">
        <img class="device-item-img" :src="getPng(item.iconType)" />
        <img class="line-bg" :src="lineBg" />
        <div class="device-item-right">
          <span class="color-text-main font-14">{{ item.deviceTypeName }}</span>
          <span class="num-font-700 color-text-white device-num" :style="{ color: typeColors[activeIndex] }">{{ nowDeviceNums[index] }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getModelData } from '../../../api/2dMap/psOverview';
import { textColor } from '../../../utils/color';

export default {
  name: 'InductionDeviceScale',
  props: {},
  data() {
    return {
      indicatorData: {
        name: '',
        num: 233
      },
      totalNum: 0,
      onlineNum: 0,
      offlineNum: 0,
      activeIndex: 0,
      typeColors: [textColor.main, textColor.green, textColor.red],
      deviceData: [
        {
          deviceType: '54',
          deviceTypeName: '摄像头',
          iconType: 'video',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '53',
          deviceTypeName: '传感器',
          iconType: 'sensor',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '61',
          deviceTypeName: '智能门禁',
          iconType: 'smart-control',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '62',
          deviceTypeName: '消防系统',
          iconType: 'fire-system',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '63',
          deviceTypeName: '智能锁控',
          iconType: 'smart-lock',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '64',
          deviceTypeName: '数字表计',
          iconType: 'digital-counter',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '101',
          deviceTypeName: '电子围栏',
          iconType: 'electronic-fence',
          onlineNum: 0,
          offlineNum: 0
        },
        {
          deviceType: '65',
          deviceTypeName: '风机监测',
          iconType: 'wind-fan',
          onlineNum: 0,
          offlineNum: 0
        }
      ],
      nowDeviceNumData: {},
      lineBg: require('@/assets/images/2dMap/2d-device-item-line.png'),
      timer: null,
      psaInfo: null
    };
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.refreshData();
  },
  destroyed() {
    clearInterval(this.timer);
    this.timer = null;
  },
  computed: {
    nowDeviceNums() {
      return this.deviceData.map((item) => {
        if (this.activeIndex == 0) {
          return item.offlineNum + item.onlineNum;
        }
        if (this.activeIndex == 1) {
          return item.onlineNum;
        }
        if (this.activeIndex == 2) {
          return item.offlineNum;
        }
      });
    }
  },
  methods: {
    async refreshData() {
      this.$emit('setLoading', true);
      let res = await getModelData({
        psaId: this.psaInfo.psaList[0],
        psId: this.psaInfo.psId,
        modelName: 'sensorScale',
        deviceType: this.deviceData.map((item) => item.deviceType).join(',')
      }).catch((e) => {
        this.$emit('setLoading', false);
        return;
      });
      let data = res.result_data?.dataResult?.sensorStatus || {};
      this.totalNum = 0;
      this.onlineNum = 0;
      this.offlineNum = 0;

      this.deviceData.map((item, index) => {
        let temp = data.find((el) => el.deviceType == item.deviceType) || {};
        item.onlineNum = temp.onlineNum || 0;
        item.offlineNum = temp.offlineNum || 0;
        this.totalNum = this.totalNum + item.onlineNum + item.offlineNum;
        this.onlineNum += item.onlineNum;
        this.offlineNum += item.offlineNum;
      });
      this.activeIndex = 0;

      this.recyleShow();
      this.$emit('setLoading', false);
    },
    activeType(type) {
      this.activeIndex = type;
      this.recyleShow();
    },
    recyleShow() {
      clearInterval(this.timer);
      this.timer = null;
      this.timer = setInterval(() => {
        if (this.activeIndex == 2) {
          this.activeIndex = 0;
        } else {
          this.activeIndex += 1;
        }
      }, 15 * 1000);
    },
    getPng(name) {
      return require(`@/assets/images/2dMap/deviceImage/${name}.png`);
    }
  }
};
</script>

<style lang="less" scoped>
.status-img {
  width: 76px;
  height: 60px;
  background-size: 76px 60px;
}

.total-img {
  background-image: url('../../../assets/images/2dMap/deviceImage/total.png');
}

.active-status {
  .statistics-text {
    color: @di-color-text-main;
  }
}

.active-status .total-img,
.total-img:hover {
  background-image: url('../../../assets/images/2dMap/deviceImage/total-hover.png');
}

.online-img {
  background-image: url('../../../assets/images/2dMap/deviceImage/online.png');
}

.active-status .online-img,
.online-img:hover {
  background-image: url('../../../assets/images/2dMap/deviceImage/online-hover.png');
}

.offline-img {
  background-image: url('../../../assets/images/2dMap/deviceImage/offline.png');
}

.active-status .offline-img,
.offline-img:hover {
  background-image: url('../../../assets/images/2dMap/deviceImage/offline-hover.png');
}

.device-area {
  margin-top: 14px;
}

.status-num {
  font-size: 20px;
  line-height: 22px;
  margin-right: 4px;
}

.device-num {
  font-size: 22px;
  line-height: 22px;
}

.device-item {
  height: 38px;
  width: 178px;
  margin-right: 12px;
  border-radius: 4px;
  padding: 4px;
  position: relative;

  .line-bg {
    position: absolute;
    height: 100%;
    right: 0;
    top: 0;
  }
}

.device-item:nth-child(2n) {
  margin-right: 0;
}

.device-item-img {
  width: 52 * 0.6px;
  height: 58 * 0.6px;
}

.device-item-right {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 10px;
  padding-right: 16px;
  width: 100%;
}

.color-text-green {
  color: #6bc292;
}
.color-text-red {
  color: #de7a7b;
}
</style>
