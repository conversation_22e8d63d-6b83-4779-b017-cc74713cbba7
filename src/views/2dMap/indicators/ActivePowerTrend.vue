<!-- 实时监测/升压站/有功功率趋势 指标卡 -->

<template>
  <div id="chartActivePowerTrend" class="width-100 height-100">
  </div>
</template>

<script>
import echarts from '@/utils/enquireEchart'
import {textColor,chartColor} from "../../../utils/color";
import { getModelData } from "../../../api/2dMap/psOverview";
import moment from "moment";
export default {
  name: "ActivePowerTrend",
  props: {},
  data() {
    return {
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops:[{
                  offset: 0, color: 'rgba(64, 170, 255, 0)' // 0% 处的颜色
                },{
                  offset: 1, color: 'rgba(64, 170, 255, 0.4)' // 100% 处的颜色
                }],
                global: false, // 缺省为 false
              },
            }
          },
          className:'di-chart-tooltip',
          borderColor: 'rgba(189, 226, 255, 0.4)',
          borderWidth:1,
          formatter: params => {
            return `<div class="title font-14">${params[0].axisValue}</div>
                    <div class="content">
                      <div  class="content-item">
                        <div class="flex-start">
                        ${params[0].marker}
                        <span class="tooltip-item-label">${params[0].seriesName}：</span>
                        </div>
                        <span>
                        <span class="color-text-white">${(params[0].value ==0 ? 0 :(params[0].value || '--'))}</span>
                        <span class="color-text-gray content-unit">MW</span>
                        </span>
                      </div>

                    </div>`
          }
        },
        grid:{
          bottom: 10,
          top: "50",
          left: "20",
          right: "10",
          containLabel: true,
        },
        xAxis: {
          data: [],
          axisTick: { show: false },
          axisLine: {
            show: true,
            lineStyle:{
              color:chartColor.splitLine
            }
          },
          axisLabel: {
            color: textColor.main
          },
        },

        legend: {
          itemHeight: 13,
          itemWidth: 13,
          align: 'left',
          icon: 'image://' + require('@/assets/images/2dMap/2d-chart-daily-power-trend-legend.png'),
          textStyle: {
            color: textColor.main,
            number: 12,
          },
          selectedMode: false,
          data: [{ name: '有功功率' }] ,
          top: '14'
        },
        yAxis: {
          name: 'MW',
          nameTextStyle: {
            align: 'right',
            color: textColor.main
          },
          splitLine: {
            show: true,
            lineStyle: {
              color:chartColor.splitLine
            }
          },
          axisTick: { show: false },
          axisLine: { show: false },
          axisLabel: {
            show: true,
            color: textColor.main
          }
        },
        color: [textColor.main],
        series: [
          {
            type:'line',
            name:'有功功率',
            symbol:'image://' + require('@/assets/icons/2dMap/2d-chart-generate-marker.png') ,
            symbolPosition: 'end',
            smooth:true,
            symbolSize: 3.5,
            showSymbol:true,
            showAllSymbol:true,
            lineStyle:{
              width:.5,
            },
            label: {
              show: false,
              color: textColor.white,
              position: 'top'
            },
            areaStyle: {   //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1,[{
                  offset: 0, color: 'rgba(38, 142, 235, 0.55)' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(38, 142, 235, 0)' // 100% 处的颜色
                }]
              )
            },
            data: []
          }
        ]
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1,[{
          offset: 0, color: 'rgba(38, 142, 235, 0.55)' // 0% 处的颜色
        }, {
          offset: 1, color: 'rgba(38, 142, 235, 0)' // 100% 处的颜色
        }]
      ),
      myChart:null,
      psaInfo:null
    }
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');

  },
  mounted() {
    this.refreshData()
  },
  methods: {
    async refreshData(){
      this.$emit('setLoading',true)
      let res = await getModelData({
        psId:this.psaInfo.psId,
        modelName:'110kVPower',
        psKey:'107353_12_28_1',
        recordDate:moment().format('YYYY-MM-DD')
      }).catch(e=>{
        this.$emit('setLoading',false)
        return
      })
      let data = res.result_data.dataResult['110kVPower']

      this.option.series[0].data = data.map(item=> item.power == null ? '--' : (item.power / (1000 * 1000)).toFixed(3) )
      this.option.xAxis.data = data.map(item => item.time)

      this.dealColorAndGrid()

      if(this.myChart == null){
        let chartDom = document.getElementById('chartActivePowerTrend');
        this.myChart = echarts.init(chartDom);
      }
      this.myChart.setOption(this.option);
      this.$emit('setLoading',false)
    },
    dealColorAndGrid(){
      if(this.option.series[0].data.every(item=> item == '--')){
        this.option.grid.left = 40
        // 纯空值渐变会报错
        this.option.series[0].areaStyle.color = '#eeeeee'
      } else {
        this.option.grid.left = 20
        this.option.series[0].areaStyle.color = this.color
      }
    },
  }
}
</script>
