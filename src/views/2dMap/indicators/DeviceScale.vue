<!-- 实时监测 设备规模 指标卡 -->
<template>
  <div class=" width-100 device-area">
    <div class="device-item linear-bg-border flex-space-between"
         v-for="(item,index) in deviceData"
         :key="index"
    >
      <div class="flex-start">
        <img class="device-item-img margin-r-8" :src="getPng(item.imgName)"/>
        <span class="color-text-main font-14">{{ item.label }}</span>
      </div>

      <span class="num-font-700 color-text-white font-20 device-num margin-r-12">{{ item.value }}</span>
      <img class="line-bg" :src="item.src" />
    </div>

  </div>

</template>

<script>
import {getModelData} from "../../../api/2dMap/psOverview";
import moment from "moment";

export default {
  name: "DeviceScale",
  props: {
    dataParams:{
      type:Object,
      default:()=>{
        return {
          type:1
        }
      }
    }
  },
  data() {
    return {
      deviceDataPhotovolaticArea:[
        {
          label:'集电线',
          value:0,
          deviceType:'3',
          imgName: 'collecting-wires',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'箱变',
          value:0,
          deviceType:'6',
          imgName: 'transformer-box',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'逆变器',
          value:0,
          deviceType:'1',
          imgName: 'inverter',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'汇流箱',
          value:0,
          deviceType:'4',
          imgName: 'combiner-box',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'组串',
          value:0,
          deviceType:'10',
          imgName: 'string-formation',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'组件',
          value:0,
          deviceType:'58',
          imgName: 'assembly',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },
      ],

      deviceDataBoosterStation:[
        {
          label:'主变',
          value:0,
          deviceType:'6-1',
          imgName: 'main-transformer',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'SVG变压器',
          value:0,
          deviceType:'29',
          imgName: 'svg-inverter',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'站用变/接地变',
          value:0,
          deviceType:'6-3',
          imgName: 'station-transformer',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'备用变',
          value:0,
          deviceType:'6-50',
          imgName: 'backup-transformer',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'110kv间隔',
          value:0,
          deviceType:'12-1',
          imgName: '110kV-interval',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },{
          label:'35kv间隔',
          value:0,
          deviceType:'12-2',
          imgName: '35kV-interval',
          src:require('@/assets/images/2dMap/2d-device-item-line.png'),
        },
      ],
      deviceData:[],
      psaInfo:null
    }
  },
  created() {
    this.deviceData = JSON.parse(JSON.stringify(this.dataParams.type == 1 ? this.deviceDataPhotovolaticArea : this.deviceDataBoosterStation))
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.refreshData()
  },
  methods: {
   async refreshData(){
     let res = await getModelData({
       psId:this.psaInfo.psId,
       modelName:'deviceScale',
     }).catch(e=>{
       this.$emit('setLoading',false)
       return
     })
     let data = res.result_data.dataResult.deviceScale

     this.deviceData.map(item =>{
       let temp = data.find(el => el.deviceType == item.deviceType)
       if(temp){
         item.value = temp.number || 0
         item.label = temp.deviceTypeName
       } else {
         item.value = 0
       }
     })

      this.$emit('setLoading',false)
    },
    getPng (name) {
      return require(`@/assets/images/2dMap/deviceImage/${name}.png`)
    }
  }
}
</script>

<style lang="less" scoped>
.device-area{
  display: flex;
  flex-wrap: wrap;
}
.device-item-img{
  width: 50px;
  height: 50px;
}
.device-item{
  height: 58px;
  width: 178px;
  margin-right: 12px;
  border-radius: 4px;
  margin-top:22px;
  padding: 4px;
  position: relative;
  box-sizing: border-box;
  .line-bg{
    position: absolute;
    height: 100%;
    right: 0;
    top: 0;
  }
}

.device-item:nth-child(2n){
  margin-right: 0;
}

</style>