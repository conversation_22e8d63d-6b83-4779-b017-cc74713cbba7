<!-- 实时监测 重点区域监控画面 指标卡 -->
<template>
  <div class="width-height-100">
    <div class="flex-start flex-row-wrap width-height-100 flex-gap-12 margin-t-12">
      <div class="flex-center width-height-100" v-if="monitors.length == 0">
        <div class="no-data"></div>
      </div>
      <div class="monitor-item flex-start flex-column" :class="{ 'single-monitor-item': videoDisplayNum == 1 }" v-for="(item, index) in monitors" :key="index">
        <div class="live-area margin-b-8">
          <Live class="width-100 height-100" v-if="item.url" :url="item.url" :psKey="item.psKey" />
          <div class="width-height-100 flex-center" v-else>
            <div class="no-config margin-t-8"></div>
          </div>
        </div>
        <div class="live-label flex-center">
          <img :src="titleBgURL" class="label-bg" alt="title-bg" />
          <p class="font-14 color-text-white label-text margin-b-0 text-ellipsis" :title="item.name">
            {{ item.name }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Live from '@/components/Live';
import { pLayLiveApi } from '../../../api/common';
import { getModelData } from '../../../api/2dMap/psOverview';

export default {
  name: 'ImportantMonitorVideo',
  components: { Live },
  props: {
    dataParams: {
      type: Object,
      default: () => {
        return {
          areaType: 1
        };
      }
    },
    // 需要展示的设备个数
    videoDisplayNum: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      titleBgURL: require('../../../assets/images/2dMap/2d-booster-station-room-title-bg.png'),
      monitors: []
    };
  },

  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
    this.refreshData();
  },
  methods: {
    async refreshData() {
      this.$emit('setLoading', true);
      let res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: 'cameraList',
        isKeyArea: 1,
        ...this.dataParams
      }).catch(() => {
        this.$emit('setLoading', false);
        return;
      });
      const { cameraInfo = [] } = res.result_data.dataResult;
      let monitorList = cameraInfo.slice(0, this.videoDisplayNum);
      this.monitors = monitorList
        .filter((el, index) => index < this.videoDisplayNum)
        .map((item) => {
          return {
            name: item.deviceName,
            onlineStatus: item.onlineStatus,
            psKey: item.psKey,
            id: item.psKey
          };
        });
      if (monitorList.length === 0) {
        this.$emit('setLoading', false);
        return;
      }
      res = await pLayLiveApi({
        psaId: this.psaInfo.psaList[0],
        psId: this.psaInfo.psId,
        psKeys: this.monitors.map((item) => item.psKey).join(',')
      }).catch(() => {
        this.$emit('setLoading', false);
        return [];
      });
      this.monitors = this.monitors.map((item) => {
        let temp = res.find((el) => el.psKey == item.psKey);
        return {
          ...item,
          url: temp ? (item.onlineStatus == 1 ? temp.liveUrl[0] : '') : ''
        };
      });
      this.$emit('setLoading', false);
    }
  }
};
</script>

<style lang="less" scoped>
.monitor-item {
  width: 178px;
  height: 124px;
  //margin-top: 12px;
  //margin-right: 12px;
  transform: scale(1, 0.95);
  transform-origin: center top;

  &.single-monitor-item{
    width: 100%;
    height: 100%;
    .live-area{
      width: 100%;
      height: calc(100% - 35px);
      background-size: inherit;
    }
  }

  .live-area {
    width: 178px;
    height: 94px;
    border-radius: 4px;
    padding: 4px;
    background-image: url('../../../assets/images/live-video-bg.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  .live-label {
    width: 178px;
    height: 24px;
    position: relative;

    .label-text {
      z-index: 1;
      width: 80%;
      text-align: center;
    }

    .label-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
    }
  }
}

.monitor-item:nth-child(2n) {
  margin-right: 0;
}
</style>
