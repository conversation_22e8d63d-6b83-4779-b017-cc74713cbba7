<template>
  <div class="width-height-100 padding-24">
    <a-row :gutter="24">
      <a-col :xxl="4" :xl="6" :md="8">
        <div class="search-item">
          <span class="search-label">电站名称</span>
          <a-input v-model="deviceName" placeholder="请输入门禁名称" allowClear />
        </div>
      </a-col>
      <a-col :xxl="4" :xl="6" :md="8">
        <div class="search-item">
          <DiThrottleButton label="查询" @click="handleQuery" />
          <DiThrottleButton label="重置" class="di-cancel-btn margin-l-12" @click="handleReset" />
        </div>
      </a-col>
    </a-row>
    <div class="divide-line margin-t-24 margin-b-24"></div>
    <a-spin :spinning="loading">
      <vxe-table
        class="my-table"
        ref="xTable"
        :data="dataSource"
        resizable
        show-overflow
        border="none"
        :height="tableHeight - 120"
        :seq-config="{ startIndex: (pagination.currentPage - 1) * pagination.pageSize }"
      >
        <vxe-table-column type="seq" title="序号" width="120"></vxe-table-column>
        <vxe-table-column v-for="item in columns" :key="item.key" :title="item.label" :min-width="120" :formatter="$tabFormatter">
          <template #default="{ row }">
            <span>{{ row[item.key] }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column title="操作" width="350">
          <template #default="{ row }">
            <div class="flex-start flex-gap-8">
              <DiThrottleButton label="开门" size="small" @click="handleControl('2', row)" />
              <DiThrottleButton label="闭门" size="small" @click="handleControl('1', row)" />
              <DiThrottleButton label="常开" class="di-cancel-btn" size="small" @click="handleControl('0', row)" />
              <DiThrottleButton label="常闭" class="di-cancel-btn" size="small" @click="handleControl('3', row)" />
            </div>
          </template>
        </vxe-table-column>
      </vxe-table>
    </a-spin>
    <page-pagination :total="total" :page-size="pagination.pageSize" :current="pagination.currentPage" @size-change="sizeChange" />
  </div>
</template>

<script>
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { getSecurityListApi, operateDoorApi } from '@/api/health/healthapi';

export default {
  name: 'DoorControl',
  mixins: [tableHeight],
  data() {
    return {
      deviceName: undefined,
      dataSource: [],
      columns: [
        { label: '门禁名称', key: 'deviceName' },
        { label: '当前状态', key: 'doorStateName' }
      ],
      total: 0,
      pagination: {
        pageSize: 10,
        currentPage: 1
      },
      timer: null,
      isPolling: false, // 添加轮询状态标志
      loading: false
    };
  },
  created() {

    this.initRefreshTimer();
  },
  beforeDestroy() {
    this.clearTimer();
  },
  methods: {
    async getList(needLoading = true) {
      if (this.isPolling) return; // 如果正在轮询，直接返回

      this.isPolling = true;
      try {
        this.loading = needLoading;
        const params = {
          ...this.pagination,
          deviceName: this.deviceName
        };
        const res = await getSecurityListApi(params);
        this.dataSource = res.results;
        this.total = res.total;
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        this.isPolling = false;
        this.loading = false;
      }
    },
    handleQuery() {
      Object.assign(this.pagination, { currentPage: 1 });
      this.initRefreshTimer();
    },
    handleReset() {
      this.deviceName = undefined;
      this.handleQuery();
    },
    async handleControl(type, row) {
      const { psKey } = row;
      await operateDoorApi({
        controlType: type,
        psKey
      });
      this.$message.success('操作成功');
      this.initRefreshTimer();
    },
    initRefreshTimer() {
      this.clearTimer();
      this.getList();
      this.timer = setInterval(async () => {
        await this.getList(false);
      }, 5 * 1000);
    },
    sizeChange(current, pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.currentPage = current;
      this.getList();
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    }
  }
};
</script>
