<template>
  <div class="small-card-bg gas-fire">
    <DeviceControl :deviceInfo="deviceInfo" :pa="pa" :status="status">
    </DeviceControl>
    <!-- <div class="divide-line"></div> -->
    <div class="content">
      <div class="flex-space-between paddiging-t-8 padding-l-12 padding-r-12">
        <span class="color-text-main">摄像头24小时温度趋势</span>
        <svg-icon icon-class="camera" class="font-20"></svg-icon>
      </div>
      <div :id="id" class="width-100 height-98"></div>
    </div>
  </div>
</template>
<script>
import DeviceControl from "./DeviceControl.vue";

import LineChart from "../mixin/LineChart";
export default {
  name: "GasFire",
  mixins: [LineChart],
  components: {
    DeviceControl,
  },
  props: {
    id: {
      type: String,
      default: "gas-fire",
    },
    deviceInfo: {
      type: Object,
      default: () => {
        return {
          title: "气体消防系统",
          viceTitle: "七氟丙烷灭火系统",
          indicator: "当前气压（Pa）",
          isOnline: 1,
        };
      },
    },
    pa: {
      type: String,
      default: "0",
    },
    status: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      xData: [
        "00:00",
        "02:00",
        "04:00",
        "06:00",
        "08:00",
        "10:00",
        "12:00",
        "14:00",
        "16:00",
        "18:00",
        "20:00",
        "22:00",
        "24:00",
      ],
      yData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      legend: [
        {
          name: "温度",
          unit: "℃",
          startColor: "#9DC535",
          endColor: "rgba(255, 255, 255, 0)",
        },
      ],
    };
  },
  methods: {
    getChartData() {
      // ToDO 获取echart 数据
    },
  },
  beforeDestroy() {},
};
</script>
<style scoped lang="less">
.small-card-bg {
  background: #17539f;
  border-radius: 8px;
}
.content {
  height: calc(100% - 110px);
  position: relative;
  margin-top: 8px;
}
.height-98 {
  height: calc(100% - 22px);
  position: relative;
}
.gas-fire {
  padding: 12px 0 0;
}
canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
}
</style>
