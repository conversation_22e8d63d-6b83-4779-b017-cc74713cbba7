<template>
  <div class="fire-control">
    <div class="title width-100" :class="{'alarm-title-bg': !disabled}">{{ getLabel(deviceInfo.deviceName, null) }}</div>
    <div class="status-box flex-center flex-gap-8 margin-t-24">
      <div class="status flex-center flex-gap-36">
        <div class="flex-center">
          <svg-icon :icon-class="iconClass" class="font-size-48" />
          <span class="label">告警状态：</span>
          <span class="value flex-center">
          <svg-icon :icon-class="alarmIconClass" class="font-size-24 margin-r-4" />
          <span class="status-value" :class="{ 'color-text-error': !disabled, 'color-text-success': disabled }">{{ alarmStatus }}</span>
        </span>
        </div>
        <div class="status-btn flex-center flex-gap-16">
          <!--          <DiThrottleButton label="消音" :disabled="disabled" :loading="loading" :class="{ 'di-cancel-btn': disabled }" @click="handleControl(0)" />-->
          <DiThrottleButton label="消音" @click="handleControl(0)" />
          <DiThrottleButton label="启动消防" :disabled="disabled" :class="{ 'di-cancel-btn': disabled }" @click="handleControl(1)" />
        </div>
      </div>
    </div>
    <div class="live-box">
      <div class="light-live">
        <Live class="width-height-100" :url="lightLive.url" />
        <div class="live-label flex-center">
          <img :src="titleBgURL" class="label-bg" alt="title-bg" />
          <p class="font-14 color-text-white label-text margin-b-0 text-ellipsis" :title="lightLive.name">
            {{ getLabel(lightLive.name, null) }}
          </p>
        </div>
      </div>
      <div class="infrared-live">
        <Live class="width-height-100" :url="infraredLive.url" />
        <div class="live-label flex-center">
          <img :src="titleBgURL" class="label-bg" alt="title-bg" />
          <p class="font-14 color-text-white label-text margin-b-0 text-ellipsis" :title="infraredLive.name">
            {{ getLabel(infraredLive.name, null) }}
          </p>
        </div>
      </div>
    </div>
    <Verify
      @success="verifySuccess"
      @error="verifyError"
      @close="verifyClose"
      :mode="'pop'"
      :captchaType="'blockPuzzle'"
      modeType="99"
      :imgSize="{ width: '330px', height: '155px' }"
      ref="verify"
      :loginParams="{}"
    />
  </div>
</template>

<script>
import Live from '@/components/Live';
import { pLayLiveApi } from '@/api/common';
import { thirdDeviceCtrlApi } from '@/api/health/healthapi';
import Verify from '@/components/verifition/Verify.vue';

export default {
  name: 'FireControl',
  components: { Verify, Live },
  props: {
    deviceInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    iconClass: {
      type: String,
      default: ''
    },
    psaInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      titleBgURL: require('@/assets/images/2dMap/2d-booster-station-room-title-bg.png'),
      lightLive: {
        name: '',
        url: ''
      },
      infraredLive: {
        name: '',
        url: ''
      },
      firePoint: 'p620001'
    };
  },
  mounted() {
    this.getLiveUrl();
    Object.assign(this.lightLive, { name: this.extend.cameraNames?.split(',')?.[0] });
    Object.assign(this.infraredLive, { name: this.extend.cameraNames?.split(',')?.[1] });
  },
  computed: {
    extend() {
      const extend = JSON.parse(this.deviceInfo.extend || '{}');
      // if (Math.abs(Date.now() - extend?.time) > 10 * 60 * 1000) return {};
      return extend;
    },
    alarmStatus() {
      const isEmpty = this.$isEmpty(this.extend[this.firePoint]);
      return isEmpty ? '--' : this.disabled ? '正常' : '告警';
    },
    alarmIconClass() {
      const isEmpty = this.$isEmpty(this.extend[this.firePoint]);
      return isEmpty ? '' : this.disabled ? 'normal' : 'alarm-error';
    },
    disabled() {
      return this.extend[this.firePoint] == 1;
    }
  },
  methods: {
    async getLiveUrl() {
      const res = await pLayLiveApi({
        psaId: this.psaInfo.psaList[0],
        psId: this.psaInfo.psId,
        psKeys: this.extend.psKeys
      }).catch(() => {
        return [];
      });
      const lightUrlObj = res.find((item) => item.psKey == this.extend.psKeys.split(',')[0]);
      const infraredUrlObj = res.find((item) => item.psKey == this.extend.psKeys.split(',')[1]);
      Object.assign(this.lightLive, { url: lightUrlObj?.liveUrl?.[0] });
      Object.assign(this.infraredLive, { url: infraredUrlObj?.liveUrl?.[0] });
    },
    handleControl(controlType) {
      const content = controlType == 1 ? '确定要开启消防灭火设备吗？' : '确定要消除本次报警声音吗？';
      this.$confirm({
        title: '操作确认',
        content: content,
        centered: true,
        iconType: 'exclamation-circle',
        onOk: async () => {
          if (controlType == 1) this.$refs.verify.show();
          else await this.confirmSuccess(controlType);
        }
      });
    },
    async confirmSuccess(controlType) {
      await thirdDeviceCtrlApi({
        controlType,
        psKey: this.deviceInfo.psKey
      }).catch((err) => {
        this.$message.error('操作失败');
        return;
      });
      this.$message.success('操作成功');
      this.$emit('refresh', false);
    },
    verifySuccess(params) {
      console.log('verifySuccess', params);
      this.confirmSuccess(1);
    },
    verifyError(params) {
      console.log('verifyError', params);
    },
    verifyClose(params) {
      console.log('verifyClose', params);
    }
  }
};
</script>
<style lang="less" scoped>
.fire-control {
  width: 100%;
  height: 309px;
  border: 1px solid #255dae;
  background: linear-gradient(180deg, #1a4a8e 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  .title {
    height: 38px;
    padding: 8px 16px;
    background: linear-gradient(90deg, #0661ae 0%, rgba(5, 97, 175, 0) 100%);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-radius: 8px 8px 0 0;

    &.alarm-title-bg{
      background: linear-gradient(90deg, #A14B5F 0%, rgba(161, 75, 95, 0) 100%);
    }
  }

  .status-box {
    height: 48px;
    width: 100%;
  }

  .live-box {
    width: 100%;
    height: calc(100% - 110px);
    display: flex;
    gap: 8px;
    justify-content: center;
    padding-top: 24px;

    .light-live,
    .infrared-live {
      width: 45%;
      height: 70%;

      .live-label {
        width: 178px;
        height: 24px;
        position: relative;
        margin: 6px auto;

        .label-text {
          z-index: 1;
          width: 80%;
          text-align: center;
        }

        .label-bg {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: 0;
        }
      }
    }
  }
}
</style>
