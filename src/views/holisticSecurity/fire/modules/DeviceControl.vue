<template>
  <div class="flex-baseline flex-column width-100 padding-l-16 padding-r-16">
    <div>
      <div>{{ deviceInfo.title }}</div>
      <div v-if="deviceInfo.viceTitle" class="color-text-main">
        {{ deviceInfo.viceTitle }}
      </div>
    </div>
    <div class="flex-center width-100 flex-1 flex-gap-16">
      <div class="flex-column flex-center">
        <div class="text-blod font-24">{{ pa }}</div>
        <div>{{ deviceInfo.indicator }}</div>
      </div>
      <div>
        <div :class="isRight ? 'is-right' : ''">
          <span class="device-status margin-r-4" :class="{ offline: deviceInfo.isOnline == 1 }"></span>
          <span class="font-12 color-text-main"> {{ deviceInfo.isOnline == 0 ? '在线' : '离线' }}</span>
        </div>
        <a-switch v-model="deviceStatus" checked-children="开" un-checked-children="关" @change="(checked) => changeEvent(checked, 'deviceStatus')"></a-switch>
      </div>
    </div>
    <div class="margin-t-8 divide-line" v-show="!isRight"></div>
    <Verify
      @success="verifySuccess"
      @error="verifyError"
      @close="verifyClose"
      :mode="'pop'"
      :captchaType="'blockPuzzle'"
      modeType="99"
      :imgSize="{ width: '330px', height: '155px' }"
      ref="verify"
    ></Verify>
  </div>
</template>
<script>
import Verify from '@/components/verifition/Verify';
import { operateFire } from '@/api/holisticSecurity/fire';

export default {
  components: { Verify },
  props: {
    pa: {
      type: String,
      default: ''
    },
    isRight: {
      // 状态是否显示在右侧
      type: Boolean,
      default: false
    },

    deviceInfo: {
      type: Object,
      default: () => {
        return {
          title: '',
          viceTitle: '',
          indicator: '',
          isOnline: 1
        };
      }
    },
    status: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      deviceStatus: this.status
    };
  },
  created() {
    // 这里可以添加一些初始化逻辑
    this.deviceStatus = this.status;
  },
  methods: {
    changeEvent(value) {
      const status = value;
      const self = this;
      this.$confirm({
        title: '提示',
        content: `确定要${status ? '开启' : '关闭'}消防设备吗`,
        onCancel: () => {
          self.deviceStatus = !status;
        },
        onOk: async () => {
          const deviceId = self.deviceInfo.id;
          if (self.deviceStatus) {
            self.$refs.verify.show();
          } else {
            const res = await operateFire({
              deviceId: deviceId,
              status: status ? 1 : 0
            });
            if (res.success) {
              // Todo 调用后端接口
              self.$message.success('关闭成功');
              self.$emit('change', value, deviceId);
            }
          }
        }
      });
      //this.$emit("change", value, type);
    },
    verifySuccess() {
      self.$message.success('启动成功');
      self.$emit('change', value, this.deviceInfo.id);
      // params 返回的二次验证参数
    },
    verifyError() {
      console.log('验证失败');
    },
    verifyClose(params) {
      this.deviceStatus = !this.deviceStatus;
      this.loginBtn = false;

      if (params) {
        if (params.code === 600) {
          //this.$refs.security.showModal(params.result);
          return false;
        } else {
          this.$notification.error({
            message: `开启失败`,
            description: params.message
          });
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
.device-control {
  padding: 8px 16px;
}

.is-right {
  position: absolute;
  right: 32px;
  top: 56px;
}

.offline {
  background: @di-color-text-gray;
}
</style>
