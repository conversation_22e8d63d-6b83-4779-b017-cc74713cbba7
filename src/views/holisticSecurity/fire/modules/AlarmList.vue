<!-- 实时监测 告警列表 指标卡 -->
<template>
  <div class="width-100 height-100 padding-t-16 dark-scroll">
    <div class="table-title width-100">
      <div
        class="title-item"
        :style="{ width: item.width }"
        v-for="(item, index) in columns"
        :key="index"
      >
        <span class="color-text-second font-14">{{ item.label }}</span>
      </div>
    </div>
    <div
      class="table-content width-100"
      :style="{ height: `calc( 100% - ${reduceHeight}px )` }"
    >
      <div class="width-100 flex-center" v-if="filterList.length == 0">
        <div class="no-data margin-t-12"></div>
      </div>

      <div
        class="content-item cursor-pointer"
        @click="selectAlarm(item)"
        v-for="(item, index) in filterList"
        :key="index"
      >
        <div
          v-for="(el, elIndex) in columns"
          :title="item[el.key]"
          :style="{ width: el.width }"
          class="flex-start"
          :key="elIndex"
        >
          <p
            :style="{ color: getAlarmTextColor(el, item) }"
            class="color-text-white font-14 margin-b-0 text-ellipsis num-font-500"
            :title="item[el.key]"
          >
            {{
              el.key == "alarmGrade"
                ? getLabel(alarmLevels[Number(item[el.key]) - 1]?.label)
                : item[el.key]
            }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import { getModelData } from "@/api/2dMap/psOverview";
export default {
  name: "AlarmList",
  props: {
    dataParams: {
      type: Object,
      default: () => {
        return {
          areaType: 1,
          monitorType: undefined,
          deviceType: null
        };
      },
    },
  },
  data() {
    return {
      columns: [
        {
          label: "设备名称",
          key: "deviceName",
          width: "100px",
        },
        // {
        //   label: "设备位置",
        //   key: "location",
        //   width: "96px",
        // },
        {
          label: "告警类型",
          key: "alarmName",
          width: "80px",
        },
        {
          label: "告警时间",
          key: "alarmTime",
          width: "120px",
        },
      ],
      nowAlarmGrade: "", // 现在选中的告警等级 ''为全部
      alarmLevels: [
        {
          value: 1,
          label: "I类",
          num: 0,
          color: "#F07072",
        },
        {
          value: 2,
          label: "II类",
          num: 0,
          color: "#FFA56E",
        },
        {
          value: 3,
          label: "III类",
          num: 0,
          color: "#C8C1FF",
        },
        {
          value: 4,
          label: "IV类",
          num: 0,
          color: "#C0CBCF",
        },
      ],
      totalNum: 0,
      listData: [],
      filterList: [],
      buttons: [
        {
          label: "主变区域",
          value: 1,
          style: "width: 86px;",
        },
        {
          label: "35kV高压室/400V低压室",
          value: 2,
          style: "width: 180px;",
        },
        {
          label: "二次设备室",
          value: 3,
          style: "width: 86px;",
        },
      ],
      monitorAlarmTextColors: [
        {
          key: "82",
          color: "#D883F0",
        },
        {
          key: "86",
          color: "#73BBFF",
        },
        {
          key: "84",
          color: "#F7CE5E",
        },
        {
          key: "87",
          color: "#D3E56D",
        },
      ],
      activeType: undefined,
      filterPsKey: undefined,
      infiniteId: new Date(),
      psaInfo: null,
      reduceHeight: 0,
    };
  },
  created() {
    this.psaInfo = this.$ls.get("PSA_INFO");
    this.refreshData();

    const { areaType, monitorType } = this.dataParams;
    this.reduceHeight = areaType == 2 && monitorType == 1 ? 50 : 60;
    if (monitorType == undefined) {
      this.reduceHeight = 60;
    } else if (monitorType == 2 && monitorType == 1) {
      this.reduceHeight = 50;
    } else {
      this.reduceHeight = 20;
    }
  },
  methods: {
    async refreshData() {
      this.$emit("setLoading", true);
      let res = await getModelData({
        ...this.dataParams,
        psId: this.psaInfo.psId,
        modelName: "alertList",
        alarmGrade: this.nowAlarmGrade || undefined,
        subAreaType: this.activeType
      }).catch((e) => {
        this.$emit("setLoading", false);
        return;
      });
      this.listData = JSON.parse(JSON.stringify(res.result_data || []));
      this.filterList = JSON.parse(JSON.stringify(this.listData));
      if (this.filterPsKey) {
        this.filterByArea(this.filterPsKey);
      }
      if (this.nowAlarmGrade == "") {
        this.getAlarmLevelNums();
        this.$emit("callBack", {
          type: "setAlarmNum",
          params: {
            num: this.listData.length,
          },
        });
        this.$emit("setMatrixHighlight", this.listData);
      }
      this.$emit("setLoading", false);
    },
    getAlarmLevelNums() {
      this.totalNum = this.listData.length;
      this.alarmLevels.map((item) => {
        item.num = this.listData.filter(
          (x) => x.alarmGrade == item.value
        ).length;
        return item;
      });
    },
    alarmGradeChange() {
      this.refreshData();
    },
    getAlarmTextColor(el, item) {
      if (el.key == "alarmGrade") {
        return this.alarmLevels[Number(item.alarmGrade) - 1]?.color;
      } else {
        return "";
      }
    },
    selectAlarm(item) {
      this.$emit("callBack", {
        type: "selectAlarm",
        params: item,
      });
    },
    setRoom(e) {
      if (this.activeType == e.value) {
        return;
      }
      this.activeType = e.value;
      this.refreshData();
    },
    filterByArea(psKey) {
      this.filterPsKey = psKey;
      if (!psKey) {
        this.filterList = JSON.parse(JSON.stringify(this.listData));
      } else {
        this.filterList = this.listData.filter(
          (item) => item.parentUnitPsKey == psKey
        );
      }
      this.$emit("callBack", {
        type: "setAlarmNum",
        params: {
          num: this.filterList.length,
        },
      });
    },
    changeBotton(value) {
      this.activeType = value;
      this.refreshData();
      this.$emit("callBack", {
        type: "goStationRoom",
        params: {
          value,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-item {
  width: 20%;
  text-align: center;
  padding: 0;
}

.alarm-circle-marker {
  width: 10px;
  height: 10px;
  border-radius: 10px;
}
.table-title {
  display: flex;
  justify-content: space-between;
  .title-item {
    text-align: left;
  }
}
.table-content {
  overflow-y: scroll;
  .content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    text-align: left;
    font-size: 14px;
    line-height: 16px;
  }
}
.content-item:hover {
  background: linear-gradient(
    180deg,
    rgba(64, 170, 255, 0.06) 0%,
    rgba(64, 170, 255, 0.28) 100%
  );
}
</style>
