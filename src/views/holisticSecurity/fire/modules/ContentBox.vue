<template>
  <div
    class="content-box width-100 height-100"
    :class="{ 'content-box-bg': isNeedBg }"
  >
    <a-spin :spinning="loading" class="width-100 height-100">
      <div
        class="head-div flex-space-between"
        :class="{'titleBg': isNeedTitleBg}"
        :style="{ borderRadius: !isNeedBg ? '0' : '' }"
      >
        <div class="flex-start">
          <div class="head-marker"></div>
          <svg-icon
            :icon-class="iconClass"
            class="title-icon"
            v-show="iconClass"
          />
          <div :class="{ 'padding-l-16': !iconClass }">{{ title }}</div>
        </div>
        <div class="right">
          <slot name="right"></slot>
        </div>
      </div>
      <div class="content" :class="{'no-padding': !contentNeedPadding}"><slot name="content"></slot></div>
    </a-spin>
  </div>
</template>
<script>
export default {
  props: {
    // 标题
    title: {
      type: String,
      default: "",
      required: true,
    },
    // 标题图标
    iconClass: {
      type: String,
      default: "",
    },
    // 高度
    height: {
      type: [Number, String],
      default: 0,
    },
    // 宽度
    width: {
      type: [Number, String],
      default: "100%",
    },
    isNeedBg: {
      type: Boolean,
      default: true,
    },
    contentNeedPadding:{
      type: Boolean,
      default: true,
    },
    isNeedTitleBg: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    callBack(type, param) {
      this.$emit("callBack", type, param);
    },
    setLoading(loading) {
      this.loading = loading;
    },
    refreshData(data) {
      this.$refs.component.refreshData(data);
    },
  },
};
</script>
<style scoped lang="less">
.content-box {
  border-radius: 8px;
  box-sizing: border-box;
  color: #fff;

  .head-div {
    &.titleBg {
      background: linear-gradient(90deg, #0661ae 0%, rgba(5, 97, 175, 0) 100%);
    }
    border-radius: 8px 8px 0 0;
    height: 32px;
    .head-marker {
      background: #bde2ff;
      height: 16px;
      width: 2px;
    }
    .title-icon {
      font-size: 20px;
      margin: 0 6px 0 4px;
    }
  }
  .content {
    height: calc(100% - 32px);
    padding: 0 16px;
  }
}
.content-box-bg {
  background: linear-gradient(
    180deg,
    #1a4a8e 0%,
    rgba(26, 74, 142, 0.97) 0%,
    rgba(14, 64, 133, 0.94) 100%
  );
  border: 1px solid #255dae;
}
</style>
