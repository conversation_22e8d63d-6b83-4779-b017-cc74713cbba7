<template>
  <div class="margin-t-16 flex-column width-100 height-100">
    <DeviceControl
      :deviceInfo="deviceInfo"
      :pa="pa"
      :status="status"
      :isRight="true"
      style="height: 112px; background: #17539f"
      class="radius-8 padding-t-12 padding-l-12"
    />
    <div class="margin-t-16 small-card-bg padding-t-12 radius-8">
      <div class="flex-space-between padding-l-12 padding-r-12">
        <span>测温</span>
        <svg-icon icon-class="camera" class="font-20"></svg-icon>
      </div>
      <div class="flex-space-between height-98">
        <div class="flex-center flex-column width-120">
          <span class="text-blod font-24 color-text-error">0</span>
          <span class="padding-l-16">当前温度（℃）</span>
        </div>
        <div :id="id" class="width-100 height-100"></div>
      </div>
    </div>
  </div>
</template>
<script>
import DeviceControl from "./DeviceControl.vue";
import LineChart from "../mixin/LineChart";
export default {
  name: "LiquidFire",
  components: {
    DeviceControl,
  },
  mixins: [LineChart],
  data() {
    return {
      deviceInfo: {
        title: "喷淋系统",
        viceTitle: "",
        indicator: "当前水压（Pa）",
        isOnline: 1,
      },
      pa: "0",
      status: true,
      id: "liquid-fire",
      xData: [
        "00:00",
        "02:00",
        "04:00",
        "06:00",
        "08:00",
        "10:00",
        "12:00",
        "14:00",
        "16:00",
        "18:00",
        "20:00",
        "22:00",
        "24:00",
      ],
      yData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      legend: [
        {
          name: "测温线缆温度（℃）",
          unit: "cm",
          startColor: "#40AAFF",
          endColor: "rgba(64, 170, 255, 0)",
        },
        {
          name: "摄像头温度（℃）",
          unit: "℃",
          startColor: "#9DC535",
          endColor: "#9DC535",
        },
      ],
    };
  },
  async mounted() {
    await this.getChartData();
    this.$nextTick(() => {
      this.initChart({
        xData: this.xData,
        yData: this.yData,
        legend: this.legend,
        y1Data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      });
      this.setupResizeHandler();
    });
  },
  async getChartData() {
    // ToDO 获取echart 数据
    const res = await getMonitorData({
      deviceId: this.deviceId,
    });
    let result = res.result_data;
    this.xData = result.xData;
    this.yData = result.yData;
  },
};
</script>
<style scoped lang="less">
.small-card-bg {
  background: #17539f;
  height: calc(100% - 156px);
}
.height-98 {
  height: calc(100% - 24px);
  min-height: 124px;
}
.width-120 {
  width: 140px;
}
</style>
