<template>
  <div class="fire-view">
    <div class="font-16 text-bold">{{ title }}</div>
    <div class="flex-gap-16 flex-space-around">
      <div v-for="(item, index) in fireList" :key="item.title + '-' + index" class="flex-start flex-gap-8">
        <div>
          <svg-icon :icon-class="item.icon" class="font-size-36" />
        </div>
        <div>
          <div class="font-size-24 num-font-700" :style="{ color: item.num > 0 ? item.color : '' }">
            {{ item.num }}
          </div>
          <p style="color: #85caff">{{ item.name }}</p>
        </div>
        <div v-if="item.children && item.children.length > 0" class="card-extra margin-b-12">
          <div class="padding-b-4 flex-baseline">
            <div class="padding-r-6">成功</div>
            {{ item.children[0] }}
          </div>
          <div class="flex-baseline">
            <div class="padding-r-8">失败</div>
            {{ item.children[1] }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FireSafetyCard',
  props: {
    fireList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '消防系统'
    }
  }
};
</script>

<style scoped>
.fire-view {
  padding: 16px;
  border-radius: 8px;
  background: #144588;
  color: #fff;
}

.card-extra {
  background: #1b5097;
  border-radius: 4px;
  padding: 4px;
  gap: 4px;
}
</style>
