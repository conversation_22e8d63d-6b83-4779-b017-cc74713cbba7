<template>
  <div class="width-height-100flex-baseline flex-column">
    <div class="flex-space-around width-100 margin-t-16">
      <div
        class="flex-center flex-column"
        v-for="(item, index) in pannelIndicator"
        :key="index"
      >
        <div class="font-size-24 text-bold">{{ item[item.key] }}</div>
        <p>{{ item.name }}</p>
      </div>
      <!-- <div class="flex-center flex-column">
        <div class="font-size-24 text-bold">2</div>
        <p>总线设备</p>
      </div>
      <div class="flex-center flex-column">
        <div class="font-size-24 text-bold">1</div>
        <p>联动公式</p>
      </div> -->
    </div>
    <div class="divide-line"></div>
    <div
      style="flex: 1"
      class="flex-gap-16 margin-t-16 flex-space-around fire-view"
    >
      <img
        src="@/assets/images/holisticSecurity/fire-panel.png"
        alt="fire-panel"
      />
      <div class="flex-gap-8 flex-baseline flex-column">
        <div>
          <div class="margin-b-8">
            <svg-icon icon-class="handle-manual"></svg-icon> 手动模式
          </div>
          <Manual
            :list="manulModeList"
            :value="manulMode"
            key="manulMode"
          ></Manual>
          <!-- <a-radio-group
            v-model="manulMode"
            @change="() => changeEvent(manulMode, 'manulMode')"
          >
            <a-radio value="1">开启</a-radio>
            <a-radio value="0">关闭</a-radio>
          </a-radio-group> -->
        </div>
        <div>
          <div>
            <div class="margin-b-8">
              <svg-icon icon-class="auto-manual"></svg-icon>
              自动模式
            </div>
            <Manual
              :list="autoModeList"
              :value="autoMode"
              key="autoMode"
            ></Manual>
            <!-- <a-radio-group
              v-model="autoMode"
              @change="() => changeEvent(autoMode, 'autoMode')"
            >
              <a-radio value="1">自动</a-radio>
              <a-radio value="0">部分自动</a-radio>
              <a-radio value="2">模拟</a-radio>
            </a-radio-group> -->
          </div>
        </div>
        <div>
          <div>
            <div class="margin-b-8">
              <svg-icon icon-class="bus-shielding"></svg-icon> 总线屏蔽
            </div>
            <Manual :list="busShieldingList" :value="busShielding"></Manual>
            <!-- <a-radio-group
              v-model="busShielding"
              @change="() => changeEvent(busShielding, 'busShielding')"
            >
              <a-radio value="1">有</a-radio>
              <a-radio value="0">无</a-radio>
            </a-radio-group> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getPannelStatus, setPannelStatus } from "@/api/holisticSecurity/fire";
import Manual from "./Manual.vue";
import {
  manulModeList,
  busShieldingList,
  autoModeList,
  pannelIndicator,
} from "../mixin/enum";
export default {
  name: "FirePanel",
  components: { Manual },
  data() {
    return {
      manulMode: "1",
      autoMode: "1",
      busShielding: "1",
      psaInfo: {},
      psaId: "",
      psaName: "",
      manulModeList,
      busShieldingList,
      autoModeList,
      pannelIndicator,
    };
  },
  inject: ["psaInfo"],
  created() {
    this.getPannel();
  },
  methods: {
    getPannel() {
      // getPannelStatus(this.psaInfo.psaId).then((res) => {
      //   if (res.code === 200) {
      //     const resData = res.data;
      //     this.manulMode = resData.manulMode;
      //     this.autoMode = resData.autoMode;
      //     this.busShielding = resData.busShielding;
      //   }
      // });
    },
    async changeEvent(val, type) {
      console.log("changeEvent", type, val);
      await setPannelStatus({
        [type]: val,
      });
      this.$emit("callBack", type, val);
    },
  },
};
</script>
<style scoped lang="less">
.ant-radio-wrapper.ant-radio-wrapper {
  color: #85caff;
}
:deep(.ant-radio-inner) {
  background-color: #144588;
  border-color: #527bb5;
}
</style>
