<template>
  <div class="flex-baseline flex-gap-16">
    <div v-for="item in list" :key="item.id" class="flex-baseline flex-gap-8">
      <div class="circle" :class="{ 'circle-selected': value == item.value }">
        {{ item.title }}
      </div>
      <div class="color-text-main margin-r-12">{{ item.label }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "Manual",
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    value: {
      type: [String, Number],
      default: "",
    },
  },
};
</script>
<style lang="less" scoped>
.circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid #527bb5;
}
.circle-selected {
  background: #4fb0ff;
}
</style>
