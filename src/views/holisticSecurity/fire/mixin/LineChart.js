import echarts from '@/utils/enquireEchart';
import { min } from 'moment';
import { debounce } from 'xe-utils';
import { isEmpty } from '@/utils/erpCommon';

export default {
  data() {
    return {
      myChart: null, // 保存图表实例
      resizeObserver: null // 用于观察容器尺寸变化

    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart({
        xData: this.xData,
        yData: this.yData,
        legend: this.legend
      });
      this.setupResizeHandler();
    });
  },
  methods: {
    resize() {
      const myChart = echarts.init(document.getElementById(this.id));
      myChart.resize();
    },
    initChart({ xData, yData, legend, y1Data, yXais, ...rest }) {
      const chartDom = document.getElementById(this.id);
      if (!chartDom) return;

      // 如果已有实例，先销毁
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(chartDom);
      this.myChart.setOption(
        this.getOption({
          xData,
          yData,
          legend,
          y1Data,
          yXais,
          ...rest
        })
      );
      // 使用 ResizeObserver 监听容器尺寸变化（现代浏览器支持）
      if (typeof ResizeObserver !== 'undefined') {
        this.resizeObserver = new ResizeObserver(
          debounce(() => {
            this.myChart && this.myChart.resize();
          }, 100)
        );
        this.resizeObserver.observe(chartDom);
      }
    },
    setupResizeHandler() {
      // 添加防抖的窗口resize监听
      this.debouncedResize = debounce(() => {
        this.myChart && this.myChart.resize();
      }, 100);
      window.addEventListener('resize', this.debouncedResize);
    },
    getOption({ xData, yData, legend, y1Data, yXais, ...rest }) {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 170, 255, 0)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 170, 255, 0.4)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          className: 'di-chart-tooltip',
          borderColor: 'rgba(189, 226, 255, 0.4)',
          borderWidth: 1,
          // 自定义显示逻辑
          formatter: (params) => {
            let html = `<div class="title font-14">${params[0].axisValue}</div>
                    <div class="content">`;

            params.forEach((item, index) => {
              html += `
              <div class="content-item">
                <div class="flex-start">
                  ${item.marker}
                  <span class="tooltip-item-label">${item.seriesName}：</span>
                </div>
                <span>
                  <span class="color-text-white">${isEmpty(item.value) ? '--' : item.value}</span>
                  <span class="color-text-gray content-unit">${legend[index].unit}</span>
                </span>
              </div>
            `;
            });
            html += `</div>`;
            return html;
          }
        },
        legend: {
          icon: 'roundRect',
          data: legend.map((item) => item.name),
          top: 8,
          bottom: 0,
          itemGap: 15,
          itemWidth: 10,
          itemHeight: 10,
          itemStyle: {
            borderRadius: 2
          },
          textStyle: {
            fontSize: 10,
            lineHeight: 10,
            verticalAlign: 'middle',
            color: '#85CAFF'
          }
        },
        grid: {
          left: '16px',
          right: '16px',
          top: '40px',
          bottom: '8px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: xData,
            axisTick: { show: false },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(97, 161, 213, 0.19)'
              }
            },
            axisLabel: {
              showMinLabel: true,
              showMaxLabel: true,
              color: '#85CAFF'
            }
          }
        ],
        yAxis: legend.slice(0, yXais || 1).map((item, index) => {
          const allData = [...yData, ...(y1Data || [])].filter((v) => v != null && !isNaN(v));
          let max = Math.max(...allData);
          if(!isEmpty(rest?.limit)) max = rest?.limit;
          // 如果数据全部相同（如 min === max），则手动调整范围
          const adjustedMax = min === max ? max + 1 : max;
          const interval = Math.ceil((adjustedMax - 0) / 4);
          return {
            type: 'value',
            axisPointer: { show: false },
            axisLabel: {
              color: '#85CAFF',
              show: true
            },
            name: item.unit,
            nameTextStyle: {
              color: '#85CAFF',
              fontSize: 12,
              padding: [0, 0, 0, 8] // 可调整与轴的距离
            },
            splitLine: {
              show: index === 0, // 通常只显示第一个Y轴的网格线
              lineStyle: {
                color: '#61A1D5'
              }
            },
            min: 0, // 强制设置最小值
            max: adjustedMax, // 强制设置最大值
            interval: interval
          };
        }),
        series: legend.map((item, index) => {
          const seriesData = index === 0 ? yData : y1Data;
          return {
            name: item.name,
            type: 'line',
            data: seriesData || [],
            yAxisIndex: yXais == 2 ? index : 0,
            smooth: true,
            showSymbol: false,
            itemStyle: {
              color: item.startColor
            },
            areaStyle: {
              opacity: 0.2,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: item.startColor
                },
                {
                  offset: 1,
                  color: item.endColor
                }
              ])
            }
          };
        })
      };
    }
  },
  beforeDestroy() {
    // 清理图表实例
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }

    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    // 移除窗口resize监听
    if (this.debouncedResize) {
      window.removeEventListener('resize', this.debouncedResize);
    }
  }
};
