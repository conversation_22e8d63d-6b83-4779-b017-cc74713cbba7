<template>
  <div class="width-height-100 fire-container">
    <div class="left width-height-100">
      <div class="flex-gap-16 flex-baseline width-100">
        <content-box title="液体消防" :isNeedBg="false" :contentNeedPadding="false" ref="liquid">
          <template #content>
            <div v-if="liquidFireList.length" class="margin-t-16 height-100 liquid-fire-content-box">
              <FireControl v-for="(item, index) in liquidFireList" :key="index" :deviceInfo="item" iconClass="liquid-fire" :psaInfo="psaInfo" @refresh="getLiquidFireList" />
            </div>
            <div v-else class="no-data width-100 margin-t-28"></div>
          </template>
        </content-box>
      </div>
      <div class="flex-gap-16 flex-baseline width-100">
        <content-box title="气体消防" :isNeedBg="false" :contentNeedPadding="false" ref="gas">
          <template #content>
            <div v-if="gasFireList.length" class="margin-t-16 height-100 liquid-fire-content-box">
              <FireControl v-for="(item, index) in gasFireList" :key="index" :deviceInfo="item" iconClass="gas-fire" :psaInfo="psaInfo" @refresh="getGasFireList" />
            </div>
            <div v-else class="no-data width-100 margin-t-28"></div>
          </template>
        </content-box>
      </div>
      <div class="flex-gap-16 flex-baseline width-100">
        <content-box title="火情预警" ref="fireWarning" style="height: 300px">
          <template #content>
            <ImportantMonitorVideo
              class="padding-b-12"
              :isInline="true"
              :data-params="{ areaType: 1, deviceSubType: 50, isKeyArea: null }"
              @setLoading="(loading) => setLoading('fireWarning', loading)"
              :videoDisplayNum="5"
            />
          </template>
          <template #right>
            <div class="cursor-pointer color-text-main flex-center margin-r-12" @click="goVideoPage">
              <span class="margin-r-2">更多</span>
              <svg-icon icon-class="more" class="font-10"/>
            </div>
          </template>
        </content-box>
      </div>
    </div>
    <div class="right height-100 flex-gap-16 flex-baseline flex-column">
      <content-box title="消防主机" iconClass="health-appearance" class="height-100" :contentNeedPadding="false">
        <template #content>
          <div class="flex-center flex-gap-16 margin-r-36">
            <svg-icon icon-class="fire-main-device" class="margin-r-12" style="font-size: 90px; height: 160px" />
            <div class="fire-main-info">
              <div class="item">
                <span class="font-14 color-text-white">厂家 </span>
                <span class="font-14 color-text-main">海湾安全技术有限公司</span>
              </div>
              <div class="item">
                <span class="font-14 color-text-white">型号 </span>
                <span class="font-14 color-text-main">JB-QB-GST200H-S</span>
              </div>
            </div>
          </div>
          <content-box title="告警列表" :isNeedBg="false" :isNeedTitleBg="false" iconClass="alarm-list" style="height: calc(100% - 160px)" ref="alarmList">
            <template #content>
              <AlarmList @callBack="selectAlarm" @setLoading="(loading) => setLoading('alarmList', loading)" :dataParams="{ areaType: null, deviceType: 62, noFile: 1 }" />
            </template>
          </content-box>
        </template>
      </content-box>
    </div>
    <drawer-view ref="drawerView"/>
  </div>
</template>
<script>
import ImportantMonitorVideo from '@/views/2dMap/indicators/ImportantMonitorVideo.vue';
import AlarmList from './modules/AlarmList.vue';
import ContentBox from './modules/ContentBox.vue';
import getPsaInfo from './mixin/getPsaInfo';
import FireControl from './modules/FireControl.vue';
import { getModelData } from '@/api/2dMap/psOverview';
import { DEVICE_SUBTYPE_ENUM, DEVICE_TYPE_ENUM } from '@/utils/util';
import { permissionRouterGo } from '@/utils';

export default {
  components: {
    AlarmList,
    ContentBox,
    ImportantMonitorVideo,
    FireControl
  },
  mixins: [getPsaInfo],
  data() {
    return {
      contentHeight: 268,
      psaInfo: null,
      // 液体消防
      liquidFireList: [],
      //   气体消防
      gasFireList: []
    };
  },
  created() {
    this.psaInfo = this.$ls.get('PSA_INFO');
  },
  mounted() {
    this.getLiquidFireList();
    this.getGasFireList();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedResize);
    this.debouncedResize = null;
  },
  methods: {
    async getLiquidFireList(needRefresh = true) {
      this.$refs.liquid.setLoading(needRefresh);
      const res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: 'psDeviceList',
        manageDeviceType: DEVICE_TYPE_ENUM.FIRE_SYSTEM,
        deviceSubType: DEVICE_SUBTYPE_ENUM.LIQUID_FIRE
      });
      this.liquidFireList = res?.result_data?.dataResult?.psDeviceList || [];
      this.$refs.liquid.setLoading(false);
    },
    async getGasFireList(needRefresh = true) {
      this.$refs.gas.setLoading(needRefresh);
      const res = await getModelData({
        psId: this.psaInfo.psId,
        modelName: 'psDeviceList',
        manageDeviceType: DEVICE_TYPE_ENUM.FIRE_SYSTEM,
        deviceSubType: DEVICE_SUBTYPE_ENUM.GAS_FIRE
      });
      this.gasFireList = res?.result_data?.dataResult?.psDeviceList || [];
      this.$refs.gas.setLoading(false);
    },
    setLoading(ref, loading) {
      this.$nextTick(() => {
        this.$refs[ref].setLoading(loading);
      });
    },
    goVideoPage(){
      permissionRouterGo("/holisticSecurity/video", this);
    },
    selectAlarm(params){
      if(params.type === 'selectAlarm'){
        if (['83', '85'].includes(params.params.reamrk)) return;
        this.$refs.drawerView.init('3', params.params, '/health/modules/DetailForm');
      }
    }
  }
};
</script>
<style lang="less" scoped>
.fire-container {
  display: flex;
  gap: 16px;

  .left {
    width: calc(100% - 400px);
    height: 100%;
    display: flex;
    gap: 16px;
    flex-direction: column;
    overflow: auto;

    :deep(.monitor-item) {
      width: 250px;
      height: 200px;

      .live-area {
        width: 100%;
        height: 100%;
      }
    }
  }

  .right {
    width: 380px;
  }

  .fire-height-1-3 {
    height: calc((100% - 32px) / 3);
  }

  .liquid-fire-content-box {
    display: grid;
    grid-template-columns: repeat(3, minmax(350px, 1fr));
    gap: 16px;
  }

  .fire-main-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;

    .item {
      display: flex;
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }
  }
}
</style>
