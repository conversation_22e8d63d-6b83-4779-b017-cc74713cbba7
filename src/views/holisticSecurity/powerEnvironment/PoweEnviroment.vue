<!--动环管理-->
<template>
  <div class="width-height-100 power-enviroment">
    <div class="left flex-gap-16 flex-column flex-start">
      <!--      <div class="flex-gap-16 flex-space-around fire-top width-100">-->
      <!--        <Card :fireList="monitorList" title="监测点统计" />-->
      <!--        <Card :fireList="realAlarm" style="width: 648px" title="实时告警" />-->
      <!--        <Card :fireList="linkageEvent" title="联动事件" />-->
      <!--      </div>-->
      <div class="width-100 height-32">
        <a-radio-group v-model="boxId" @change="goToScrollBox" style="height: 30px">
          <a-radio-button value="tempMonitorBox">温湿度监测</a-radio-button>
          <a-radio-button value="wellWaterBox">水浸监测</a-radio-button>
          <a-radio-button value="waterLevelBox">水位监测</a-radio-button>
        </a-radio-group>
      </div>
      <div class="width-100 scroll-container">
        <div style="width: calc(100% + 6px)">
          <div class="flex-gap-16 flex-baseline width-100" id="tempMonitorBox">
            <content-box title="温湿度监测" :isNeedBg="false" ref="tempMonitorBox" :contentNeedPadding="false">
              <template #content>
                <div v-if="tempList.length" class="margin-t-16 width-height-100 fire-middle-content">
                  <TempMonitor v-for="item in tempList" :key="item.psKey + Date.now()" :deviceInfo="item" :style="{ height: contentHeight + 'px' }" style="min-height: 350px"></TempMonitor>
                </div>
                <div v-else class="no-data width-100 margin-t-28"></div>
              </template>
            </content-box>
          </div>
          <div class="flex-gap-16 flex-baseline fire-middle width-100 margin-t-16" id="wellWaterBox">
            <content-box title="水浸监测" :isNeedBg="false" ref="wellWaterBox" :contentNeedPadding="false">
              <template #content>
                <div v-if="waterList.length" class="margin-t-16 height-100 well-water-content-box">
                  <WellWaterCard v-for="(item, index) in waterList" :key="index" :wellWaterInfo="item" />
                </div>
                <div v-else class="no-data width-100 margin-t-28"></div>
              </template>
            </content-box>
          </div>
          <div class="flex-gap-16 flex-baseline fire-middle width-100 margin-t-16" id="waterLevelBox">
            <content-box title="水位监测" :isNeedBg="false" ref="waterLevelBox" :contentNeedPadding="false">
              <template #content>
                <div v-if="waterLevelList.length" class="margin-t-16 height-100 fire-middle-content">
                  <WaterMonitor v-for="(item, index) in waterLevelList" :key="item.psKey + Date.now()" :style="{ height: contentHeight + 'px' }" style="min-height: 269px" :deviceInfo="item" />
                </div>
                <div v-else class="no-data width-100 margin-t-28"></div>
              </template>
            </content-box>
          </div>
        </div>
      </div>
    </div>
    <div class="right height-100 flex-gap-16 flex-baseline flex-column">
      <content-box title="空调监控" iconClass="air-monitor" class="air-control-box height-100" ref="airControlBox">
        <template #content>
          <template v-if="airDeviceList.length">
            <AirControl2
              v-for="(item, index) in airDeviceList"
              :deviceInfo="item"
              :key="item.psKey"
              @updateDeviceInfo="updateDeviceInfo($event, index)"
              @setLoading="$refs.airControlBox.setLoading($event)"
            />
          </template>
          <div v-else class="no-data width-100 margin-t-28"></div>
        </template>
      </content-box>
      <!--      <content-box title="联动事件" iconClass="linkage-event" class="fire-bottom">-->
      <!--        <template #content>-->
      <!--          <AlarmList />-->
      <!--        </template>-->
      <!--      </content-box>-->
    </div>
  </div>
</template>
<script>
import AirControl2 from './modules/AirControl2.vue';
import { realAlarm, monitorList, linkageEvent } from './data.js';
import ContentBox from '../fire/modules/ContentBox.vue';
import WellWaterCard from './modules/WellWaterCard.vue';
import TempMonitor from './modules/TempMonitor.vue';
import { debounce } from 'xe-utils';
import getPsaInfo from '../fire/mixin/getPsaInfo.js';
import { getModelData as getModelDataApi } from '@/api/2dMap/psOverview';
import { DEVICE_SUBTYPE_ENUM, DEVICE_TYPE_ENUM } from '@/utils/util';
import WaterMonitor from './modules/WaterMonitor.vue';

export default {
  name: 'PowerEnvironment',
  components: {
    ContentBox,
    AirControl2,
    TempMonitor,
    WellWaterCard,
    WaterMonitor
  },
  mixins: [getPsaInfo],
  data() {
    return {
      realAlarm,
      monitorList,
      boxId: null,
      airDeviceList: [],
      linkageEvent,
      waterList: [],
      tempList: [],
      debouncedResize: null,
      contentHeight: 249,
      pasInfo: null,
      timer: null,
      waterLevelList: [],
      airTimer: null,
    };
  },
  mounted() {
    this.setupResizeHandler();
    this.init();
    this.refreshTimerData();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedResize);
    this.debouncedResize = null;
    clearInterval(this.timer);
    this.timer = null;
    clearInterval(this.airTimer);
    this.airTimer = null;
  },
  methods: {
    init() {
      this.getWellWaterList();
      this.getAirControlList();
      this.getTempMonitorList();
      this.getWaterLevelList();
    },
    refreshTimerData() {
      this.timer = setInterval(() => {
        this.getWellWaterList(false);
      }, 5 * 1000);
      this.airTimer = setInterval(() => {
        this.getTempMonitorList(false);
        this.getWaterLevelList(false);
        this.getAirControlList(false);
      }, 2 * 60 * 1000);
    },
    updateDeviceInfo(newInfo, index) {
      this.$set(this.airDeviceList, index, newInfo);
    },
    setupResizeHandler() {
      // 添加防抖的窗口resize监听
      this.$nextTick(() => {
        this.contentHeight = (document.getElementsByClassName('main')[0].clientHeight - 300) / 2;
      });
      this.debouncedResize = debounce(() => {
        this.contentHeight = (document.getElementsByClassName('main')[0].clientHeight - 300) / 2;
      }, 100);
      window.addEventListener('resize', this.debouncedResize);
    },
    // 水侵
    async getWellWaterList(needLoading = true) {
      this.$refs.wellWaterBox.setLoading(needLoading);
      const res = await getModelDataApi({
        psId: this.psaInfo.psId,
        modelName: 'psDeviceList',
        manageDeviceType: DEVICE_TYPE_ENUM.WELL_WATER,
        deviceSubType: DEVICE_SUBTYPE_ENUM.WATER_INFLUX
      });
      this.$refs.wellWaterBox.setLoading(false);
      this.waterList = res.result_data.dataResult?.psDeviceList || [];
    },
    // 温湿度
    async getTempMonitorList(needLoading = true) {
      this.$refs.tempMonitorBox.setLoading(needLoading);
      let res = await getModelDataApi({
        psaId: '1',
        psId: this.psaInfo.psId,
        modelName: 'psDeviceList',
        manageDeviceType: DEVICE_TYPE_ENUM.TEMPERATURE_HUMIDITY,
        deviceSubType: DEVICE_SUBTYPE_ENUM.TEMPERATURE_HUMIDITY_SUBTYPE
      });
      this.tempList = res.result_data.dataResult?.psDeviceList || [];
      this.$refs.tempMonitorBox.setLoading(false);
    },
    // 空调
    async getAirControlList(needLoading = true) {
      this.$refs.airControlBox.setLoading(needLoading);
      const res = await getModelDataApi({
        psId: this.psaInfo.psId,
        modelName: 'psDeviceList',
        manageDeviceType: DEVICE_TYPE_ENUM.AIR_CONDITIONING
      });
      this.airDeviceList = res.result_data.dataResult?.psDeviceList || [];
      this.$refs.airControlBox.setLoading(false);
    },
    // 水位
    async getWaterLevelList(needLoading = true) {
      this.$refs.waterLevelBox.setLoading(needLoading);
      const res = await getModelDataApi({
        psId: this.psaInfo.psId,
        modelName: 'psDeviceList',
        manageDeviceType: DEVICE_TYPE_ENUM.WELL_WATER,
        deviceSubType: DEVICE_SUBTYPE_ENUM.WATER_POOL
      });
      this.waterLevelList = res.result_data.dataResult?.psDeviceList || [];
      this.$refs.waterLevelBox.setLoading(false);
    },
    // 点击滚动到相应容器
    goToScrollBox() {
      const boxDom = document.getElementById(this.boxId);
      // 滚动到相应boxDom容器
      boxDom?.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start'
      });
    }
  }
};
</script>
<style lang="less" scoped>
.fire-view {
  width: calc((100% - 600px) / 2);
  color: #fff;
  height: 100%;
}

.fire-top {
  height: 115px;
}

.fire-middle-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  grid-auto-columns: auto;
}

.fire-bottom {
  height: 268px;
}

.left {
  //flex: 1;
  width: calc(100% - 400px);
  height: 100%;
}

.right {
  width: 380px;
}

.power-enviroment {
  display: flex;
  gap: 16px;
}

.air-control-box :deep(.content.content) {
  overflow-y: auto;
}

.air-control {
  width: 100%;
  height: calc((100% - 36px) / 4);
  min-height: 130px;
  margin-top: 8px;
}

.well-water-content-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  overflow: auto;
  gap: 16px;
}

.scroll-container {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  scrollbar-gutter: stable;
}

.scroll-container:hover {
  overflow-y: auto;
  scrollbar-gutter: stable;
}
</style>
