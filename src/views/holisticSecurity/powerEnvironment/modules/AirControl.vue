<template>
  <div class="air-control">
    <div class="air-control-title padding-8 flex-space-between">
      <div>{{ getLabel(deviceInfo.deviceName, null) }}</div>
      <div>
        <span class="device-status margin-r-4" v-if="deviceInfo.onlineStatus == 1"></span>
        <svg-icon icon-class="offline" class="margin-r-4" v-if="deviceInfo.onlineStatus == 0" />
        <span :class="{ 'color-text-second': deviceInfo.onlineStatus == 0, 'color-text-main': deviceInfo.onlineStatus == 1 }">{{ onlineStatusName }}</span>
      </div>
    </div>
    <div class="air-control-content flex-center">
      <img src="@/assets/images/holisticSecurity/air.png" alt="" class="margin-l-16 margin-r-16" style="width: 65px" />
      <div class="flex-column flex-center" style="width: 96px">
        <div class="font-16 color-text-main">
          {{ getAirStatus() }}
        </div>
        <div class="flex-space-around width-100">
          <svg-icon icon-class="min" class="air-svg" @click="changeEvent('min')"></svg-icon>
          <div style="font-size: 18px;font-weight: bold;">{{ getLabel(extend[TEMP_POINT], null) }}</div>
          <svg-icon icon-class="plus" class="air-svg" @click="changeEvent('plus')"></svg-icon>
        </div>
      </div>
      <div class="flex-center flex-warp flex-1">
        <a-button
          v-for="item in airBtnList"
          :key="item.icon"
          type="primary"
          class="air-btn margin-r-8 margin-b-8"
          :class="{ active: extend[STATUS_POINT] === item.status }"
          @click="changeOperationBtn(item.status)"
        >
          <svg-icon :icon-class="item.icon"></svg-icon>
          <div class="btn-content">{{ item.name }}</div>
        </a-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    deviceId: {
      type: String,
      default: ''
    },
    deviceInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      isOnline: true,
      airBtnList: [
        { name: '关机', icon: 'air-close', status: 0 },
        { name: '制冷', icon: 'cool', status: 1 },
        { name: '干燥', icon: 'dry', status: 3 },
        { name: '制热', icon: 'hot', status: 2 }
      ],
      TEMP_POINT: 'p640002',
      STATUS_POINT: 'p640001'
    };
  },

  computed: {
    onlineStatusName() {
      if (this.$isEmpty(this.deviceInfo.onlineStatus)) return '--';
      return this.deviceInfo.onlineStatus == 1 ? '在线' : '离线';
    },
    extend() {
      return JSON.parse(this.deviceInfo.extend || '{}');
    }
  },
  methods: {
    async changeEvent(type) {
      const extend = JSON.parse(this.deviceInfo.extend || '{}');
      if (type == 'min') {
        extend[this.TEMP_POINT] = (extend[this.TEMP_POINT] * 1 - 0.5).toFixed(1);
      }
      if (type == 'plus') {
        extend[this.TEMP_POINT] = (extend[this.TEMP_POINT] * 1 + 0.5).toFixed(1);
      }
      //  TODO: 空调控制
      const newObj = Object.assign({}, this.deviceInfo, { extend: JSON.stringify(extend) });
      this.$emit('updateDeviceInfo', newObj);
    },
    async changeOperationBtn(val) {},
    getAirStatus() {
      const statusItem = this.airBtnList.find((item) => item.status == this.extend[this.STATUS_POINT]);
      return statusItem?.name || '--';
    }
  }
};
</script>
<style lang="less" scoped>
.air-control {
  border: 1px solid #0077d1;
}

.air-control-content {
  height: calc(100% - 32px);

  svg {
    cursor: pointer;
  }
}

.air-svg {
  width: 20px;
  height: 20px;
}
.air-svg:hover {
  border-radius: 1px;
  background: rgba(97, 161, 213, 0.22);
}

.air-btn {
  padding: 2px 8px;
  gap: 4px;
  color: white;
  background: #006ec2;
  border: none;

  &.active,
  &:hover {
    background: linear-gradient(180deg, #0092ff 0%, #007cd9 100%);
  }
}

.flex-warp {
  flex-wrap: wrap;
}

.btn-content {
  display: inline-block;
  padding-left: 4px;
}
</style>
