<template>
  <div class="flex-baseline flex-column height-100 device-module">
    <div class="title margin-b-12 flex-space-between">
      <div>{{ deviceInfo.deviceName }}</div>
      <div>
        <template v-if="deviceInfo.onlineStatus == 0">
          <svg-icon icon-class="offline" class="margin-r-4 font-16" />
          <span class="color-text-second font-12">离线</span>
        </template>
        <template v-else>
          <svg-icon :icon-class="statusClass" />
          <span :class="statusClass">{{ areaStatus }}</span>
        </template>
      </div>
    </div>
    <template v-if="deviceInfo.onlineStatus != 0">
      <div class="flex-center width-100 margin-b-12">
        <div class="flex-center flex-column">
          <div class="text-blod font-24">{{ tempValue || '--' }}</div>
          <div>当前温度（℃）</div>
        </div>
        <div class="flex-center flex-column">
          <div class="text-blod font-24">{{ humidityValue || '--' }}</div>
          <div>当前湿度（%RH）</div>
        </div>
      </div>
      <div class="flex-start">
        <div class="before margin-r-16"></div>
        <div class="font-12">当日温湿度趋势</div>
      </div>
      <!--      <div :id="id" class="width-100 flex-1 height-100"></div>-->
      <!--      <div :id="id" class="width-100 flex-1 height-100"></div>-->
      <TempChart :id="id + 'temp'" :chartData="tempData" :legend="legend" />
      <TempChart :id="id + 'humidity'" :chartData="humidityData" :legend="legend2" />
    </template>
    <div v-else class="flex-center width-100 height-100">
      <img src="@/assets/images/holisticSecurity/device-offline.png" alt="" />
    </div>
  </div>
</template>
<script>
import { getModelData } from '@/api/2dMap/psOverview';
import moment from 'moment';
import TempChart from '@/views/holisticSecurity/powerEnvironment/modules/TempChart.vue';

export default {
  name: 'DeviceModule',
  components: { TempChart },
  props: {
    deviceInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      legend: [
        {
          name: '温度',
          unit: '℃',
          startColor: '#40AAFF',
          endColor: 'rgba(64, 170, 255, 0)'
        }
      ],
      legend2: [
        {
          name: '湿度',
          unit: '%RH',
          startColor: '#9DC535',
          endColor: 'rgba(157, 197, 53, 0)'
        }
      ],
      tempPoint: 'p1000002',
      humidityPoint: 'p1000003',
      tempValue: '',
      humidityValue: '',
      id: 'chart-1',
      tempData: {},
      humidityData: {}
    };
  },
  computed: {
    statusClass() {
      const { alarmStatus } = this.deviceInfo ?? {};
      return alarmStatus == '1' ? 'powerAlarm' : alarmStatus == '0' ? 'normal' : '';
    },
    areaStatus() {
      const { alarmStatus } = this.deviceInfo ?? {};
      return alarmStatus == '1' ? '异常' : alarmStatus == '0' ? '正常' : '--';
    }
  },
  created() {
    this.id = this.deviceInfo.uuid;
    this.getData();
  },

  methods: {
    async getData() {
      const nowDate = moment().format('YYYY-MM-DD');
      let res = await getModelData({
        modelName: 'ckRealTimeData',
        psKey: this.deviceInfo.psKey,
        deviceType: this.deviceInfo.deviceType,
        recordDate: nowDate,
        points: `${this.tempPoint},${this.humidityPoint}`
      });
      const { ckRealTimeData } = res.result_data.dataResult || {};
      // 温度和湿度取当前时间在15min内最新的点，比如当前时间是21：15， 那就取21:00-21:15这个时间段内最新的点，即21:15这个点
      const now = moment();
      const startTime = moment().subtract(15, 'minutes');
      const endTime = now;
      const allTempData = ckRealTimeData.filter((item) => item?.[this.tempPoint] != null);
      const allHumidityData = ckRealTimeData.filter((item) => item?.[this.humidityPoint] != null);
      const tempData = allTempData.filter((item) => moment(item.time).isBetween(startTime, endTime, null, '[)')).sort((a, b) => moment(b.time) - moment(a.time))?.[0] || {};
      const humidityData = allHumidityData.filter((item) => moment(item.time).isBetween(startTime, endTime, null, '[)')).sort((a, b) => moment(b.time) - moment(a.time))?.[0] || {};
      this.tempValue = this.$isEmpty(tempData) ? '--' : tempData?.[this.tempPoint]?.toFixed(1);
      this.humidityValue = this.$isEmpty(humidityData) ? '--' : humidityData?.[this.humidityPoint]?.toFixed(1);
      const x1Data = ckRealTimeData.filter((item) => item?.[this.tempPoint] != null).map((item) => moment(item.time).format('HH:mm'));
      const x2Data = ckRealTimeData.filter((item) => item?.[this.humidityPoint] != null).map((item) => moment(item.time).format('HH:mm'));
      const y1Data = ckRealTimeData.filter((item) => item?.[this.tempPoint] != null).map((item) => item?.[this.tempPoint]?.toFixed(1));
      const y2Data = ckRealTimeData.filter((item) => item?.[this.humidityPoint] != null).map((item) => item?.[this.humidityPoint]?.toFixed(1));
      this.tempData = { xData: x1Data, yData: y1Data };
      this.humidityData = { xData: x2Data, yData: y2Data };
    }
  }
};
</script>
<style scoped lang="less">
@import url('../mixin/monitor.less');
</style>
