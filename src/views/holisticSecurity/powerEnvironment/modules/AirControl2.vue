<template>
  <div class="air-control">
    <div class="air-control-title padding-8 flex-space-between">
      <div>{{ getLabel(deviceInfo.deviceName, null) }}</div>
      <div>
        <svg-icon icon-class="offline" class="margin-r-4" v-if="$isEmpty(extend)" />
        <span class="device-status margin-r-4" v-else></span>
        <span :class="{ 'color-text-second': $isEmpty(extend), 'color-text-main': !$isEmpty(extend) }">{{ $isEmpty(extend) ?  '离线' : '在线' }}</span>
      </div>
    </div>
    <div class="air-control-content flex-center">
      <img src="@/assets/images/holisticSecurity/air.png" alt="" class="margin-l-16 margin-r-16" style="width: 65px" />
      <div class="flex-column flex-center flex-1" style="align-items: flex-start">
        <div class="flex-start font-14 color-text-main flex-gap-8">
          柜内温度 <span class="margin-l-4 color-text-white">{{ extend[TEMP_POINT]?.toFixed(1) || '--' }}℃</span>
        </div>
        <div class="flex-start font-14 color-text-main flex-gap-8">
          柜内湿度 <span class="margin-l-4 color-text-white">{{ extend[HUMIDITY_POINT]?.toFixed(1) || '--' }}%RH</span>
        </div>
        <div class="flex-start font-14 color-text-main flex-gap-8">
          空调状态 <span class="margin-l-4 font-16 color-text-primary">{{ getAirStatus }}</span>
        </div>
      </div>
      <div class="flex-center flex-warp flex-1 flex-column">
        <!--        <a-button-->
        <!--          v-for="(item, index) in airBtnList"-->
        <!--          :key="item.status + '-' + index"-->
        <!--          type="primary"-->
        <!--          class="air-btn margin-r-8 margin-b-8"-->
        <!--          :class="{ active: extend[STATUS_POINT] === item.status }"-->
        <!--          @click="changeOperationBtn(item.status)"-->
        <!--          size="small"-->
        <!--        >-->
        <!--          <svg-icon :icon-class="item.icon"></svg-icon>-->
        <!--          <div class="btn-content">{{ item.name }}</div>-->
        <!--        </a-button>-->
        <a-switch :checked="airStatus" @change="changeOperationBtn" :disabled="disabled" checked-children="开机" un-checked-children="关机" />
      </div>
    </div>
  </div>
</template>
<script>
import { operateAirConditionerApi } from '@/api/health/healthapi';

export default {
  props: {
    deviceId: {
      type: String,
      default: ''
    },
    deviceInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      isOnline: true,
      airBtnList: [
        { name: '关机', icon: 'air-close', status: 0 },
        { name: '启动', icon: 'air-close', status: 1 }
        // { name: '制冷', icon: 'cool', status: 1 },
        // { name: '干燥', icon: 'dry', status: 3 },
        // { name: '制热', icon: 'hot', status: 2 }
      ],
      TEMP_POINT: 'p640002',
      STATUS_POINT: 'p640001',
      HUMIDITY_POINT: 'p640003'
    };
  },

  computed: {
    extend() {
      const extend = JSON.parse(this.deviceInfo.extend || '{}');
      if (Math.abs(Date.now() - extend?.time) > 10 * 60 * 1000) return {};
      return extend;
    },
    // 0关机 1运行 2制热 3制冷
    getAirStatus() {
      const statusMap = { 0: '关机', 1: '运行', 2: '制热', 3: '制冷' };
      const status = this.extend[this.STATUS_POINT];
      return statusMap?.[status] || '--';
    },
    airStatus() {
      return this.extend[this.STATUS_POINT] == 1;
    },
    disabled() {
      return this.$isEmpty(this.extend);
    }
  },
  methods: {
    async changeEvent(type) {
      const extend = JSON.parse(this.deviceInfo.extend || '{}');
      if (type == 'min') {
        extend[this.TEMP_POINT] = (extend[this.TEMP_POINT] * 1 - 0.5).toFixed(1);
      }
      if (type == 'plus') {
        extend[this.TEMP_POINT] = (extend[this.TEMP_POINT] * 1 + 0.5).toFixed(1);
      }
      //  TODO: 空调控制
      const newObj = Object.assign({}, this.deviceInfo, { extend: JSON.stringify(extend) });
      this.$emit('updateDeviceInfo', newObj);
    },

    async changeOperationBtn(val) {
      this.$emit('setLoading', true);
      const extend = JSON.parse(this.deviceInfo.extend || '{}');
      extend[this.STATUS_POINT] = val ? 1 : 0;
      await operateAirConditionerApi({
        psKey: this.deviceInfo.psKey,
        controlType: val ? 1 : 0
      }).catch(() => {
        this.$message.success('操作失败');
        extend[this.STATUS_POINT] = val ? 0 : 1;
      });
      this.$emit('setLoading', false);
      this.$message.success('操作成功');
      const newObj = Object.assign({}, this.deviceInfo, { extend: JSON.stringify(extend) });
      this.$emit('updateDeviceInfo', newObj);
    }
  }
};
</script>
<style lang="less" scoped>
.air-control {
  border: 1px solid #0077d1;
}

.air-control-content {
  height: calc(100% - 32px);

  svg {
    cursor: pointer;
  }
}

.air-svg {
  width: 20px;
  height: 20px;
}

.air-svg:hover {
  border-radius: 1px;
  background: rgba(97, 161, 213, 0.22);
}

.air-btn {
  padding: 2px 8px;
  gap: 4px;
  color: white;
  background: #006ec2;
  border: none;

  &.active,
  &:hover {
    background: linear-gradient(180deg, #0092ff 0%, #007cd9 100%);
  }
}

.flex-warp {
  flex-wrap: wrap;
}

.btn-content {
  display: inline-block;
  padding-left: 4px;
}
</style>
