<template>
  <div :id="id" class="width-100 flex-1 height-100"></div>
</template>
<script>
import LineChart from '../../fire/mixin/LineChart';

export default {
  name: 'Temp<PERSON><PERSON>',
  mixins: [LineChart],
  props: {
    id: {
      type: String,
      default: ''
    },
    legend: {
      type: Array,
      default: () => []
    },
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      xData: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00'],
      yData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    };
  },
  watch: {
    chartData: {
      handler(val, oldVal) {
        this.initChart({
          xData: val.xData,
          yData: val.yData,
          legend: this.legend,
        });
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart({
        xData: this.xData,
        yData: this.yData,
        legend: this.legend
      });
      this.setupResizeHandler();
    });
  }
};
</script>
