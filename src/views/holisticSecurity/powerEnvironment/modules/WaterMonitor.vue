<template>
  <div class="flex-baseline flex-column height-100 device-module">
    <div class="title margin-b-12 flex-space-between">
      <div>{{ deviceInfo.deviceName }}</div>
      <div>
        <!--        <svg-icon-->
        <!--          class="margin-r-4 font-16"-->
        <!--          :icon-class="getStatusName(deviceInfo.status, true)"-->
        <!--        ></svg-icon>-->
        <!--        <span :class="getStatusName(deviceInfo.status, true)">-->
        <!--          {{ getStatusName(deviceInfo.status) }}</span-->
        <!--        >-->
      </div>
    </div>
    <div class="flex-center flex-column width-100 margin-b-12">
      <div class="text-blod font-24">{{ waterValue || '--' }}</div>
      <div>当前水位（m）</div>
    </div>
    <div class="flex-space-between width-100">
      <div class="flex-start">
        <div class="before margin-r-8"></div>
        <div>当日水位趋势</div>
      </div>
      <div class="padding-r-12">限值：{{ extend.limit }}m</div>
    </div>
    <div :id="id" class="width-100 flex-1 height-100"></div>
  </div>
</template>
<script>
import LineChart from '../../fire/mixin/LineChart';
import getDeviceStatus from '../mixin/getDeviceStatus';
import moment from 'moment/moment';
import { getModelData } from '@/api/2dMap/psOverview';

export default {
  name: 'WaterMonitor',
  mixins: [LineChart, getDeviceStatus],
  props: {
    deviceInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      xData: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00'],
      yData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      legend: [
        {
          name: '水位',
          unit: 'm',
          startColor: '#40AAFF',
          endColor: 'rgba(64, 170, 255, 0)'
        }
      ],
      id: 'chart-1',
      waterPoint: 'p630001',
      waterValue: ''
    };
  },
  created() {
    this.id = this.deviceInfo.psKey;
    this.getData();
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart({
        xData: this.xData,
        yData: this.yData,
        legend: this.legend,
        startColor: this.startColor,
        endColor: this.endColor
      });
      this.setupResizeHandler();
    });
  },
  computed:{
    extend() {
      return JSON.parse(this.deviceInfo.extend || '{}');
    }
  },
  methods: {
    async getData() {
      const nowDate = moment().format('YYYY-MM-DD');
      let res = await getModelData({
        modelName: 'ckRealTimeData',
        psKey: this.deviceInfo.psKey,
        deviceType: this.deviceInfo.deviceType,
        recordDate: nowDate,
        points: this.waterPoint
      });
      const { ckRealTimeData } = res.result_data.dataResult || {};
      // 温度和湿度取当前时间在15min内最新的点，比如当前时间是21：15， 那就取21:00-21:15这个时间段内最新的点，即21:15这个点
      const now = moment();
      const startTime = moment().subtract(15, 'minutes');
      const endTime = now;
      const tempData = ckRealTimeData.filter((item) => moment(item.time).isBetween(startTime, endTime, null, '[)')).sort((a, b) => moment(b.time) - moment(a.time))?.[0] || {};
      this.waterValue = this.$isEmpty(tempData?.[this.waterPoint]) ?  '--' : tempData?.[this.waterPoint].toFixed(2);
      const xData = ckRealTimeData.filter((item) => item?.[this.waterPoint] != null).map((item) => moment(item.time).format('HH:mm'));
      const y1Data = ckRealTimeData.filter((item) => item?.[this.waterPoint] != null).map((item) => item?.[this.waterPoint]?.toFixed(2));
      this.xData = xData;
      this.yData = y1Data;
      this.initChart({
        xData: this.xData,
        yData: this.yData,
        legend: this.legend,
        limit: this.extend.limit
      });
    }
  }
};
</script>
<style scoped lang="less">
@import url('../mixin/monitor.less');
</style>
