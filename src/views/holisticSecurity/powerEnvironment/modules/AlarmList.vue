<!-- 实时监测 告警列表 指标卡 -->
<template>
  <div class="width-100 height-100 padding-t-16 dark-scroll">
    <div class="table-title width-100">
      <div class="title-item" :style="{ width: item.width }" v-for="(item, index) in columns" :key="index">
        <span class="color-text-second font-14">{{ item.label }}</span>
      </div>
    </div>
    <div class="table-content width-100" :style="{ height: `calc( 100% - 20px )` }">
      <div class="width-100 flex-center" v-if="filterList.length == 0">
        <div class="no-data margin-t-12"></div>
      </div>

      <div class="content-item cursor-pointer" v-for="(item, index) in filterList" :key="index">
        <div v-for="(el, elIndex) in columns" :title="item[el.key]" :style="{ width: el.width }" class="flex-start" :key="elIndex">
          <p class="color-text-white font-14 margin-b-0 text-ellipsis num-font-500" :title="item[el.key]">
            {{ item[el.key] }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
export default {
  name: 'AlarmList',
  props: {
    filterList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      columns: [
        {
          label: '事件类型',
          key: 'deviceName',
          width: '100px'
        },
        {
          label: '事件源',
          key: 'location',
          width: '96px'
        },
        {
          label: '事件等级',
          key: 'alarmName',
          width: '80px'
        },
        {
          label: '发生时间',
          key: 'alarmTime',
          width: '120px'
        }
      ]
    };
  },
  created() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.radio-item {
  width: 20%;
  text-align: center;
  padding: 0;
}

.alarm-circle-marker {
  width: 10px;
  height: 10px;
  border-radius: 10px;
}

.table-title {
  display: flex;
  justify-content: space-between;

  .title-item {
    text-align: left;
  }
}

.table-content {
  overflow-y: scroll;

  .content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    text-align: left;
    font-size: 14px;
    line-height: 16px;
  }
}

.content-item:hover {
  background: linear-gradient(180deg, rgba(64, 170, 255, 0.06) 0%, rgba(64, 170, 255, 0.28) 100%);
}
</style>
