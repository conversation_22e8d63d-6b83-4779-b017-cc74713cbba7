<!--水侵监测卡片-->
<template>
  <div class="well-water-card flex-column flex-gap-16">
    <div class="title width-100">{{ getLabel(wellWaterInfo.deviceName, null) }}</div>
    <div class="content flex-center flex-gap-8">
      <svg-icon v-if="wellWaterInfo.onlineStatus == 0" icon-class="unified-offline" class="font-size-64" />
      <template v-else>
        <svg-icon :icon-class="iconClass" class="font-size-54" />
        <span class="water-status" :class="statusClass">{{ title }}</span>
      </template>
    </div>
  </div>
</template>
<script>
//p630002 1正常 0异常
const WATER_POINT = 'p630002';
export default {
  name: 'WellWaterCard',
  props: {
    wellWaterInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    extend() {
      return JSON.parse(this.wellWaterInfo.extend || '{}');
    },
    title() {
      return this.extend[WATER_POINT] == 0 ? '异常' : '正常';
    },
    iconClass() {
      return this.extend[WATER_POINT] == 0 ? 'water-error' : 'water-normal';
    },
    statusClass() {
      return this.extend[WATER_POINT] == 0 ? 'water-error' : 'water-normal';
    }
  }
};
</script>
<style scoped lang="less">
.well-water-card {
  height: 118px;
  border: 1px solid #255dae;
  background: linear-gradient(180deg, #1a4a8e 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
  border-radius: 8px;

  .title {
    height: 38px;
    padding: 8px 16px;
    background: linear-gradient(90deg, #0661ae 0%, rgba(5, 97, 175, 0) 100%);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-radius: 8px 8px 0 0;
  }

  .content {
    height: calc(100% - 38px);

    .water-status {
      font-weight: 500;

      &.water-error {
        color: #f07072;
      }

      &.water-normal {
        color: #42b946;
      }
    }
  }
}
</style>
