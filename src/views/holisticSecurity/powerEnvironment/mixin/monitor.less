.device-module {
  border-radius: 8px;
  background: linear-gradient(
    180deg,
    #1a4a8e 0%,
    rgba(26, 74, 142, 0.97) 0%,
    rgba(14, 64, 133, 0.94) 100%
  );

  box-sizing: border-box;
  border: 1px solid #255dae;
  .title {
    width: 100%;
    background: linear-gradient(90deg, #0661ae 0%, rgba(5, 97, 175, 0) 100%);
    padding: 8px 16px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
}
.before {
  width: 4px;
  height: 18px;
  background: #bde2ff;
}
.powerAlarm {
  color: @di-color-text-error;
}
.offline {
  color: @di-color-text-second;
}