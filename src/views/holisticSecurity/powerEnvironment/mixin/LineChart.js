import echarts from "@/utils/enquireEchart";
import { debounce } from "xe-utils";
export default {
  data() {
    return {
      myChart: null, // 保存图表实例
      resizeObserver: null, // 用于观察容器尺寸变化
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.setupResizeHandler();
    });
  },
  methods: {
    resize() {
      const myChart = echarts.init(document.getElementById(this.id));
      myChart.resize();
    },
    initChart() {
      const chartDom = document.getElementById(this.id);
      if (!chartDom) return;

      // 如果已有实例，先销毁
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(chartDom);
      this.myChart.setOption(
        this.getOption({
          xData: [
            "00:00",
            "02:00",
            "04:00",
            "06:00",
            "08:00",
            "10:00",
            "12:00",
            "14:00",
            "16:00",
            "18:00",
            "20:00",
            "22:00",
            "24:00",
          ],
          yData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        })
      );
      // 使用 ResizeObserver 监听容器尺寸变化（现代浏览器支持）
      if (typeof ResizeObserver !== "undefined") {
        this.resizeObserver = new ResizeObserver(
          debounce(() => {
            this.myChart && this.myChart.resize();
          }, 100)
        );
        this.resizeObserver.observe(chartDom);
      }
    },
    setupResizeHandler() {
      // 添加防抖的窗口resize监听
      this.debouncedResize = debounce(() => {
        this.myChart && this.myChart.resize();
      }, 100);
      window.addEventListener("resize", this.debouncedResize);
    },
    getOption({ xData, yData }) {
      return {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(64, 170, 255, 0)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(64, 170, 255, 0.4)", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          className: "di-chart-tooltip",
          borderColor: "rgba(189, 226, 255, 0.4)",
          borderWidth: 1,
          // 自定义显示逻辑
          formatter: (params) => {
            return `<div class="title font-14">${params[0].axisValue}</div>
                    <div class="content">
                      <div  class="content-item">
                        <div class="flex-start">
                        ${params[0].marker}
                        <span class="tooltip-item-label">${
                          params[0].seriesName
                        }：</span>
                        </div>
                        <span>
                        <span class="color-text-white">${
                          params[0].value == 0 ? 0 : params[0].value || "--"
                        }</span>
                        <span class="color-text-gray content-unit">°C</span>
                        </span>
                      </div>

                    </div>`;
          },
        },
        legend: {
          icon: "roundRect",
          data: ["温度"],
          top: 8,
          bottom: 0,
          itemGap: 15,
          itemWidth: 10,
          itemHeight: 10,
          itemStyle: {
            borderRadius: 2,
          },
          textStyle: {
            fontSize: 10,
            lineHeight: 10,
            verticalAlign: "middle",
            color: "#efefef",
          },
        },
        grid: {
          left: "16px",
          right: "16px",
          top: "40px",
          bottom: "8px",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: xData,
            axisTick: { show: false },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(97, 161, 213, 0.19)",
              },
            },
            axisLabel: {
              showMinLabel: true,
              showMaxLabel: true,
              color: "#85CAFF",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisPointer: { show: false },
            axisLabel: {
              color: "#85CAFF",
            },
          },
        ],
        series: [
          {
            name: "温度",
            type: "line",
            itemStyle: { color: "#9DC535" },
            emphasis: { disabled: true },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#9DC535",
                },
                {
                  offset: 1,
                  color: "rgba(255, 255, 255, 0)",
                },
              ]),
            },
            data: yData,
          },
        ],
      };
    },
  },
  beforeDestroy() {
    // 清理图表实例
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }

    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    // 移除窗口resize监听
    if (this.debouncedResize) {
      window.removeEventListener("resize", this.debouncedResize);
    }
  },
};
