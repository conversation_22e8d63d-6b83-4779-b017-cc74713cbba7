.ant-spin-nested-loading {
  //height: calc(100% - 82px);
}

.table-container {
  height: calc(100% - 130px);
  background: linear-gradient(180deg, #18488b 0%, #0e4085 100%);
  border-radius: 4px;
  padding: 16px 24px;
  position: relative;

  .table-content {
    display: grid;
    grid-column-gap: 12px;
    grid-row-gap: 16px;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    height: calc(100% - 100px);
    overflow-y: auto;
    margin-right: -24px;
    padding-right: 24px;
    overflow-x: hidden;

    &.empty-data {
      color: @di-color-text-white;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .card-item {
      cursor: pointer;
      height: 448px;
      border-radius: 4px;
      background: #1f559e;
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(180deg,
      rgba(64, 170, 255, 0.53) 0%,
      rgba(64, 170, 255, 0) 32%,
      rgba(64, 170, 255, 0.0631) 79%,
      rgba(64, 170, 255, 0.12) 100%) 1;
      box-shadow: inset 0px 7px 10px 0px rgba(64, 170, 255, 0.14);
      padding: 8px;
      color: @di-color-text-white;
      position: relative;

      &:hover{
        background: #386DB6;
      }
      .grade-div {
        color: #fff;
        border-radius: 14px;
        padding: 0 10px;
        height: 20px;
        position: absolute;
        right: 18px;
        top: 16px;
        z-index: 1;
        font-size: 12px;
      }

      .img-box {
        height: 216px;
        background-color: #092753;
        display: flex;
        align-items: center;
        justify-content: center;

        .error-image {
          height: 80px;
          object-fit: contain;
        }
      }

      .content-box {
        padding: 24px 8px 16px;

        .operations {
          position: absolute;
          left: 0;
          bottom: 13px;
          width: 100%;
          button + button {
            margin-left: 16px;
          }
        }

        .info-item {
          display: flex;
          align-items: center;
          justify-content: space-between;


          .grade-reason {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            margin-left: 24px;
          }

          &.warning {
            color: #F07072;
          }

          &:not(:last-child) {
            margin-bottom: 16px;
          }
        }
      }

      .card-item-vignette {
        position: absolute;
        bottom: 0px;
        left: 50%;
        width: 282px;
        height: 20px;
        transform: translateX(-50%);
      }
    }
  }

  .table-vignette{
    position: absolute;
    bottom: 52px;
    left: 0;
    width: 100%;
    height: 16px;
    opacity: 0.1;
    background: linear-gradient(180deg, rgba(97, 161, 213, 0) 0%, #61A1D5 100%);
  }
}