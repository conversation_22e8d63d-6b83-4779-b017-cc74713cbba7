<template>
  <div class="width-height-100">
    <div class="card-box margin-b-16">
      <Card class="grid-column-3" style="height: 115px" :fireList="monitorList" title="监控点统计" />
      <Card class="grid-column-2" style="height: 115px" :fireList="electronicFenceList" title="电子围栏" />
      <Card class="grid-column-2" style="height: 115px" :fireList="alarmList" title="告警统计" />
    </div>
    <div class="table-container" id="drawerDetail">
      <a-spin :spinning="loading" class="width-100 height-100">
        <AroundSearch :searchParams.sync="searchParams" @query="handleSearch" @reset="handleReset" />
        <div class="margin-t-16 margin-b-16 divide-line"></div>
        <div class="table-content" :class="dataSource.length ? '' : 'empty-data'">
          <template v-if="dataSource && dataSource.length">
            <div v-for="item in dataSource" :key="item.id" class="card-item" @click="handleOpenDrawer(item)">
              <div class="grade-div" :style="{ background: getBgColor(item.alarmGrade) }">
                {{ item.alarmGradeName }}
              </div>
              <div class="img-box">
                <img
                  :src="item?.fileDetail?.path || require(`@/assets/images/error-image.png`)"
                  :alt="item.alarmRemarkName"
                  class="width-height-100"
                  :class="{ 'error-image': !item?.fileDetail?.path }"
                  loading="lazy"
                />
              </div>
              <div class="content-box">
                <div class="info-item">
                  <span class="text-ellipsis"> <svg-icon icon-class="2d-alarm-monitor" class="font-size-20 margin-r-4" />设备名称：{{ getLabel(item.deviceName, null) }}</span>
                </div>
                <div class="info-item">
                  <span class="flex-start">
                    <svg-icon icon-class="2d-alarm-time" class="font-size-20 margin-r-4" />
                    发生时间：{{ getLabel(item.happenTime, null) }}
                  </span>
                </div>
                <div class="info-item" v-if="item.handleStatus == '03'">
                  <span class="flex-start">
                    <svg-icon icon-class="circle-completed" class="font-size-20 margin-r-4" />
                    确认时间：{{ getLabel(item.disappearTime, null) }}
                  </span>
                </div>
                <div class="operations flex-center">
                  <DiThrottleButton
                    :label="item.handleStatus == '03' ? '已确认' : '确认'"
                    @click="(e) => handleDistributed(e, item)"
                    :class="{ 'di-cancel-btn': item.handleStatus == '03' }"
                    :disabled="item.handleStatus == '03'"
                  />
                  <DiThrottleButton label="详情" class="di-cancel-btn" />
                  <DiThrottleButton v-if="item.handleStatus == '03'" label="删除" class="di-cancel-btn" @click.stop="handleDelete(item)" />
                </div>
              </div>
              <div class="table-vignette"></div>
            </div>
          </template>
          <div v-else class="no-data"></div>
        </div>
        <div class="table-vignette"></div>
        <PagePagination :pageSize="pageData.size" :current="pageData.curPage" :total="total" @size-change="pageChangeEvent" />
      </a-spin>
      <DrawerView ref="drawerView" @cancel="handleSearch" />
    </div>
  </div>
</template>
<script>
import Card from '@/views/holisticSecurity/fire/modules/Card.vue';
import AroundSearch from './modules/Search.vue';
import { DEVICE_TYPE_ENUM, DEVICE_SUBTYPE_ENUM, processSensorStatus } from '@/utils/util';
import { PSA_INFO } from '@/store/mutation-types';
import { cloneDeep } from 'lodash';
import {
  affirmForSensorApi, dataStatisticalRateForSensorApi,
  dataStatisticalSumForSensorApi,
  deleteAlarmEvent,
  listForSensorApi
} from '@/api/health/AlarmEvents';
import { getModelData } from '@/api/2dMap/psOverview';

export default {
  name: 'Around',
  components: { AroundSearch, Card },
  data() {
    return {
      monitorList: [
        { name: '总数', key: 'total', num: 0, icon: 'device-total' },
        { name: '在线数量', key: 'onlineNum', num: 0, icon: 'device-online', color: '#42B946' },
        { name: '离线数量', key: 'offlineNum', num: 0, icon: 'device-offline', color: '#F07072' }
      ],
      electronicFenceList: [
        { name: '数量', deviceType: '101', num: 0, icon: 'device-total' },
        { name: '防区', deviceType: '100', num: 0, icon: 'device-total' }
      ],
      alarmList: [
        { name: '总数', num: 0, key: 'total', icon: 'device-total', color: '#FFFFFF' },
        { name: '今日新增', num: 0, key: 'todayAdd', icon: 'device-added', color: '#FA5151' }
      ],
      dataSource: [],
      pageData: { curPage: 1, size: 10 },
      total: 0,
      searchParams: {
        dateRange: undefined, //时间区间
        handleStatus: undefined, // 处理状态
        alarmStatus: '01',
        alarmReason: ''
      },
      alarmRemarkList: ['84']
    };
  },
  created() {
    this.getMonitorCardData();
    this.getElectronicFenceData();
    this.getFenceAreaData();
    this.getAlarmCardData();
    this.queryList();
  },
  computed: {
    treePsId() {
      return this.$ls.get(PSA_INFO)?.psId;
    }
  },
  methods: {
    // 获取监控点统计
    getMonitorCardData() {
      const monitorParams = {
        psId: this.treePsId,
        modelName: 'sensorScale',
        deviceType: DEVICE_TYPE_ENUM.CAMERA,
        deviceSubType: DEVICE_SUBTYPE_ENUM.CAMERA_SUBTYPE
      };
      getModelData(monitorParams).then((res) => {
        const dataResult = res?.result_data?.dataResult?.sensorStatus || [];
        processSensorStatus(dataResult, [
          {
            type: DEVICE_TYPE_ENUM.CAMERA,
            list: this.monitorList,
            subType: DEVICE_SUBTYPE_ENUM.CAMERA_SUBTYPE
          }
        ]);
      });
    },
    // 获取电子围栏统计
    async getElectronicFenceData() {
      const res = await getModelData({
        psId: this.treePsId,
        modelName: 'sensorScale',
        deviceType: DEVICE_TYPE_ENUM.ELECTRONIC_FENCE
      });
      const dataResult = res?.result_data?.dataResult?.sensorStatus?.[0] || {};
      this.electronicFenceList.forEach(item => {
        if(dataResult.deviceType === item.deviceType) {
          item.num = (dataResult?.onlineNum + dataResult?.offlineNum) ?? 0;
        }
      });
    },
    // 获取告警统计
    async getAlarmCardData() {
      const params = this.formatParams();
      const [sumData, rateData] = await Promise.allSettled([
        dataStatisticalSumForSensorApi({
          treePsId: this.treePsId,
          alarmRemarkList: ['84'],
          ...params,
        }),
        dataStatisticalRateForSensorApi({
          treePsId: this.treePsId,
          alarmRemarkList: ['84'],
          ...params,
        }),
      ]);
      const totalSum = sumData.value.result_data['84'];
      const rateItem = rateData.value.result_data.find(o => o.tabCode === '84');
      const rateSum = rateItem?.sumToday ?? 0;
      this.alarmList.forEach(item => {
        item.num = item.key === 'total' ? totalSum : rateSum;
      });

    },
    // 获取防区统计
    async getFenceAreaData() {
      const {TEMPERATURE_HUMIDITY} = DEVICE_TYPE_ENUM;
      const {ELECTRONIC_FENCE_SUBTYPE} = DEVICE_SUBTYPE_ENUM;
      const areaParams = {
        psId: this.treePsId,
        modelName: 'sensorScale',
        deviceType: TEMPERATURE_HUMIDITY,
        deviceSubType: ELECTRONIC_FENCE_SUBTYPE
      };
      const res = await getModelData(areaParams);
      const dataResult = res?.result_data?.dataResult?.sensorStatus?.[0] || {};
      this.electronicFenceList.forEach(item => {
        if(dataResult.deviceType === item.deviceType) {
          item.num = (dataResult?.onlineNum + dataResult?.offlineNum) ?? 0;
        }
      });
    },
    //   format入参
    formatParams() {
      const params = cloneDeep(this.searchParams);
      if (!this.$isEmpty(params.dateRange)) {
        const [startTime, endTime] = params.dateRange;
        params.startTime = startTime;
        params.endTime = endTime;
        delete params.dateRange;
      }
      return params;
    },
    // 根据告警类型获取背景色
    getBgColor(grade) {
      switch (grade) {
        case '1':
          return 'linear-gradient(180deg, #F07072 0%, #8C1B1D 100%)';
        case '2':
          return 'linear-gradient(180deg, #FFA56E 0%, #B6561B 100%)';
        case '3':
          return 'linear-gradient(180deg, #BB6EFF 0%, #591BB6 100%)';
        case '4':
          return 'linear-gradient(180deg, #B9CCD3 0%, #59737C 100%)';
      }
    },
    // 分页逻辑
    pageChangeEvent(curPage, size) {
      this.pageData.curPage = curPage;
      this.pageData.size = size;
      this.queryList();
    },
    handleSearch() {
      Object.assign(this.pageData, { curPage: 1 });
      this.queryList();
    },
    // 查询列表逻辑
    async queryList() {
      this.loading = true;
      const { treePsId, pageData, alarmRemarkList } = this;
      const params = this.formatParams();
      const queryParams = { treePsId, alarmRemarkList, ...params, ...pageData };
      const res = await listForSensorApi(queryParams);
      this.loading = false;
      const { pageList, rowCount } = res.result_data;
      this.dataSource = pageList;
      this.total = rowCount;
    },
    handleReset() {
      this.searchParams = this.$options.data.call(this).searchParams;
      this.queryList();
    },
    handleOpenDrawer(row = {}) {
      this.$refs.drawerView.init('3', row, '/holisticSecurity/around/modules/AroundDetail');
    },
    // 确认逻辑
    handleDistributed(e, item) {
      e.stopPropagation();
      this.$confirm({
        title: '是否确认',
        centered: true,
        onOk: async () => {
          await affirmForSensorApi({ id: item.id, handleStatus: '03' });
          this.$message.success('操作成功');
          await this.handleSearch();
        }
      });
    },
    // 告警删除
    handleDelete(item) {
      this.$confirm({
        title: '是否删除',
        centered: true,
        onOk: async () => {
          await deleteAlarmEvent({ id: item.id });
          this.$message.success('操作成功');
          await this.handleSearch();
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
@import './less/around.less';

.card-box {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-gap: 16px;

  .grid-column-1 {
    grid-column: span 1;
    grid-row: 1;
  }

  .grid-column-2 {
    grid-column: span 2;
    grid-row: 1;
  }

  .grid-column-3 {
    grid-column: span 3;
    grid-row: 1;
  }
}
</style>
