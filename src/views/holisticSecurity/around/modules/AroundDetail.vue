<template>
  <div class="drawer-form-com">
    <a-spin :spinning="loading" class="width-100 height-100">
      <div class="drawer-form-content">
        <DetailLayout class="margin-t-12" :labelList="basicLabelList" :form="formData" />
        <DetailLayout class="margin-t-12 alarm-card" :labelList="alarmLabelList" :form="formData" title="告警信息" v-if="formData.id">
          <template v-slot:alarmShotImages>
            <div class="alarm-shot-images alarm-card-item">
              <div class="title">故障截图</div>
              <div class="content cursor-pointer" @click="previewImage">
                <img
                  :src="formData.media && formData.media.pic && formData.media.pic[0] && formData.media.pic[0].path ? formData.media.pic[0].path : require('@/assets/images/error-image.png')"
                  :class="{
                    'error-image': !(formData.media && formData.media.pic && formData.media.pic[0] && formData.media.pic[0].path),
                    'width-100 height-100': formData.media && formData.media.pic && formData.media.pic[0] && formData.media.pic[0].path
                  }"
                  alt="故障截图"
                />
              </div>
            </div>
          </template>
          <!--电子围栏不需要历史回放-->
          <template v-slot:alarmPlayback v-if="!(formData.alarmRemark == '84' && formData.alarmReason == '1620')">
            <div class="alarm-playback alarm-card-item">
              <div class="title">历史回放</div>
              <div class="content">
                <video
                  v-if="formData.media && formData.media.video && formData.media.video[0] && formData.media.video[0].path"
                  :src="formData.media && formData.media.video && formData.media.video[0] && formData.media.video[0].path"
                  controls
                  class="width-100 height-100"
                />
                <img v-else class="error-image" src="@/assets/images/error-video.png" />
              </div>
            </div>
          </template>
          <!--电子围栏不需要实时监控-->
          <template v-slot:alarmLiveVideo v-if="!(formData.alarmRemark == '84' && formData.alarmReason == '1620')">
            <div class="alarm-live-video alarm-card-item">
              <div class="title">
                <span class="status"></span>
                <span>实时监控</span>
              </div>
              <div class="content">
                <LiveVideo v-if="formData.liveUrl" :url="formData.liveUrl" />
                <img v-else class="error-image" src="@/assets/images/error-video.png" />
              </div>
            </div>
          </template>
        </DetailLayout>
      </div>
      <div class="drawer-form-foot">
        <DiThrottleButton :label="rowInfo.handleStatus != '03' ? '取消' : '返回'" class="di-cancel-btn" @click="$emit('cancel', !!rowInfo.comeFromAlarm)" />
        <DiThrottleButton v-if="rowInfo.handleStatus == '03'" class="di-cancel-btn" label="删除" @click="handleDelete" />
        <DiThrottleButton v-if="rowInfo.handleStatus != '03'" label="确认" @click="handleConfirm" />
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getDetailForSensorApi, affirmForSensorApi, deleteAlarmEvent } from '@/api/health/AlarmEvents';
import { pLayLiveApi } from '@/api/common';
import { PSA_INFO } from '@/store/mutation-types';
import VueViewer from '@/mixins/VueViewer';
import LiveVideo from '@/components/Live.vue';

export default {
  name: 'AroundDetail',
  components: { LiveVideo },
  mixins: [VueViewer],
  data() {
    return {
      loading: false,
      basicLabelList: [
        {
          label: '电站名称',
          key: 'psName'
        },
        {
          label: '设备名称',
          key: 'deviceName'
        },
        {
          label: '设备位置',
          key: 'location'
        },
        {
          label: '告警类型',
          key: 'alarmRemarkName'
        },
        {
          label: '告警原因',
          key: 'alarmReasonName'
        },
        {
          label: '告警时间',
          key: 'happenTime'
        },
        {
          label: '确认时间',
          key: 'disappearTime'
        }
      ],
      alarmLabelList: [
        {
          slot: 'alarmShotImages'
        },
        {
          slot: 'alarmPlayback'
        },
        {
          slot: 'alarmLiveVideo'
        }
      ],
      formData: {},
      rowInfo: {}
    };
  },
  computed: {
    psId() {
      const psaInfo = this.$ls.get(PSA_INFO);
      return psaInfo.psId;
    }
  },
  methods: {
    async init(type, row) {
      this.rowInfo = row;
      this.loading = true;
      const { id } = this.rowInfo;
      const detailResult = await getDetailForSensorApi({ id }).catch(() => (this.loading = false));
      const { live } = detailResult.result_data.media;
      if (live && live[0] && live[0].psKey) {
        const liveResultData = await pLayLiveApi({
          psKeys: live[0].psKey,
          psId: this.psId
        }).catch(() => (this.loading = false));
        Object.assign(detailResult.result_data, {
          liveUrl: liveResultData[0] && liveResultData[0].liveUrl && liveResultData[0].liveUrl[0]
        });
      }
      this.formData = detailResult.result_data;
      this.loading = false;
      return '详情';
    },

    async handleConfirm() {
      const { id } = this.rowInfo;
      await affirmForSensorApi({ id, handleStatus: '03' });
      this.$message.success('操作成功');
      this.$emit('cancel', true);
    },
    previewImage() {
      if (this.$isEmpty(this.formData.media) || this.$isEmpty(this.formData.media.pic) || this.$isEmpty(this.formData.media.pic[0]) || this.$isEmpty(this.formData.media.pic[0].path)) return;
      this.viewerImage({ images: [{ path: this.formData.media.pic[0].path }] });
    },
    previewImageFault(type) {
      const findItem = this.formData?.media?.pic?.find((item) => item.annexName == type);
      this.viewerImage({ images: [{ path: findItem.path }] });
    },
    getFaultPicture(type) {
      const findItem = this.formData?.media?.pic?.find((item) => item.annexName == type);
      return findItem?.path ?? null;
    },
    // 告警删除
    async handleDelete() {
      const { id } = this.rowInfo;
      this.$confirm({
        title: '是否删除',
        centered: true,
        onOk: async () => {
          await deleteAlarmEvent({ id: id });
          this.$message.success('操作成功');
          this.$emit('cancel', true);
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
:deep(.alarm-card) {
  .ant-row {
    display: flex;
    margin-top: 8px;

    .alarm-card-item {
      width: 482px;
      height: 335px;
      border-radius: 4px;
      background: #083573;
      margin-left: 12px;
      padding: 16px 16px 24px;

      &.fire-alarm {
        width: 820px;
        height: 294px;

        .fire-alarm-image {
          width: 50%;
          height: 100%;
        }
      }

      &.fire-alarm-item {
        height: 294px;
      }

      .title {
        font-size: 18px;

        .status {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: #f07072;
          margin-right: 8px;
        }
      }

      .content {
        height: calc(100% - 35px);
        width: 100%;
        background: #113871;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        .error-image {
          object-fit: contain;
          width: 96px;
        }
      }
    }
  }
}

.drawer-form-foot {
  button + button {
    margin-left: 16px;
  }
}
</style>
