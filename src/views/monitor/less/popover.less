.ant-popover.device {
    .ant-popover-arrow {
        display: none;
    }

    .ant-popover-inner-content {
        background: linear-gradient(222deg, rgba(94, 128, 171, 0.25) 0%, rgba(0, 0, 0, 0.35) 71%, rgba(0, 0, 0, 0.45) 100%);
        box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
        border-radius: 4px;
        border: 2px solid;
        border-image: linear-gradient(129deg, rgba(84, 119, 166, 1), rgba(175, 175, 175, 0), rgba(29, 69, 122, 1)) 2 2;
        backdrop-filter: blur(8px);
        padding: 16px 0 0 0;
    }

    .popover-bg {
        display: flex;
    }

    .title {
        margin-bottom: 10px;
        color: white;
    }

    .title::before {
        content: ' ';
        width: 4px;
        margin-right: 8px;
        align-items: middle;
        border-radius: 0 2px 2px 0;
        height: 14px;
        background: #24ccff;
        display: inline-block;
    }


}
.reason_title {
    display: flex;
    padding: 0px 12px 8px;
    align-items: center;
    .alarm_name,
    .alarm_name_bg {
      width: 167px;
      height: 24px;
      padding-left: 8px;
      margin-left: 8px;
      border-radius: 2px;
      position: relative;
      overflow:hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .alarm_name_bg {
      position: absolute;
      top: 0;
      right: 0;
      background: linear-gradient(90deg, #f6504d 0%, rgba(248, 115, 115, 0) 100%);
      opacity: 0.2;
    }
  }
section,
.inverter_desc {
    min-width: 138px;

    .params {
        margin-left: 12px;
        height: 30px;
        line-height: 30px;
    }

    .params .left {
        width: 80px;
        display: inline-block;
        text-align: left;
        div {
            display: inline-block;
            &:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: bottom;
            max-width: 85%;
            }
        }
    }

    .params .left.width_120 {
        width: 120px;
        
    }

    .params.left.width_180 {
        width: 180px;
    }

    .params .right {
        min-width: 80px;
        display: inline-block;
    }
}

.device_bottom {
    height: 37px;
    background: linear-gradient(90deg, #223046 0%, #0C111C 100%);
    line-height: 37px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: 12px;
    .anomaly-title {
        padding-left: 8px;
      
        font-weight: 400;
        color: #A7A7A7;
    }
    .anomaly,
    .unconnected {
        width: 8px;
        height: 8px;
        border-radius: 8px;
        margin: 0 4px;
    }

    .anomaly {
        background-color: #e14242;
    }

    .anomaly_number {
        color: #e14242;
        padding-left: 8px;
    }

    .unconnected {
        background-color: rgb(13, 112, 53);;
        margin-left: 8px;
    }
    .unconnected_number {
        color: white;
        padding-left: 8px;
        padding-right: 4px;
    }
}

#monitor-content .infinite-loading-container {
    width: 100%;
    text-align: center;
    height: 100%;;
}
#monitor-content .infinite-status-prompt {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.no-data-img {
    margin-top: 72px;
}
.border_color {
    border-color: transparent;
}
[class*='point_alarm_check_'] .ant-checkbox-checked::after {
    border-color: transparent;
}
.bgAndShadow(@r,@g,@b,@a:0.4) {
    background: rgba(@r,@g,@b, @a);
    width: 18px;
    height: 18px;
    &::after {
        left: 25%;
    }
}
.point_alarm_check_1 .ant-checkbox-checked .ant-checkbox-inner {
   .bgAndShadow(225,66,66);
    .border_color;
}

.point_alarm_check_2 .ant-checkbox-checked .ant-checkbox-inner {
    .bgAndShadow(255, 145, 30, 0.3);
    .border_color;
}

.point_alarm_check_3 .ant-checkbox-checked .ant-checkbox-inner {
    .bgAndShadow(131, 134, 36);
    .border_color;
}

.point_alarm_check_4 .ant-checkbox-checked .ant-checkbox-inner {
    background: rgba(36, 204, 255, .4);
    .border_color;
}

.point_alarm_check_5 .ant-checkbox-checked .ant-checkbox-inner {
    .bgAndShadow(55, 222, 133);
    .border_color;
   
}

.point_alarm_check_6 .ant-checkbox-checked .ant-checkbox-inner {
    background: rgba(176, 176, 176, 0.4) !important;
    .bgAndShadow(176, 176, 176);
    .border_color;
}
label[class*='point_alarm_check_'].ant-checkbox-wrapper {
    color: #A7A7A7 !important;
}
.no-data-text {
    font-size: 12px;
    font-weight: 400;
    color: #A7A7A7;
  }
  .no-data {
    margin: 0 auto;
  }
  .card-spin-center {
    margin-left: 20px;
  }
  .card-spin-center-inverter {
    .ant-spin-nested-loading > div > .ant-spin {
        width: 98%;
    }
    .ant-spin-blur {
        margin-left: 20px;
    }
  }