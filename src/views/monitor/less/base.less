.device {
  display: flex;
  align-items: center;
  margin-top: 16px;
  flex-direction: column;
  .padding_20 {
    margin-left: 20px;
  }
  header {
    display: flex;
    .device_name_lable {
      width: calc(100% - 30px);
    }
  }
  .content_header {
    display: flex;
    width: calc(100%);
    justify-content: space-between;
    margin-bottom: 16px;
    align-items: center;
    .right  {
      display: flex;
    }
    .right div+div {
      margin-left: 16px;
    }
    .right .anticon {
      font-size: 18px;
    }
  }
  .b-box {
    width: 168px;
    height: 228px;
    background: linear-gradient(232deg, rgba(6, 8, 15, 0.45) -3%, rgba(34, 42, 71, 0.35) 27%, rgba(136, 166, 205, 0.25) 97%);
    box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
    border-radius: 4px;
    opacity: 0.8;
    border: 2px solid;
    border-image: linear-gradient(121deg, #5477A6 1%, rgba(175, 175, 175, 0) 55%, #1D457A 107%) 2;
    backdrop-filter: blur(14px);
    display: flex;
    flex-direction: column;
    &:hover {
      box-shadow: 0px 2px 10px 0px rgba(45, 146, 226, 0.71);
      border: 2px solid #2D92E2;
    }
    header {
        height: 46px;
        display: flex;
        align-items: center;
        background: linear-gradient(90deg, rgba(36, 156, 255, 0.3) 7%, rgba(102, 164, 231, 0.1) 96%);
        &::before {
              content: ' ';
            width: 4px;
            margin-right: 16px;
            align-items: middle;
            border-radius: 0 2px 2px 0;
            height: 14px;
            background: #24ccff;
            display: inline-block;
        }
        .device_name {
          width: 85%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .next_device {
          cursor: pointer;
          width: 32px;
          height: 32px;
          background: url(../../../assets/images/monitor/device/link.png)  no-repeat;
          background-size: 100% 100%;
          position: absolute;
          right: 12px;
        }
        .next_device:hover {
          background: url(../../../assets/images/monitor/device/link_dark.png) no-repeat;
          background-size: 100% 100%;
        }
    }
    section {
        flex: 1;
        align-items: center;
        justify-content: center;
        display: flex;
        .left {
          width: 128px;
        }
        .right {
          flex: 1;
          display: flex;
        };
        .right-desc {
          width: 160px;
          display: flex;
          flex-wrap: wrap;
          align-content: baseline;
          div {
              width: 32px;
              height: 30px;
              border-radius: 4px;
              margin:  0 8px 8px 0;
              text-align: center;
              line-height: 30px;
              color: white;
          }
        } 
    }
    footer {
        height: 46px;
        background: linear-gradient(90deg, #223046 0%, #0C111C 100%);
        display: flex;
        align-items: center;
        position: relative;
        padding-left: 16px;
        div {
        position: absolute;
        left: 58px;
        font-weight: 500;
        letter-spacing: 2px;
        }
        .alarm_type_1 {
        color: #FFC2C4;
        }
        .alarm_type_2 {
        color: #FFE3C9;
        }
        .alarm_type_3 {
        color: #FDFFCC;
        }
        .alarm_type_4 {
        color: #C5E6FF;
        }
        .alarm_type_5 {
        color: #C5FFE0;
        }
    }
  }
  .line {
    border-top: 8px solid #24ccff;
  }
  .vertical-line {
    height: 38px;
    width: 2px;
    background: #24CCFF;
  }
  .content {
    display: flex;
    .circuit {
        display: flex;
        flex-direction: column;
        align-items: center;
    
    }
    .circuit {
        margin-right: 16px;
    }
  }

}
.point_alarm_type_1 {
  background:rgba(225,66,66,0.4);
  box-shadow: 0px 0px 4px 3px rgba(225,66,66,0.5);
  border: 2px solid #E14242;
  line-height: 26px !important;
}
.point_alarm_type_2 {
  background: rgba(255,145,30,0.3)
}
.point_alarm_type_3 {
  background: rgb(253, 255, 204,.4);
}
.point_alarm_type_4 {
  background: rgba(36, 204, 255, .4)
}
.point_alarm_type_5 {
  background: rgba(55, 222, 133,.4)
}
.point_alarm_type_6 {
  background: rgba(176, 176, 176, 0.4);
}
.ellipsis {
  width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.width_100 {
  width: 100%;
}
.spin-content {
  margin-left: 14px;
  .device {
    section + section {
      margin-top: 10px;
    }
    .radio-div {
      margin-left: 6px;
    }
    .radio-div + .radio-div {
      margin-left: 0;
    }
    section .content .circuit {
      margin: 0 10px 10px 0;
    }
  }
  .b-box {
    .alarm-type-img {
      position: absolute;
      right: 52px;
      height: 22px;
    }
    .alarm-type {
      position: absolute;
      font-size: 12px;
      right: 68px;
    }
    section {
      .right-box {
        background: rgba(0, 0, 0, 0.2);
        padding: 16px;
        border-radius: 8px;
        margin-right: 16px;
        flex: 1;
        .reason_title {
          padding: 0 0 8px;
          .alarm_name_bg {
            width: 100%;
          }
          .alarm_name {
            width: calc(100% - 22px);
          }
        }
        .right-content {
          display: flex;
          justify-content: space-between;
          .value-div {
            display: flex;
            margin-right: 4px;
          }
          .head {
            font-size: 12px;
            line-height: 20px;
            opacity: 0.7;
          }
          .text {
            line-height: 22px;
          }
          .text + .text {
            margin-top: 4px;
          }
          .values {
            text-align: right;
            .value {
              line-height: 22px;
              max-width: 58px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .value + .value {
              margin-top: 4px;
            }
          }
          .values + .values {
            margin-left: 8px;
          }
        }
        .device_bottom {
          background: none;
          height: 22px;
          line-height: 22px;
          margin-top: 4px;
          .anomaly-title {
            padding-left: 0;
          }
        }
      }
    }
  }
}