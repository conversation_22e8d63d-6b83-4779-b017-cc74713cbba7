import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://10.5.9.126:8085';
export default {
  components: { InfiniteLoading },
  inject: ['params', 'isShowInverterType'],
  data () {
    return {
      infiniteId: +new Date(),
      noDataImg: require('@/assets/images/public/no-data.png'), // 暂无数据图片
      wholePageLoading: false,
      currentPage: 1,
      alarmGrade: '',
      deviceSubType: '', // 14 集中式 // 15 组串式
      deviceModel: '',
      alarmNumList: [
      ],
      pageSize: null,
      group: ['1', '4', '5', '6'],
      list: [],
      stringsList: [
        {
          'gradeName': '故障组串',
          'grade': '1'
        },
        {
          'gradeName': '低效组串',
          'grade': '4'
        },
        {
          'gradeName': '正常组串',
          'grade': '5'
        }
      ]
    };
  },
  props: {
    psInfo: {
      default: () => {
        return {
          psKey: undefined, uuid: undefined
        };
      }
    }
  },
  created () {
    this.deviceSubType = this.isShowInverterType() == 15 ? 15 : 14;
    this.params().deviceType != 5 && this.getInverterAlarmCount();
    this.getList(false, true);
  },
  computed: {
    activeTotal () {
      return this.alarmNumList ? this.alarmNumList.reduce((a, b) => {
        a = a + b.count;
        return a;
      }, 0) : 0;
    }
  },
  mounted () {
    let timer = setInterval(() => {
      this.list = [];
      this.currentPage = 1;
      this.initData(); // 获取可视区域内的dom节点
    }, 1000 * 5 * 60);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      timer = null;
    });
    this.$once('hook:deactivated', () => {
      clearInterval(timer);
      timer = null;
    });
  },
  methods: {
    initData () {
      this.alarmGrade = '';
      this.deviceSubType = this.isShowInverterType() == 15 ? 15 : 14;
      this.params().deviceType != 5 && this.getInverterAlarmCount();
      this.getList(false, true);
    },
    getInverterAlarmCount () {
      if (!this.url.count) {
        return;
      }
      let params = this.params();
      let deviceType = params.deviceType;
      let str = JSON.parse(JSON.stringify(this.group)).join();
      postAction(baseUrl + this.url.count, {
        ...this.params(),
        deviceSubType: this.params().deviceType == 1 ? this.deviceSubType : undefined,
        stringStatus: ((deviceType == 1 && this.deviceSubType == 15) || deviceType == 4) ? str : undefined,
        ...this.psInfo
      })
        .then((res) => {
          this.alarmNumList = res.result_data;
        })
        .catch((res) => {});
    },
    /** 测点排序
     * params {arr} 测点对象
     * params {num} type: Boolean true 前12个测点， 后12个测点
     */
    getStrings (arr, num) {
      let obj = {};
      if (!arr) return [];
      Object.keys(arr).sort((a, b) => Number(b) - Number(a)).map(item => {
        obj[item] = arr[item];
      });
      let length = Object.values(obj).length;
      return num ? Object.values(obj).slice(0, 16) : Object.values(obj).slice(16, length > 32 ? 32 : length);
    },
    /**
     * 获取列表信息
     */
    async getList (loadMore, isPartRefresh) {
      if (!this.url.alarmList) {
        return;
      }
      let that = this;
      if (isPartRefresh) {
        this.currentPage = 1;
        this.list = [];
        this.wholePageLoading = true;
        this.$emit('loaded', true);
      }
      let params = this.params();
      let deviceType = params.deviceType;
      let isBox = params.deviceType == 1;
      let isEnv = params.deviceType == 5;
      let is24 = (isBox && this.deviceSubType == 14) || (params.deviceSubType && params.deviceSubType == 4);
      // this.pageSize = is24 ? 24 : (deviceType == 4 || this.deviceSubType == 15 ? 9 : deviceType == 7 ? 12 : undefined);
      this.pageSize = is24 ? 32 : (deviceType == 4 || this.deviceSubType == 15 ? undefined : deviceType == 7 ? 16 : undefined);
      let str = JSON.parse(JSON.stringify(this.group)).join();
      postAction(baseUrl + this.url.alarmList, {
        pageSize: this.pageSize,
        currentPage: deviceType == 4 || (isBox && this.deviceSubType == 15) ? undefined : this.currentPage,
        alarmGrade: isEnv ? undefined : this.alarmGrade,
        ...this.params(),
        deviceSubType: isBox ? this.deviceSubType : undefined,
        stringStatus: ((isBox && this.deviceSubType == 15) || deviceType == 4) ? str : undefined,
        ...this.psInfo
      })
        .then((res) => {
          that.list = that.list.concat(res.result_data);
          that.currentPage++;
          this.wholePageLoading = false;
          this.$emit('loaded', false);
          if (loadMore) {
            const length = res.result_data.length;
            loadMore.loaded();
            if (length < this.pageSize || length == 0) {
              loadMore.complete();
            }
          }
        })
        .catch(() => {
          this.wholePageLoading = false;
          this.$emit('loaded', false);
          if (!isPartRefresh) {
            loadMore.complete();
          }
        });
    },
    alarmGradeChange (val, sub) {
      if (sub) {
        this.getInverterAlarmCount();
      }
      this.getList(false, true);
    },
    groupChange () {
      this.getInverterAlarmCount();
      this.getList(false, true);
    },
    getGradePng (grade) { // 目前先写死
      // if (!grade) return;
      // if (grade == '4') {
      //   grade = '3';
      // }
      return require('@/assets/images/health/alarmEvents/grade1.png');
    },
    isExistValue (str, is100) {
      let isStr = str || str == 0 ? str : '--';
      return isStr && is100 ? Math.round(isStr * 1000) : isStr;
    }
  }
};
