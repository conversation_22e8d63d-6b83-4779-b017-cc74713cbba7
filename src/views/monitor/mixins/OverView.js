import countTo from 'vue-count-to'; // 数值滚动插件
import { getStationStatisticsByPsId, stationStatisticsByPsIdCn } from '@/api/monitor/inverter/inverterList.js';
import moment from 'moment';
export default {
  components: { countTo },
  props: {
    deviceParams: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    'deviceParams.psId' () {
      this.refreshHeadData();
      this.refreshData();
    }
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },

  created () {
    this.refreshHeadData();
    this.refreshData();
  },
  methods: {
    // 定时刷新
    refreshData () {
      this.timer && clearInterval(this.timer);
      this.timer = null;
      this.timer = setInterval(() => {
        this.refreshHeadData();
      }, 1000 * 60 * 5);
      this.$once('hook:beforeDestroy', () => {
        clearInterval(this.timer);
        this.timer = null;
      });
      this.$once('hook:deactivated', () => {
        clearInterval(this.timer);
        this.timer = null;
      });
    },
    dataExchange (data, type) {
      if (type === 'unit') {
        return data.split(',')[1];
      } else {
        return Number(data.split(',')[0]);
      }
    },
    dynamicDecimals (unit) {
      if (unit == 'kW' || unit == 'kWh') {
        return 3;
      } else {
        return 4;
      }
    },
    // 时间转换为向前取整五分钟格式
    timeExchange (time) {
      let minitues = Number(time.substr(-2, 2));
      let temp = minitues - (minitues % 5);
      return time.substr(0, 14) + (temp < 10 ? '0' + temp : temp) + '';
    },
    async refreshHeadData () {
      let actions = this.isCn ? stationStatisticsByPsIdCn : getStationStatisticsByPsId;
      let res = await actions({
        psId: this.deviceParams.psId,
        time: this.timeExchange(moment().format('YYYY-MM-DD HH:mm')),
        deviceType: this.deviceParams.deviceType ? this.deviceParams.deviceType : '1'
      });
      if (res.result_code !== '1') {
        this.$message.error(res.result_msg);
        return;
      }
      if (res.result_data && Object.keys(res.result_data).length) {
        this.headData = Object.assign(this.headData, res.result_data);
      }
    }
  }
};
