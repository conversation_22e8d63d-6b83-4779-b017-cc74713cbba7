<!--汇流箱-->
<template>
<a-spin size="large" :spinning="wholePageLoading" class="card-spin-center">
  <div class="device">
    <div class="content_header">
      <a-radio-group v-model="alarmGrade" @change="alarmGradeChange" class="radio-div">
        <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
        <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
          >{{ item.gradeName }}({{ item.count }})</a-radio-button
        >
      </a-radio-group>
      <div class="right">
           <a-checkbox-group v-model="group" @change="groupChange">
          <template v-for="item in stringsList">
          <a-checkbox :key="item.grade" :value="item.grade" :class="['point_alarm_check_'+ item.grade]">{{item.gradeName}}</a-checkbox>
        </template>
         <a-checkbox value="6" class="point_alarm_check_6">
          未接组串
        </a-checkbox>
        </a-checkbox-group>
        <!-- <template v-for="item in alarmNumList">
          <div :key="item.grade"><span :class="['point_alarm_type_'+ item.grade]" style="border:none"><a-icon type="check" /></span>{{item.gradeName}}</div>
        </template>
        <div ><span class="point_alarm_type_6"><a-icon type="check" /></span>未接入</div> -->
      </div>
  </div>
    <section class="width_100">
      <div class="content" :style="{height: 'calc(100vh - 365px)'}">
        <template v-for="item in list">
          <div class="b-box" :key="item.ps_key">
            <header class="device_name" :title="item.device_name">
              <div class="device_name">
                {{ item.device_name }}
              </div>
              <img
                :src="require(`@/assets/images/monitor/device/alarm_type_${item.grade ? item.grade : 5}.png`)"
                class="alarm-type-img"
              />
              <div class="alarm-type" :class="['alarm_type_'+ item.grade]">{{item.gradeName}}</div>
            </header>
            <section>
              <a-popover overlayClassName="device combineBox" placement="right" :offset= "[10,20]">
                <template slot="content">
                  <div class="popover-bg">
                    <section>
                      <div class="reason_title" v-if="item.allReason">
                        <img class="grade-image" v-show="item.allReason" :src="getGradePng(item.alarmGrade)" />
                        <div class="alarm_name" :title="item.allReason">
                          {{ item.allReason }}
                          <div :class="{'alarm_name_bg': item.allReason}"></div>
                        </div>
                      </div>
                      <!-- <div class="params">
                        <span class="left width_120">装机容量(KWp)：</span>
                        <span class="right">{{ item.accessCapacity? item.accessCapacity:'--'}}</span>
                      </div> -->
                      <div class="params">
                        <span class="left width_120">总直流功率(kW)：</span>
                        <span class="right">{{item.power}}</span>
                      </div>
                      <div class="params">
                        <span class="left width_120">总直流电流(A)：</span>
                        <span class="right">{{item.electricCurrent}}</span>
                      </div>
                    </section>
                  </div>
                </template>
                <div class="left flex-center"><img src="@/assets/images/monitor/device/combinerBox_s.png" /></div>
              </a-popover>
              <div class="right" v-if="item.strings">
                <div class="right-desc">
                  <template v-for="child in getStrings(item.strings, true)"
                    ><div :key="child.point" :class="['point_alarm_type_'+ child.status]">
                      {{ child.status == '6' ? '' : child.value }}
                    </div></template
                  >
                </div>
                <div class="right-desc">
                  <template v-for="child in getStrings(item.strings, false)"
                    ><div :key="child.point" :class="['point_alarm_type_'+ child.status]">
                      {{ child.status == '6' ? '' : child.value }}
                    </div></template
                  >
                </div>
              </div>
            </section>
          </div>
        </template>

        <infinite-loading :identifier="infiniteId" v-if="loadMore" @infinite="refreshListData">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
            <p class="no-data"></p>
             <p class="no-data-text">无更多数据</p>
          </div>
          <div slot="no-results" v-if="!list.length">
           <p class="no-data"></p>
            <p class="no-data-text">暂无数据</p>
          </div>
        </infinite-loading>
        <div class="infinite-loading-container" v-if="!loadMore && !wholePageLoading && !list.length">
           <p class="no-data"></p>
          <p class="no-data-text">暂无数据</p>
        </div>
      </div>
    </section>
  </div>
   </a-spin>
</template>
<script>
// import deviceType from '../mixins/deviceType';
import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
export default {
  // mixins: [deviceType],
  components: { InfiniteLoading },
  inject: ['params', 'isShowInverterType'],
  data () {
    return {
      url: {
        count: '/monitorNew/getBoxAlarmCount',
        alarmList: '/monitorNew/getBoxAlarmList'
      },
      loadMore: false,
      infiniteId: +new Date(),
      noDataImg: require('@/assets/images/public/no-data.png'), // 暂无数据图片
      wholePageLoading: false,
      currentPage: 0,
      pageSize: undefined,
      alarmGrade: '',
      deviceSubType: '', // 14 集中式 // 15 组串式
      deviceModel: '',
      alarmNumList: [],
      group: ['1', '4', '5', '6'],
      list: [],
      stringsList: [
        {
          'gradeName': '故障组串',
          'grade': '1'
        },
        {
          'gradeName': '低效组串',
          'grade': '4'
        },
        {
          'gradeName': '正常组串',
          'grade': '5'
        }
      ]
    };
  },
  props: {
    psInfo: {
      default: () => {
        return {
          psKey: undefined, uuid: undefined
        };
      }
    }
  },
  created () {
    this.initData();
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    },
    activeTotal () {
      return this.alarmNumList ? this.alarmNumList.reduce((a, b) => {
        a = a + b.count;
        return a;
      }, 0) : 0;
    }
  },
  mounted () {
    let timer = setInterval(() => {
      this.refreshData();
    }, 1000 * 5 * 60);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      timer = null;
    });
    this.$once('hook:deactivated', () => {
      clearInterval(timer);
      timer = null;
    });
  },
  methods: {
    // 接口请求
    getBoxAlarmList (loadMore) {
      let that = this;
      that.wholePageLoading = true;
      return new Promise((resolve, reject) => {
        let params = that.params();
        let deviceType = params.deviceType;
        let isBox = params.deviceType == 1;
        let isEnv = params.deviceType == 5;
        that.pageSize = loadMore ? 12 : undefined;
        let { url, pageSize, currentPage, group, alarmGrade, deviceSubType, psInfo } = that;
        let str = JSON.parse(JSON.stringify(group)).join();
        postAction(baseUrl + url.alarmList, {
          pageSize,
          currentPage: loadMore ? currentPage : undefined,
          alarmGrade: isEnv ? undefined : alarmGrade,
          ...params,
          deviceSubType: isBox ? deviceSubType : undefined,
          stringStatus: ((isBox && deviceSubType == 15) || deviceType == 4) ? str : undefined,
          ...psInfo
        }).then((res) => {
          let data = res.result_data || [];
          resolve(data);
          that.wholePageLoading = false;
        }).catch((err) => {
          reject(err);
          that.wholePageLoading = false;
        });
      });
    },
    // 组串状态全选时 需要分页处理
    setLoadMore () {
      return true;
      // 不确定后续需求是否还需要这样修改，代码先注释保留
      // let group = this.group;
      // return group && group.length == 4;
    },
    // 下拉加载
    refreshListData (loadMore) {
      this.$emit('loaded', true);
      this.currentPage++;
      let pageSize = this.pageSize;
      this.getBoxAlarmList(true).then(values => {
        this.list = this.list.concat(values);
        let length = values.length;
        if (length < pageSize) {
          loadMore.complete();
          if (length != 0) {
            loadMore.loaded();
          }
        } else {
          loadMore.loaded();
        }
        // loadMore.loaded();
        // if (length < pageSize) {
        //   loadMore.complete();
        // }
        this.$emit('loaded', false);
      }).catch(() => {
        this.currentPage--;
        // loadMore.complete();
        this.$emit('loaded', false);
      });
    },
    // 查询、重置
    initData (flag = false) {
      this.loadMore = false;
      this.currentPage = 0;
      this.infiniteId += 1;
      if (!flag) {
        this.alarmGrade = '';
      }
      this.list = [];
      this.deviceSubType = this.isShowInverterType() == 15 ? 15 : 14;
      this.params().deviceType != 5 && this.getInverterAlarmCount();
      let loadMore = this.setLoadMore();
      if (loadMore) {
        this.loadMore = true;
      } else {
        this.$emit('loaded', true);
        this.getBoxAlarmList(loadMore).then(values => {
          this.list = [...values];
          this.$emit('loaded', false);
        }).catch(() => {
          this.$emit('loaded', false);
        });
      }
    },
    // 定时刷新
    refreshData () {
      this.$emit('loaded', true);
      this.params().deviceType != 5 && this.getInverterAlarmCount();
      let loadMore = this.setLoadMore();
      this.getBoxAlarmList(loadMore).then(values => {
        if (loadMore) {
          let list = this.list;
          if (list.length) {
            list.forEach((item, i) => {
              let obj = values.find(o => o.ps_key == item.ps_key);
              if (obj) {
                this.list[i] = obj;
              }
            });
          } else {
            this.list = [...values];
          }
        } else {
          this.list = [...values];
        }
        this.$emit('loaded', false);
      }).catch(() => {
        this.$emit('loaded', false);
      });
    },
    getInverterAlarmCount () {
      if (!this.url.count) {
        return;
      }
      let params = this.params();
      let deviceType = params.deviceType;
      let str = JSON.parse(JSON.stringify(this.group)).join();
      postAction(baseUrl + this.url.count, {
        ...this.params(),
        deviceSubType: this.params().deviceType == 1 ? this.deviceSubType : undefined,
        stringStatus: ((deviceType == 1 && this.deviceSubType == 15) || deviceType == 4) ? str : undefined,
        ...this.psInfo
      })
        .then((res) => {
          this.alarmNumList = res.result_data;
        })
        .catch((res) => {});
    },
    /** 测点排序
     * params {arr} 测点对象
     * params {num} type: Boolean true 前12个测点， 后12个测点
     */
    getStrings (arr, num) {
      let obj = {};
      if (!arr) return [];
      Object.keys(arr).sort((a, b) => Number(b) - Number(a)).map(item => {
        obj[item] = arr[item];
      });
      let length = Object.values(obj).length;
      return num ? Object.values(obj).slice(0, 16) : Object.values(obj).slice(16, length > 32 ? 32 : length);
    },
    alarmGradeChange (val, sub) {
      this.initData(true);
    },
    groupChange () {
      this.initData(true);
    },
    getGradePng (grade) { // 目前先写死
      // if (!grade) return;
      // if (grade == '4') {
      //   grade = '3';
      // }
      return require('@/assets/images/health/alarmEvents/grade1.png');
    },
    isExistValue (str, is100) {
      let isStr = str || str == 0 ? str : '--';
      return isStr && is100 ? Math.round(isStr * 1000) : isStr;
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.card-spin-center {
  margin-left: 14px;
}
.device {
  align-items: flex-start;
  .content_header {
    margin-bottom: 10px;
  }
  .radio-div {
    margin-left: 6px;
  }
  .b-box {
    width: 512px;
    height: 220px;
    margin: 6px 10px 10px 6px;
    header {
      .alarm-type-img {
        position: absolute;
        right: 12px;
        height: 22px;
      }
      .alarm-type {
        position: absolute;
        right: 28px;
        font-size: 12px;
      }
      .device_name {
        width: 354px;
      }
    }
    section  {
      .left {
        width: 174px;
      }
    }
    .right {
      // margin-left:28px;
      margin-top: 8px;
    }
  }
  .content {
    flex-wrap: wrap;
    overflow: auto;
    align-content: flex-start;
     height: calc(100vh - 435px);
    .circuit {
      margin-bottom: 16px;
      position: relative;
      z-index: 1;
    }
  }
}
:deep(.infinite-status-prompt) {
  text-align: center;
}
</style>
