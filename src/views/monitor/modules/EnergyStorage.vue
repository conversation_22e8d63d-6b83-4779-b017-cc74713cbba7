<!--储能单元-->
<template>
  <a-spin size="large" :spinning="wholePageLoading" class="card-spin-center">
    <div class="device">
      <!-- <section>
        <a-radio-group v-model="alarmGrade" @change="alarmGradeChange" size="default">
          <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
          <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
            >{{ item.gradeName }}({{ item.count }})</a-radio-button
          >
        </a-radio-group>
      </section> -->
      <div class="content width_100">
        <template v-for="(item,index) in list">
          <div class="b-box" :key="item.ps_key">
            <header class="deviceName">
              <div :title="item.device_name" class="device_name_lable over-flow">
                {{ item.deviceName }}
              </div>

              <a-dropdown placement="bottomLeft" :trigger="['hover']" @visibleChange="(value)=>visibleChange(value, item, index)">
                <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                  <div class="next_device" style="visibility:visible;top:8px;"></div>
                </a>
                <a-menu slot="overlay">
                 <a-menu-item v-if="item.loading"><a-spin :spinning="item.loading"></a-spin></a-menu-item>
                    <a-menu-item v-if="item.option&&item.option.indexOf('99') > -1">
                      <span @click="changeDevice(( { name: '箱变', value: 'BoxChange', type: '99', deviceSubType: 4 }), Object.assign(item,{'device_type': 47}))">
                       箱变
                      </span>
                    </a-menu-item>
                <a-menu-item v-if="item.option&&item.option.indexOf('37') > -1">
                      <span @click="changeDevice((  { value: 'CurrentConverter', name: '变流器', type: 37 }), Object.assign(item,{'device_type': 47}))">
                       变流器
                      </span>
                    </a-menu-item>
                     <a-menu-item v-if="item.option&&item.option.indexOf('23') > -1">
                      <span @click="changeDevice((  { value: 'BMS', name: '系统BMS', type: 23 }), Object.assign(item,{'device_type': 47}))">
                       系统BMS
                      </span>
                    </a-menu-item>
                </a-menu>
              </a-dropdown>
              <!-- <template v-else  v-for="(v, k) in optionList">
               <div class="next_device" :key="k" v-if="option.indexOf(v.type) > -1">
                <span @click="changeDevice(v, item)" >
                  {{ item.name }}
                </span>
              </div>
              </template> -->

            </header>
            <section>
              <div class="left"><img src="@/assets/images/monitor/device/energy.png" /></div>
              <div class="right">
                <div class="desc">
                  <div class="desc-left">有功功率({{ dealData(item.p473003, 'unit') }})：</div>
                  <div class="desc-right" :title="dealData(item.p473003,'title')">{{ dealData(item.p473003) }}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">SOC({{ dealData(item.p473101, 'unit') }})：</div>
                  <div class="desc-right" :title="dealData(item.p473101,'title')">{{ dealData(item.p473101) }}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">日充电量({{ dealData(item.p473008, 'unit') }})：</div>
                  <div class="desc-right" :title=" dealData(item.p473008,'title')">{{ dealData(item.p473008) }}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">日放电量({{ dealData(item.p473009, 'unit') }})：</div>
                  <div class="desc-right" :title="dealData(item.p473009,'title')">{{ dealData(item.p473009) }}</div>
                </div>
              </div>
            </section>
          </div>
        </template>
        <infinite-loading :identifier="infiniteId" v-if="list.length >= pageSize" @infinite="getList">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
             <p class="no-data"></p>
             <p class="no-data-text">无更多数据</p>
          </div>
        </infinite-loading>
        <div class="infinite-loading-container" v-if="list.length == 0 && !wholePageLoading">
          <p class="no-data"></p>
          <p class="no-data-text">暂无数据</p>
        </div>
      </div>
    </div>
  </a-spin>
</template>
<script>
import deviceType from '../mixins/deviceType';
import { getDownDeviceType } from '@/api/monitor/device';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {
        count: '',
        alarmList: '/monitorCn/getCnUnitsListInfo'
      },
      optionList: [
        { name: '箱变', value: 'BoxChange', type: '99', deviceSubType: 4 },
        { value: 'CurrentConverter', name: '变流器', type: '37' },
        { value: 'BMS', name: '系统BMS', type: '23' }
      ]
    };
  },
  created () {
    // this.getDownDeviceType();
  },
  methods: {
    dealData (data, isUnit) {
      if (!data) {
        return;
      }
      let isInUnit = data.indexOf(',') > -1;
      let dataNum = isInUnit ? data.split(',') : data;
      let isArr = Array.isArray(dataNum);
      let dealData = isArr && dataNum[0] ? dataNum[0] : data;
      let isTrue = dealData || dealData == 0;
      let str = String(dealData).length > 9 && !isUnit ? String(dealData).slice(0, 7) + '...' : dealData;
      if (isUnit == 'unit') {
        return isArr && dataNum.length > 1 ? dataNum[1] : 'x';
      } else {
        return isTrue ? str : '--';
      }
    },
    getDownDeviceType (uuid, index) {
      this.$set(this.list[index], 'option', []);
      getDownDeviceType({
        psId: this.params().psId,
        upUuid: uuid
      }).then((res) => {
        this.$set(this.list[index], 'option', res.result_data || []);
        this.list[index].loading = false;
      }).catch(() => {
        this.list[index].loading = false;
      });
    },
    changeDevice (obj, item) {
      this.$emit('select', obj, item);
    },
    visibleChange (value, item, index) {
      if (!item.option && value) {
        this.$set(this.list[index], 'loading', true);
        this.getDownDeviceType(item.uuid, index);
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
  align-items: flex-start;
  .b-box {
    width: 382px;
    height: 210px;
    margin: 0 16px 16px 0;
  }
  .content {
    flex-wrap: wrap;
    height: calc(100vh - 410px);
    overflow: auto;
    align-content: flex-start;
    margin-top: 16px;
  }
  .b-box {
    section {
      padding: 8px 16px 8px 0;
      .left {
        width: 140px;
        margin-right: 16px;
      }
      .right {
        flex-direction: column;
        padding: 16px;
        justify-content: space-between;
        width: 178px;
        height: 132px;
        background: rgba(00, 00, 00, 0.19);
        border-radius: 8px;
        .desc {
          display: flex;
          color: white;
        }
          .desc-left {
          white-space: nowrap;
          flex: 1;
        }
        .desc-right {
          width: 80px;
        }
      }
    }
  }
}
</style>
