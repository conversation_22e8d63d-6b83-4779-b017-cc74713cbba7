<!--逆变器-->
<template>
 <a-spin size="large" :spinning="wholePageLoading" class="spin-content">
  <div class="device">
    <div class="content_header" >
      <!--需要判断是否显示-->
      <div>
        <a-radio-group class="radio-div" v-model="deviceSubType" @change="(val)=>alarmGradeChange(val, 'sub')"  v-if="isShowInverterType()==2">
        <a-radio :value="14" >集中式</a-radio>
        <a-radio :value="15">组串式</a-radio>
      </a-radio-group>
      <a-radio-group class="radio-div" v-model="alarmGrade" @change="(val)=>alarmGradeChange(val, null)" size="default">
        <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
        <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
          >{{ item.gradeName }}({{ item.count }})</a-radio-button
        >
      </a-radio-group>
      </div>
        <div class="right" v-if="deviceSubType==15">
        <a-checkbox-group v-model="group" @change="groupChange">
          <template v-for="item in stringsList">
          <a-checkbox :key="item.grade" :value="item.grade" :class="['point_alarm_check_'+ item.grade]">{{item.gradeName}}</a-checkbox>
        </template>
         <a-checkbox value="6" class="point_alarm_check_6">
          未接组串
        </a-checkbox>
        </a-checkbox-group>
      </div>
    </div>
    <section class="width_100">
      <div class="content" :style="{height:  'calc(100vh - 365px)'}">
        <div class="circuit" :key="item.ps_key" v-for="item in list" :class="{'group_string': deviceSubType==15}">
          <div class="b-box" :class="{ 'string-box' : deviceSubType == 15 }">
            <header>
              <div class="device_name" :title="item.device_name">{{ item.device_name }}
                <img
                  :src="require(`@/assets/images/monitor/device/alarm_type_${item.grade ? item.grade : 5}.png`)"
                  alt=""
                  srcset=""
                  class="alarm-type-img"
                />
                <div class="alarm-type" :class="['alarm_type_'+ item.grade]">{{item.gradeName}}</div>
              </div>
              <div class="next_device" v-show="deviceSubType==14 && isShowLink(item)" @click="changeDevice({value:'CombinerBox', name:'汇流箱', type: 4},item)"></div>
            </header>
            <section class="inverter">
              <div class="left flex-center"><img src="@/assets/images/monitor/device/inverter_s.png" alt="" srcset="" /></div>
              <div class="right" v-if="deviceSubType==15">
                <div class="right-desc">
                  <template v-for="child in getStrings(item.strings, true)"
                    ><div :key="child.point" :class="['point_alarm_type_'+ child.status]">
                      {{ child.status == '6' ? '' : child.value }}
                    </div></template
                  >
                </div>
                <div class="right-desc">
                  <template v-for="child in getStrings(item.strings, false)"
                    ><div :key="child.point" :class="['point_alarm_type_'+ child.status]">
                      {{ child.status == '6' ? '' : child.value }}
                    </div></template
                  >
                </div>
              </div>
              <div class="right-box" v-if="deviceSubType==14">
                <div class="reason_title" v-if="item.allReason">
                    <img class="grade-image" v-show="item.allReason" :src="getGradePng(item.alarmGrade)" />
                  <div class="alarm_name flex-center" :title="item.allReason">
                    {{ item.allReason }}
                    <div :class="{'alarm_name_bg': item.allReason}"></div>
                  </div>
                </div>
                <div class="right-content">
                  <div>
                    <div class="text">装机容量(kWp)：</div>
                    <div class="text">交流功率(kW)：</div>
                    <div class="text">日发电量(kWh)：</div>
                    <div class="text">日等效利用小时(h)：</div>
                  </div>
                  <div class="value-div">
                    <div class="values">
                      <div class="value" :title="item.accessCapacity">{{item.accessCapacity}}</div>
                      <div class="value" :title="item.acPower">{{item.acPower}}</div>
                      <div class="value" :title="item.dailyPowerGeneration">{{item.dailyPowerGeneration}}</div>
                      <div class="value" :title="item.usingHours">{{item.usingHours}}</div>
                    </div>
                  </div>
                </div>
                <div class="device_bottom">
                  <span class="anomaly-title">下级状态：</span>
                  <div class="anomaly"></div>
                  异常
                  <span class="anomaly_number">{{ item.downAlarmDeviceCount }}</span>
                  <div class="unconnected"></div>
                  正常
                  <span class="unconnected_number">  {{ item.downDisConnectDeviceCount }}</span>
                </div>
              </div>
              <div class="inverter_desc" v-if="deviceSubType==15">
                <div class="reason_title" v-if="item.allReason" style="padding-top: 16px;">
                     <img class="grade-image" v-show="item.allReason" :src="getGradePng(item.alarmGrade)" />
                    <div class="alarm_name" :title="item.allReason">
                      {{ item.allReason }}
                      <div :class="{'alarm_name_bg': item.allReason}"></div>
                    </div>
                </div>
                <div class="device_desc" :style="{paddingTop: !item.allReason ?'16px':''}">
                  <div class="params">
                    <span class="left">装机容量(kWp)：</span>
                    <span class="right">{{ item.accessCapacity }}</span>
                  </div>
                  <div class="params">
                    <span class="left">交流功率(kW)：</span>
                    <span class="right">{{ item.acPower }}</span>
                  </div>
                  <div class="params">
                    <span class="left">日发电量(kWh)：</span>
                    <span class="right">{{ item.dailyPowerGeneration }}</span>
                  </div>
                  <div class="params">
                    <span class="left" >日等效利用小时(h)：</span>
                    <span class="right">{{ item.usingHours }}</span>
                  </div>
                </div>

                <div class="device_bottom" v-show="deviceSubType==14">
                  <span class="anomaly-title">下级状态：</span>
                  <div class="anomaly"></div>
                  异常
                  <span class="anomaly_number">{{ item.downAlarmDeviceCount }}</span>
                  <div class="unconnected"></div>
                  正常
                  <span class="unconnected_number">  {{ item.downDisConnectDeviceCount }}</span>
                </div>
              </div>
            </section>
          </div>
        </div>
        <infinite-loading :identifier="infiniteId" v-if="loadMore" @infinite="refreshListData">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
             <p class="no-data-text">无更多数据</p>
          </div>
          <div slot="no-results" v-if="!list.length">
           <p class="no-data"></p>
            <p class="no-data-text">暂无数据</p>
          </div>
        </infinite-loading>

        <div class="infinite-loading-container" v-if="!loadMore && !wholePageLoading && !list.length">
          <p class="no-data"></p>
          <p class="no-data-text">暂无数据</p>
        </div>
      </div>
    </section>
  </div>
   </a-spin>
</template>
<script>
import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// import InfiniteLoading from 'vue-infinite-loading'; // 滚动加载插件
// import deviceType from '../mixins/deviceType';
export default {
  components: { InfiniteLoading },
  inject: ['params', 'isShowInverterType'],
  // components: { InfiniteLoading },
  // mixins: [deviceType],
  props: {
    psInfo: {
      default: () => {
        return {
          psKey: undefined, uuid: undefined
        };
      }
    }
  },
  data () {
    return {
      url: {
        count: '/monitorNew/getInverterAlarmCount',
        alarmList: '/monitorNew/getInverterAlarmList'
      },
      loadMore: false,
      infiniteId: +new Date(),
      noDataImg: require('@/assets/images/public/no-data.png'), // 暂无数据图片
      wholePageLoading: false,
      currentPage: 0,
      alarmGrade: '',
      deviceSubType: '', // 14 集中式 // 15 组串式
      deviceModel: '',
      alarmNumList: [
      ],
      pageSize: undefined,
      group: ['1', '4', '5', '6'],
      list: [],
      stringsList: [
        {
          'gradeName': '故障组串',
          'grade': '1'
        },
        {
          'gradeName': '低效组串',
          'grade': '4'
        },
        {
          'gradeName': '正常组串',
          'grade': '5'
        }
      ]
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    },
    activeTotal () {
      return this.alarmNumList ? this.alarmNumList.reduce((a, b) => {
        a = a + b.count;
        return a;
      }, 0) : 0;
    }
  },
  created () {
    this.initData();
  },
  mounted () {
    let timer = setInterval(() => {
      this.refreshData(); // 获取可视区域内的dom节点
    }, 1000 * 5 * 60);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      timer = null;
    });
    this.$once('hook:deactivated', () => {
      clearInterval(timer);
      timer = null;
    });
  },
  methods: {
    // 数据请求
    getInverterAlarmList (loadMore) {
      this.wholePageLoading = true;
      let params = this.params();
      let { currentPage, alarmGrade, group, deviceSubType, psInfo } = this;
      this.pageSize = loadMore ? (deviceSubType == 14 ? 32 : 12) : undefined;
      return new Promise((resolve, reject) => {
        let deviceType = params.deviceType;
        let isBox = params.deviceType == 1;
        let isEnv = params.deviceType == 5;
        postAction(baseUrl + this.url.alarmList, {
          currentPage,
          ...params,
          ...psInfo,
          pageSize: this.pageSize,
          alarmGrade: isEnv ? undefined : alarmGrade,
          deviceSubType: isBox ? deviceSubType : undefined,
          stringStatus: ((isBox && deviceSubType == 15) || deviceType == 4) ? group.join() : undefined
        }).then((res) => {
          let data = res.result_data || [];
          resolve(data);
          this.wholePageLoading = false;
        }).catch((err) => {
          reject(err);
          this.wholePageLoading = false;
        });
      });
    },
    // 定时刷新
    refreshData () {
      this.$emit('loaded', true);
      this.params().deviceType != 5 && this.getInverterAlarmCount();
      let loadMore = this.setLoadMore();
      this.getInverterAlarmList(loadMore).then(values => {
        if (loadMore) {
          let list = this.list;
          if (list.length) {
            list.forEach((item, i) => {
              let obj = values.find(o => o.ps_key == item.ps_key);
              if (obj) {
                this.list[i] = obj;
              }
            });
          } else {
            this.list = [...values];
          }
        } else {
          this.list = [...values];
        }
        this.$emit('loaded', false);
      }).catch(() => {
        this.$emit('loaded', false);
      });
    },
    // 下拉加载
    refreshListData (loadMore) {
      this.$emit('loaded', true);
      this.currentPage++;
      this.getInverterAlarmList(true).then(values => {
        let pageSize = this.pageSize;
        this.list = this.list.concat(values);
        let length = values.length;
        if (length < pageSize) {
          loadMore.complete();
          if (length != 0) {
            loadMore.loaded();
          }
        } else {
          loadMore.loaded();
        }
        // loadMore.loaded();
        // if (length < pageSize) {
        //   loadMore.complete();
        // }
        this.$emit('loaded', false);
      }).catch(() => {
        this.currentPage--;
        // loadMore.complete();
        this.$emit('loaded', false);
      });
    },
    // 查询、重置
    initData (flag = false) {
      this.loadMore = false;
      this.infiniteId += 1;
      this.currentPage = 0;
      if (!flag) {
        this.alarmGrade = '';
        this.deviceSubType = this.isShowInverterType() == 15 ? 15 : 14;
      }
      this.list = [];
      this.params().deviceType != 5 && this.getInverterAlarmCount();
      let loadMore = this.setLoadMore();
      if (loadMore) {
        this.loadMore = loadMore;
      } else {
        this.$emit('loaded', true);
        this.getInverterAlarmList(loadMore).then(values => {
          this.list = [...values];
          this.$emit('loaded', false);
        }).catch(() => {
          this.$emit('loaded', false);
        });
      }
    },
    getInverterAlarmCount () {
      if (!this.url.count) {
        return;
      }
      let params = this.params();
      let deviceType = params.deviceType;
      let str = JSON.parse(JSON.stringify(this.group)).join();
      postAction(baseUrl + this.url.count, {
        ...this.params(),
        deviceSubType: this.params().deviceType == 1 ? this.deviceSubType : undefined,
        stringStatus: ((deviceType == 1 && this.deviceSubType == 15) || deviceType == 4) ? str : undefined,
        ...this.psInfo
      })
        .then((res) => {
          this.alarmNumList = res.result_data;
        })
        .catch((res) => {});
    },
    /** 测点排序
     * params {arr} 测点对象
     * params {num} type: Boolean true 前12个测点， 后12个测点
     */
    getStrings (arr, num) {
      let obj = {};
      if (!arr) return [];
      Object.keys(arr).sort((a, b) => Number(b) - Number(a)).map(item => {
        obj[item] = arr[item];
      });
      let length = Object.values(obj).length;
      return num ? Object.values(obj).slice(0, 16) : Object.values(obj).slice(16, length > 32 ? 32 : length);
    },
    // 逆变器：集中式 或 组串式，并且组串状态全选时，汇流箱：组串状态全选时 需要分页处理
    setLoadMore () {
      let { deviceSubType, group } = this;
      return deviceSubType == '14' || (deviceSubType == '15' && group && group.length == 4);
    },
    alarmGradeChange (val, sub) {
      // if (sub) {
      //   this.getInverterAlarmCount();
      // }
      this.initData(true);
    },
    // 组串状态change事件
    groupChange () {
      this.initData(true);
    },
    getGradePng (grade) { // 目前先写死
      // if (!grade) return;
      // if (grade == '4') {
      //   grade = '3';
      // }
      return require('@/assets/images/health/alarmEvents/grade1.png');
    },
    isExistValue (str, is100) {
      let isStr = str || str == 0 ? str : '--';
      return isStr && is100 ? Math.round(isStr * 1000) : isStr;
    },
    changeDevice (obj, item) {
      this.$emit('select', obj, item);
    },
    isShowLink (item) {
      return Number(item.downAlarmDeviceCount) + Number(item.downDisConnectDeviceCount);
    }
  }

};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
  align-items: flex-start;
  .content_header {
    margin-bottom: 10px;
  }
  .b-box {
    height: 258px;
    width: 380px;
    margin: 6px 0 0 6px;
    .right {
      align-self: flex-start;
      margin-top: 36px;
    }
    header {
      .alarm-type-img {
        right: 52px;
        top: 12px;
      }
      .alarm-type {
        top: 14px;
        right: 64px;
      }
      .device_name {
        width: 184px;
      }
    }
    section {
      .left {
        width: 136px;
      }
    }
  }
  .string-box {
    width: 512px;
    header {
      .alarm-type-img {
        right: 12px;
      }
      .alarm-type {
        right: 26px;
      }
    }
  }
  .content {
    flex-wrap: wrap;
    height: calc(100vh - 435px);
    overflow: auto;
    align-content: flex-start;
    .circuit {
      margin-right: 16px;
      margin-bottom: 16px;
      position: relative;
      z-index: 1;
    }
  }
  .inverter {
    position: relative;
    .inverter_desc {
      background: linear-gradient(222deg, rgba(44, 71, 100, 1) 0%, rgba(24, 34, 48, 1) 81%, rgba(24, 34, 48, 1) 100%);
      box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
      border-radius: 4px;
      border: 2px solid;
      border-image: linear-gradient(129deg, rgba(84, 119, 166, 1), rgba(175, 175, 175, 0), rgba(29, 69, 122, 1)) 2 2;
      backdrop-filter: blur(8px);
      display: flex;
      flex-direction: column;
      position: absolute;
      z-index: 2;
      width: 210px;
      height: 205px;
      top: -48px;
      left: -15px;
      display: none;
      .params {
        display: flex;
        height: 26px;
        line-height: 26px;
        .left {
          width: 70%;
        }
        .right {
          flex:1;
          min-width: auto;
          margin-top:0;
        }
      }
    }
    &:hover {
      .inverter_desc {
        display: flex;
      }
    }
    .device_desc {
      flex: 1;
    }
  }
  // .circuit:hover +.circuit {
  //   z-index: -1;
  // }
  .circuit:nth-child(8n+1):hover {
    .inverter_desc {
      left: -2px;
    }
  }
  .group_string:nth-child(3n+1):hover {
     .inverter_desc {
      left: -2px;
    }
  }
  @media (max-width: 1800px) {
    .circuit:nth-child(7n+1):hover {
    .inverter_desc {
      left: -2px;
    }
  }
   .group_string:nth-child(2n+1):hover {
     .inverter_desc {
      left: -2px;
    }
  }
  }
   @media (max-width: 1600px) {
    .circuit:nth-child(6n+1):hover {
    .inverter_desc {
      left: -2px;
    }
  }
  }
    @media (max-width: 1280px) {
    .circuit:nth-child(4n+1):hover {
    .inverter_desc {
      left: -2px;
    }
       .group_string:nth-child(n+1):hover {
     .inverter_desc {
      left: -2px;
    }
  }
  }
  }
    @media (min-width: 2000px) {
    .circuit:nth-child(9n+1):hover {
    .inverter_desc {
      left: -2px;
    }
  }
  }
     @media (min-width: 2400px) {
    .circuit:nth-child(10n+1):hover {
    .inverter_desc {
      left: -2px;
    }
  }
  }
}
</style>
<style lang="less">
:root[data-theme='dark'] {
  .ant-radio-inner {
    background: transparent;
  }
  .ant-radio-checked .ant-radio-inner {
    border-color: #267dcf;
  }
  .ant-radio-wrapper:hover .ant-radio .ant-radio-inner,
  .ant-radio:hover .ant-radio-inner,
  .ant-radio-focused .ant-radio-inner {
    border-color: #267dcf;
  }
}
</style>
