<template>
  <div class="content">
    <template v-for="child in list">
      <div class="circuit" :key="child.id">
        <div class="vertical-line" :key="child.id" v-if="!isTop"></div>
        <a-popover overlayClassName="device">
          <template slot="content">
            <div class="reason_title" v-if="child.allReason">
              <img class="grade-image" v-show="child.allReason" src="@/assets/images/health/alarmEvents/grade1.png" />
              <div class="alarm_name" :title="child.allReason">
                {{ child.allReason }}
                <div :class="{ alarm_name_bg: child.allReason }"></div>
              </div>
            </div>

            <div class="popover-bg">
              <template v-if="child.device_type == 6 && child.device_sub_type == '1'">
                <template
                  v-if="
                    isExist(child.pointList1) ||
                    isExist(child.pointList2) ||
                    isExist(child.pointList3) ||
                    isExist(child.pointList4)
                  "
                >
                  <section v-if="child.pointList1">
                    <div class="title">主变</div>
                    <template v-for="(item, itemKey) in child.pointList1">
                      <div class="params" :key="item">
                        <span class="left width_120" :title="itemKey"
                          ><div>{{ itemKey }}</div>
                          <div>：</div></span
                        >
                        <span class="right"> {{ item }}</span>
                      </div>
                    </template>
                  </section>
                  <section v-if="child.pointList2">
                    <div class="title">高压侧</div>
                    <template v-for="(item, itemKey) in child.pointList2">
                      <div class="params" :key="item">
                        <span class="left width_120" :title="itemKey"
                          ><div>{{ itemKey }}</div>
                          <div>：</div></span
                        >
                        <span class="right"> {{ item }}</span>
                      </div>
                    </template>
                  </section>
                  <section>
                    <div class="title" v-if="child.pointList3">低压侧绕组1</div>
                    <template v-for="(item, itemKey) in child.pointList3">
                      <div class="params" :key="item">
                        <span class="left width_120" :title="itemKey"
                          ><div>{{ itemKey }}</div>
                          <div>：</div></span
                        >
                        <span class="right"> {{ item }}</span>
                      </div>
                    </template>
                  </section>
                  <section v-if="child.pointList4">
                    <div class="title">低压侧绕组2</div>
                    <template v-for="(item, itemKey) in child.pointList4">
                      <div class="params" :key="item">
                        <span class="left width_120" :title="itemKey"
                          ><div>{{ itemKey }}</div>
                          <div>：</div></span
                        >
                        <span class="right"> {{ item }}</span>
                      </div>
                    </template>
                  </section>
                </template>
                <div v-else class="popover-bg" style="padding-bottom: 16px; padding-left: 24px; font-size: 16px">
                  <section>暂无测点数据</section>
                </div>
              </template>
              <template v-else>
                <section v-if="isExist(child.pointList)">
                  <template v-for="(item, itemKey) in child.pointList">
                    <div class="params" :key="item">
                      <span class="left width_120" :title="itemKey"
                        ><div>{{ itemKey }}</div>
                        <div>：</div></span
                      >
                      <span class="right"> {{ item }}</span>
                    </div>
                  </template>
                </section>
                <div v-else class="popover-bg" style="padding-bottom: 16px; padding-left: 24px; font-size: 16px">
                  <section>暂无测点数据</section>
                </div>
              </template>
            </div>

            <div class="device_bottom" v-if="child.device_sub_type == 8 && (child.downAlarmDeviceCount>0 ||child.downDisConnectDeviceCount >0 )">
              <span class="anomaly-title">下级状态：</span>
              <div class="anomaly"></div>
              异常
              <span class="anomaly_number">{{ child.downAlarmDeviceCount }}</span>
              <div class="unconnected"></div>
              正常
              <span class="unconnected_number"> {{ child.downDisConnectDeviceCount }}</span>
            </div>
          </template>
          <div class="b-box">
            <header>
              <div class="ellipsis" :title="child.device_name">{{ child.device_name }}</div>
            </header>
            <section>
              <img
                :src="
                  require(`@/assets/images/monitor/device/${getImage(child.device_sub_type, child.device_type)}.png`)
                "
                class="img_s"
              />
            </section>
            <footer>
              <img :src="require(`@/assets/images/monitor/device/alarmType_${child.grade}.png`)" alt="" srcset="" />
              <div :class="['alarm_type_' + child.grade]">{{ child.gradeName }}</div>
            </footer>
          </div>
        </a-popover>
        <div class="vertical-line" :key="child.id" v-if="child.children && child.children.length > 0"></div>
        <section v-if="child.children && child.children.length > 0" :key="child.id">
          <!-- <div class="line" :style="{ width: child.children.length * 184 - 6 + 'px' }"></div> -->
          <div style="position: relative;left:-40px">
            <span
              v-if="filterPT(child.children, true).length > 0"
              class="bus_bar"
              :style="{ width: filterPT(child.children, true)[0].device_name.length * 14 + 'px' }"
              >{{ filterPT(child.children, true)[0].device_name }}</span
            >
            <div class="line" style="flex: 1"></div>
          </div>

          <boost-detail :list="filterPT(child.children)"></boost-detail>
        </section>
      </div>
    </template>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      }
    },
    isTop: {
      type: Boolean,
      default: false
    }
  },
  name: 'BoostDetail',
  methods: {
    initData () {},
    isExist (pointList) {
      return pointList && Object.keys(pointList).length > 0;
    },
    getList () {},
    //  getWidth () {
    //   return this.list.length > 0 ? this.list[0].children.reduce((a, b) => {
    //     a = a + (b.children ? b.children.length * 184 : 184);
    //     return a;
    //   }, 0) : 0;
    // },
    filterPT (children, isSub6) {
      return children.filter((item) => {
        if (isSub6) {
          return item.device_sub_type == 6;
        } else {
          return item.device_sub_type != 6;
        }
      });
    },
    getImage (subType, type) {
      let caseType = subType || type;
      let name = '';
      switch (Number(caseType)) {
        case 1: // 主变
          name = 'boostVoltage';
          break;
        case 2:
        case 3:
          name = 'groundChange';
          break;
        case 4:
          name = 'box';
          break;
        case 7:
        case 8:
        case 9:
        case 10:
        case 11:
          name = 'boot_circut';
          break;
        case 12:
        case 13:
        case 29:
          name = 'SVG';
          break;
        default:
          name = 'boot_circut';
          break;
      }
      return name;
    }
  },
  created () {}
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.content {
  padding-left: 16px;
  width: 100%;
  justify-content: center;
}
.img_s {
  width: 100%;
  height: 100%;
  max-width: 85%;
  max-height: 85%;
}
.bus_bar {
  position: absolute;
  text-align: center;
  line-height: 48px;
  top: -22px;
  height: 48px;
  background: linear-gradient(
    222deg,
    rgba(6, 8, 15, 0.45) 0%,
    rgba(34, 42, 71, 0.35) 42%,
    rgba(136, 166, 205, 0.25) 100%
  );
  box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
  border: 2px solid;
  border-image: linear-gradient(129deg, rgba(84, 119, 166, 1), rgba(175, 175, 175, 0), rgba(29, 69, 122, 1)) 2 2;
  backdrop-filter: blur(14px);
}
</style>
