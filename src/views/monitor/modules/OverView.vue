<template>
<div class="left">
      <header>电站概览</header>
      <section>装机容量（{{ dataExchange(headData.accessCapacity, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :startVal="0"
          :decimals="dynamicDecimals(dataExchange(headData.accessCapacity, 'data'))"
          :endVal="dataExchange(headData.accessCapacity, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <header>电站实况</header>
      <section>实时功率（{{ dataExchange(headData.realTimePower, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :startVal="0"
          :decimals="dynamicDecimals(dataExchange(headData.realTimePower, 'unit'))"
          :endVal="dataExchange(headData.realTimePower, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>倾斜面辐射（{{ dataExchange(headData.radiation, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :startVal="0"
          :decimals="4"
          :endVal="dataExchange(headData.radiation, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <header>电站发电</header>
      <section>日发电量（{{ dataExchange(headData.dailyPowerGeneration, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :decimals="dynamicDecimals(dataExchange(headData.dailyPowerGeneration, 'unit'))"
          :startVal="0"
          :endVal="dataExchange(headData.dailyPowerGeneration, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>日等效利用小时（{{ dataExchange(headData.usingHours, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :decimals="2"
          :startVal="0"
          :endVal="dataExchange(headData.usingHours, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>月发电量（{{ dataExchange(headData.monthPower, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :decimals="dynamicDecimals(dataExchange(headData.yearPower, 'unit'))"
          :startVal="0"
          :endVal="dataExchange(headData.monthPower, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>年发电量（{{ dataExchange(headData.yearPower, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :decimals="dynamicDecimals(dataExchange(headData.yearPower, 'unit'))"
          :startVal="0"
          :endVal="dataExchange(headData.yearPower, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
    </div>
</template>
<script>
import OverView from '../mixins/OverView';
export default {
  data () {
    return {
      headData: {
        deviceType: '1', // 当前设备类型
        accessCapacity: '0,kW', // 接入容量
        dailyPowerGeneration: '0,kW', // 日发电量
        inverterNum: '0,台', // 逆变器数量
        stringBoxNum: '0,台',
        // monthCompletionRate: '--', // 月完成率
        monthPower: '0,万kWh', // 月发电量
        realTimePower: '0,kW', // 实时功率
        radiation: '0,MJ/m²', // 倾斜面辐射
        stationNum: '0,座', // 电站数量
        usingHours: '0,h', // 日等效小时
        // yearCompletionRate: '--', // 年完成率
        yearPower: '0,万kWh', // 年发电量
        percent: 0 // 百分比
      },
      timer: null,
      isCn: false
    };
  },
  mixins: [ OverView ]
};
</script>
<style scoped lang="less">
  .left {
    width: 240px;
    background: linear-gradient(180deg, #1A4A8E 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
    box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
    border-radius: 8px;
    border: 1px solid #255DAE;
    backdrop-filter: blur(15px);
    left: 24px;
    position: absolute;
    z-index: 20;
    height: 94%;
    margin-top: 16px;
    header {
      height: 46px;
      line-height: 46px;
      font-size: 24px;
      font-family: 'YouSheBiaoTiHei';
      background: linear-gradient(90deg, #2862B3 0%, #18488C 96%);
      &:first-child {
        border-radius: 8px 8px 0 0;
      }
      &::before {
        content: ' ';
        border: 2px solid #24ccff;
        margin-right: 16px;
        font-size: 14px;
        vertical-align: bottom;
        height: 15px;
        display: inline-block;
        vertical-align: inherit;
      }
    }
    section {
      margin: 16px 24px 7px;
      color: #D6D6D6;
    }
    header + section {
      margin: 24px 24px 7px;

    }
    h1 {
      height: 34px;
      font-size: 28px;
      font-family: HelveticaNeue-Medium, HelveticaNeue;
      font-weight: 500;
      color: #ffffff;
      line-height: 34px;
      margin-left: 24px;
      margin-bottom: 0;
    }
     @media screen and (min-height: 970px) {
      h1 {
        height: 38px;
        line-height: 38px;
      }

    }
    @media screen and (min-height: 1080px) {
      h1 {
        height: 50px;
        line-height: 50px;
        font-size: 30px
      }
      section {
        font-size: 16px;
      }
    }
  }
</style>
