<template>
  <a-spin size="large" :spinning="wholePageLoading" class="card-spin-center">
    <a-cascader :options="options" popupClassName="rack_bms" placeholder="请选择" change-on-select @change="onChange" style="margin-top: 24px;width:360px">
</a-cascader>
    <div class="device">
      <div class="detail-left">
        <div class="detail-header"><svg-icon iconClass="point1"  style="margin-right:8px" />遥测信息</div>
        <div class="content">
          <template v-for="item in pointList"
            ><div class="left-content" v-if="item.type == 2" :key="item.point">
              <div class="top">{{ item.name }}</div>
              <div class="bottom" :title="item.value">{{ dealData(item.value,'title') }}&nbsp;{{dealData(item.value, 'unit')}}</div>
            </div>
          </template>
        </div>
      </div>
      <div class="detail-right">
        <div class="detail-header">
        <div style="flex:1"><svg-icon iconClass="point2" style="margin-right:8px" />遥信状态</div>
        <div class='header-right'><div class="bg green"></div>0<div class="red"></div>1<div></div>-</div>
        </div>
        <div class="content">
        <template v-for="item in pointList"
            ><div class="left-content" :class="{red: item.value ==1,green: item.value==0,grey:!item.value || item.value !=0 && item.value!=1 }" v-if="item.type == 1" :key="item.point">
              <div class="top">{{ item.name }}</div>
              <!-- <div class="bottom">{{ item.value }}</div> -->
            </div>
          </template>
        </div>
      </div>
    </div>
  </a-spin>
</template>
<script>
import deviceType from '../mixins/deviceType';
import { getRackBMSDetail, getRackBMSTree } from '@/api/monitor/device';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {},
      options: [],
      pointList: []
    };
  },
  created () {
    this.getRackBMSDetail();
    this.getRackBMSTree();
  },
  methods: {
    onChange (value) {
      if (value.length == 3) {
        this.getRackBMSDetail(value[value.length - 1]);
      } else if (value.length == 0) {
        this.getRackBMSDetail();
      }
    },
    getRackBMSDetail (psKey) {
      let params = Object.assign({}, { ...this.psInfo, psId: this.params().psId, currentPage: 1, pageSize: 99999 });
      if (psKey) { params.psKey = psKey; }
      getRackBMSDetail(params).then((res) => {
        this.pointList = res.result_data;
      });
    },
    getRackBMSTree () {
      getRackBMSTree({ psId: this.params().psId }).then((res) => {
        this.options = this.arrayToTree(res.result_data, '0');
        this.options = this.options[0].children;
        console.log(this.options[0]);
      });
    },
    arrayToTree  (items, minPid) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const uuid = item.uuid;
        const upUuid = item.upUuid;
        if (!itemMap[uuid]) {
          itemMap[uuid] = {
            children: []
          };
        }
        itemMap[uuid] = {
          ...item,
          label: item.deviceName,
          value: item.psKey,
          children: itemMap[uuid]['children']
        };

        const treeItem = itemMap[uuid];

        if (upUuid === minPid) {
          result.push(treeItem);
        } else {
          if (!itemMap[upUuid] || !items.find(ele => ele.uuid == upUuid)) {
            itemMap[upUuid] = {
              children: []
            };
          }
          itemMap[upUuid].children.push(treeItem);
        }
      }
      return result;
    },
    dealData (data, isUnit) {
      if (!data) {
        return;
      }
      let isInUnit = data.indexOf(',') > -1;
      let dataNum = isInUnit ? data.split(',') : data;
      let isArr = Array.isArray(dataNum);
      let dealData = (isArr && dataNum[0]) ? dataNum[0] : data;
      let isTrue = dealData || dealData == 0;
      let str = String(dealData).length > 12 && !isUnit ? (String(dealData).slice(0, 12) + '...') : dealData;
      if (isUnit == 'unit') {
        return isArr && dataNum.length > 1 ? dataNum[1] : 'x';
      } else {
        return isTrue ? str : '--';
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');

.detail-left,
.detail-right {
  width: 49%;
  margin-right: 16px;
}
.detail-header {
  width: 100%;
  height: 58px;
  line-height: 58px;
  background: linear-gradient(90deg, rgba(36, 204, 255, 0.15) 7%, rgba(102, 202, 231, 0.1) 96%);
  padding: 0 16px;
  fontsize: 18px;
  display: flex;
  align-items: center;
  .header-right {
    display: flex;
    width: 120px;
    align-items:center;
    div {
      width: 12px;
      height: 12px;
      border-radius:50%;
      background:#3e3e3e;
      margin:0 6px;
    }
     .green {
        background: #104619;
      }
      .red {
        background: #6a1616;
      }
  }
}
.device {
  align-items: flex-start;
  flex-direction: row;
  .content {
    flex-wrap: wrap;
    height: calc(100vh - 300px);
    overflow: auto;
    align-content: flex-start;
    background: #0e141e;
    padding-top: 16px;
    .left-content,
    .right-content {
      width: 159px;
      height: 94px;
      border-radius: 6px;
      /* 自动布局 */
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 8px 16px;
      background: var(--zw-input-bg-color--disable);
      margin: 0 16px 16px;
      &.green {
        background: var(--zw-proceed-bg-2-color--default);
      }
      &.red {
        background: var(--zw-warning-color--disable);
        color: white;
      }
      &.grey {
        background: #3e3e3e;
      }
    }
    .left-content {
      flex-direction: column;
      .bottom {
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

</style>
