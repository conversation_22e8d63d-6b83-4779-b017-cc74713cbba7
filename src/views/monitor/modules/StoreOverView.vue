<template>
<div class="left" :style="{height: isShowMenu ? 'calc(100vh - 112px - 74px)' : 'calc(100vh - 112px - 74px + 124px)'}">
      <header class="stored">电站概览</header>
      <section>装机规模（{{ dataExchange(headData.ratedPowerCn, 'unit') }}/{{ dataExchange(headData.totalCapcityCn, 'unit') }}）</section>
      <h1>
       <countTo
          class="info-num"
          :style="{fontSize:String(dataExchange(headData.ratedPowerCn, 'data')).length > 4 ||String(dataExchange(headData.totalCapcityCn, 'data')).length > 4 ? '22px': '' }"
          :startVal="0"
          :decimals="2"
          :endVal="dataExchange(headData.ratedPowerCn, 'data')"
          :duration="1000"
        ></countTo>
        <span :style="{fontSize:String(dataExchange(headData.ratedPowerCn, 'data')).length > 4 ||String(dataExchange(headData.totalCapcityCn, 'data')).length > 4 ? '22px': '' }">/</span>
        <countTo
          class="info-num"
           :style="{fontSize:String(dataExchange(headData.totalCapcityCn, 'data')).length > 4 || String(dataExchange(headData.ratedPowerCn, 'data')).length > 4   ? '22px': '' }"
          :startVal="0"
          :decimals="2"
          :endVal="dataExchange(headData.totalCapcityCn, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <header class="stored">电站实况</header>
      <section>有功功率（{{ dataExchange(headData.p370009, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :startVal="0"
          decimals="2"
          :style="{fontSize:String(dataExchange(headData.p370009, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p370009, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>直流功率（{{ dataExchange(headData.p370008, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :startVal="0"
          :decimals="2"
          :style="{fontSize:String(dataExchange(headData.p370008, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p370008, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
       <section>SOC（{{ dataExchange(headData.p230004, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          :startVal="0"
          :decimals="2"
           :style="{fontSize:String(dataExchange(headData.p230004, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p230004, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <header class="stored">电站充放电</header>
      <section>日充电量（{{ dataExchange(headData.p370016, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          decimals="4"
          :startVal="0"
          :style="{fontSize:String(dataExchange(headData.p370016, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p370016, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>日放电量（{{ dataExchange(headData.p370017, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          decimals="4"
          :startVal="0"
          :style="{fontSize:String(dataExchange(headData.p370017, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p370017, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>总充电量（{{ dataExchange(headData.p370014, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          decimals="4"
          :startVal="0"
           :style="{fontSize:String(dataExchange(headData.p370014, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p370014, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
      <section>总放电量（{{ dataExchange(headData.p370015, 'unit') }}）</section>
      <h1>
        <countTo
          class="info-num"
          decimals="4"
          :startVal="0"
          :style="{fontSize:String(dataExchange(headData.p370015, 'data')).length > 9 ? '22px': '' }"
          :endVal="dataExchange(headData.p370015, 'data')"
          :duration="1000"
        ></countTo>
      </h1>
    </div>
</template>
<script>
import OverView from '../mixins/OverView';
export default {
  data () {
    return {
      headData: {
        'totalCapcityCn': '--,W', // 装机功率
        'p370008': '0.01,KW', // 直流功率
        'p370009': '0.01,KW', // 有功功率
        'ratedPowerCn': '--,W', // 额定功率
        'p370017': '2.0,Wh', // 日放
        'p230004': '7.0,%', // soc
        'p370015': '4.0,Wh', // 总放
        'p370016': '1.0,Wh', // 日充
        'p370014': '3.0,Wh'// 总充
      },
      timer: null,
      isCn: true
    };
  },
  mixins: [ OverView ],
  methods: {

  }
};
</script>
<style scoped lang="less">
  .left {
    width: 240px;
    height: calc(100vh - 112px - 74px);
    background: linear-gradient(222deg, rgba(94, 128, 171, 0.25) 0%, rgba(0, 0, 0, 0.35) 27%, rgba(0, 0, 0, 0.45) 100%);
    box-shadow: 0px 6px 10px 0px rgba(16, 8, 2, 0.2);
    border-radius: 4px;
    border: 2px solid;
    border-image: linear-gradient(129deg, rgba(84, 119, 166, 1), rgba(175, 175, 175, 0), rgba(29, 69, 122, 1)) 2 2;
    backdrop-filter: blur(15px);
    top: 24px;
    left: 24px;
    position: absolute;
    z-index: 20;
    header {
      height: 46px;
      line-height: 46px;
      font-size: 24px;
      font-family: 'YouSheBiaoTiHei';
      background: linear-gradient(90deg, rgba(36, 204, 255, 0.3) 0%, rgba(102, 202, 231, 0.1) 100%);
      &:not(:first-child) {
        margin-top: 24px;
      }
      &::before {
        content: ' ';
        border: 2px solid #24ccff;
        margin-right: 16px;
        font-size: 14px;
        vertical-align: bottom;
        border-radius: 0 4px 4px 0;
        height: 15px;
        display: inline-block;
        vertical-align: inherit;
      }

    }
    section {
      margin: 16px 24px 0px;
      color: #D6D6D6;
    }
    header + section {
      margin: 24px 24px 0px;
    }
     header.stored + section {
      margin-top: 16px;
    }
    h1 {
      height: 34px;
      font-size: 28px;
      font-family: HelveticaNeue-Medium, HelveticaNeue;
      font-weight: 500;
      color: #ffffff;
      line-height: 34px;
      margin-left: 24px;
      margin-bottom: 0;
    }
     @media screen and (min-height: 970px) {
      h1 {
        height: 36px;
        line-height: 36px;
      }

    }
    @media screen and (min-height: 1080px) {
      h1 {
        height: 40px;
        line-height: 40px;
        font-size: 30px
      }
      section {
        font-size: 16px;
      }
    }
  }
</style>
