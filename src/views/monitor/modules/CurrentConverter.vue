<!--变流器 -->
<template>
<a-spin size="large" :spinning="wholePageLoading" class="card-spin-center">
  <div class="device">
      <!-- <section>
      <a-radio-group v-model="alarmGrade" @change="alarmGradeChange" size="default">
        <a-radio-button value="">全部({{ activeTotal }})</a-radio-button>
        <a-radio-button v-for="item in alarmNumList" :key="item.grade" :value="item.grade"
          >{{ item.gradeName }}({{ item.count }})</a-radio-button
        >
      </a-radio-group>
    </section> -->
    <div class="content  width_100">
      <template v-for="item in list">
        <div class="b-box" :key="item.ps_key">
          <header class="device_name">
            <div :title="item.deviceName" class="device_name_lable over-flow">
              {{item.deviceName}}
            </div>
          </header>
          <section>
            <div class="left"><img src="@/assets/images/monitor/device/current.png" /></div>
            <div class="right">
              <div class="desc">
                <div class="desc-left">有功功率({{dealData(item.p370009, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p370009,'title')">{{dealData(item.p370009)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">直流功率({{dealData(item.p370008, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p370008,'title')">{{dealData(item.p370008)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">日充电量({{dealData(item.p370016, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p370016,'title')">{{dealData(item.p370016)}}</div>
              </div>
              <div class="desc">
                <div class="desc-left">日放电量({{dealData(item.p370017, 'unit')}})：</div>
                <div class="desc-right" :title="dealData(item.p370017,'title')">{{dealData(item.p370017)}}</div>
              </div>
            </div>
          </section>
        </div>
      </template>
         <infinite-loading :identifier="infiniteId" v-if="list.length >= pageSize" @infinite="getList">
          <div slot="spinner">
            <a-spin size="large" v-if="!wholePageLoading" class="card-spin"></a-spin>
          </div>
          <div slot="no-more">
               <p class="no-data"></p>
             <p class="no-data-text">无更多数据</p>
          </div>
        </infinite-loading>
         <div class="infinite-loading-container" v-if="list.length == 0 && !wholePageLoading">
            <p class="no-data"></p>
            <p class="no-data-text">暂无数据</p>
          </div>
    </div>
  </div>
  </a-spin>
</template>
<script>
import deviceType from '../mixins/deviceType';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {
        count: '',
        alarmList: '/monitorCn/getPcsListInfo'
      }
    };
  },
  methods: {
    dealData (data, isUnit) {
      if (!data) {
        return;
      }
      let isInUnit = data.indexOf(',') > -1;
      let dataNum = isInUnit ? data.split(',') : data;
      let isArr = Array.isArray(dataNum);
      let dealData = (isArr && dataNum[0]) ? dataNum[0] : data;
      let isTrue = dealData || dealData == 0;
      let str = String(dealData).length > 9 && !isUnit ? (String(dealData).slice(0, 7) + '...') : dealData;
      if (isUnit == 'unit') {
        return isArr && dataNum.length > 1 ? dataNum[1] : 'x';
      } else {
        return isTrue ? str : '--';
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
 align-items: flex-start;
  .b-box {
    width: 382px;
height: 210px;
    margin: 0 16px 16px 0;
  }
  .content {
    flex-wrap: wrap;
    height: calc(100vh - 410px);
    overflow: auto;
    align-content: flex-start;
    margin-top: 16px;
  }
  .b-box {
    section {
      padding: 8px 16px 8px 0;
      .left {
        width: 140px;
        margin-right: 8px;
      }
      .right {
        flex-direction: column;
        padding: 16px;
        justify-content: space-between;
        width: 178px;
        height: 132px;
        background: rgba(00, 00, 00, 0.19);
        border-radius: 8px;
        .desc {
          display: flex;
          color: white;
        }
         .desc-left {
          white-space: nowrap;
          flex: 1;
        }
        .desc-right {
          width: 80px;
        }
      }
    }
  }
}
</style>
