<template>
  <a-drawer
    width="100%"
    :closable="false"
    :visible="drawerInfo.visible"
    :body-style="{ padding: '12px' }"
    @close="onClose(false)"
    :get-container="getDom"
    :wrap-style="{ position: 'absolute' }"
    :drawerStyle="{ padding: 0, 'overflow-x': 'hidden' }"
    :headerStyle="{ borderBottom: 'none' }"
    :bodyStyle="{ padding: 0 }"
  >
    <div class="title">
      <span class="back-icon" @click="onClose(false)" style="cursor: pointer"><a-icon type="left" />返回</span>
      <div class="split-line"></div>
      <span>{{ commonParams.psaName }}</span
      ><span class="split">/</span>
      <span data-v-183d8447="" class="alarm-type">统计详情</span>
    </div>

    <div class="solar-eye-search-model">
      <a-row :gutter="16" class="solar-eye-search-content" style="background: transparent; padding: 0 12px 16px; ">
        <!-- <a-col :span="queryParams.dateType == 'custom' ? 7 : 4"> -->
        <a-col :span="5">
          <div class="search-item">
            <div
              v-for="item in btnList"
              :key="item.value"
              class="btn_bg date-item flex-center font-12 cursor-pointer color-text-highlight border-highlight active-item"
              :class="{ selected: item.value == queryParams.dateType }"
              @click="selectBtn(item.value)"
            >
              {{ item.name }}
            </div>
            <a-date-picker
              style="flex: 1"
              v-model="queryParams.startTimeCn"
              placeholder="请选择日期"
              :format="time"
              :value-format="time"
              size="default"
              :mode="queryParams.dateType"
              :open="isOpen"
              :allowClear="false"
              @change="panelChangeOne" @openChange="openChangeOne" @panelChange="panelChangeOne"
            />
          </div>
        </a-col>
        <a-col :span="4">
          <div class="search-item">
            <span class="search-label">电站设备</span>
            <a-select v-model="queryParams.deviceType" placeholder="请选择" @change="changeDevice" >
              <a-select-option v-for="item in deviceList" :key="item.deviceType" :value="item.deviceType">
                {{ item.deviceTypeName }}</a-select-option
              >
            </a-select>
          </div>
        </a-col>
        <a-col :span="4">
          <div class="search-item">
            <span class="search-label">设备数据</span>
            <a-select v-model="queryParams.point" placeholder="请选择">
              <a-select-option v-for="item in pointList" :key="item.point" :value="item.point">
                {{ item.pointName }}</a-select-option
              >
            </a-select>
          </div>
        </a-col>
        <a-col :span="2">
          <div class="search-item">
            <di-throttle-button class="margin-r-12" label="查询" :loading="loading" @click="getData()" />
            <a-button class="di-cancel-btn" @click="resetEvent()">重置</a-button>
          </div>
        </a-col>
        <!-- <a-col :span="3" :offset="queryParams.dateType == 'custom' ? 4 : 7"> -->
        <a-col :span="4" :offset="5">
          <div class="search-item" style="justify-content: flex-end;">
          <div :class="{disabled: queryParams.currentPage==1}" @click="previousEvent" v-show="total>10" class='page-btn'>上一页</div>
            <span class="search-label">共 {{ total }} 条</span>
            <a-select v-model="queryParams.pageSize" style="width: 80px" @change="getData()">
              <a-select-option :value="10">10</a-select-option>
              <a-select-option :value="20">20</a-select-option>
              <a-select-option :value="50">50</a-select-option>
              <a-select-option :value="100">100</a-select-option>
              <a-select-option :value="500">500</a-select-option>
            </a-select>
            &nbsp;&nbsp;条&nbsp;&nbsp;
             <div @click="nextEvent" :class="{disabled:lastDisable}" class='page-btn' v-show="total>10"> 下一页</div>
          </div>
        </a-col>
      </a-row>
    </div>
    <a-spin :spinning="loading">
    <div class="static-content" style="height: calc(100vh - 314px)">
      <div id="staticChart" style="width: calc(100vw); height: 100%"></div>
    </div>
    </a-spin>
    <div class="drawer-form-foot" style="margin-top: 16px">
      <a-button @click="onClose(false)" class="di-cancel-btn"> 返回 </a-button>
    </div>
  </a-drawer>
</template>
<script>
import echarts from '@/utils/enquireEchart';
import moment from 'moment';
import { getCnMonitorDeviceTypePointMapping, monitorInsightAnalysisDataCn } from '@/api/monitor/device';
export default {
  props: {
    commonParams: {
      type: Object,
      default: () => {}
    },
    stationType: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      drawerInfo: {
        visible: false,
        title: '数据分析'
      },
      queryParams: {
        startTimeCn: moment(new Date()),
        deviceType: '',
        point: '',
        dateType: 'day',
        pageSize: 10,
        timeInterval: 5,
        currentPage: 1
      },
      deviceList: [],
      pointList: [],
      total: 0,
      myChart: null,
      btnList: [
        {
          value: 'day',
          name: '日'
        },
        {
          value: 'month',
          name: '月'
        },
        {
          value: 'year',
          name: '年'
        }
      ],
      baseChartOption: {
        // 图表基础配置
        legend: {
          show: false
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: false,
            lineStyle: {
              // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : '#ADADAD'
              color: '#fff'
            }
          },
          axisLabel: {
            interval: 35
          },
          axisTick: {
            show: false
          },
          boundaryGap: false
        },
        grid: {
          left: '4%',
          right: '4%'
        },
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 0,
            end: 100
          },
          {
            type: 'inside',
            realtime: true,
            start: 0,
            end: 100
          }
        ],
        yAxis: {
          splitLine: {// 分割线配置
            show: true,
            lineStyle: {
              color: ['rgba(151, 151, 151, 0.11)']
            }
          },
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
            // color: this.$store.state.app.theme === 'dark' ? '#ffffff' : '#ADADAD'
              color: '#fff'
            }
          } },
        tooltip: {
          trigger: 'axis',
          className: 'solar-eye-tooptip'
        },
        series: [
        ]
      },
      isOpen: false,
      loading: false
    };
  },
  inject: ['params'],
  mounted () {},
  watch: {
    // 'params().psId' (value) {
    //   this.getStationDevice(value);
    // }
  },
  computed: {
    time () {
      let type = this.queryParams.dateType;
      return type == 'day' ? 'YYYY-MM-DD' : (type == 'month' ? 'YYYY-MM' : 'YYYY');
    },
    lastDisable () {
      return (this.total / (Number(this.queryParams.pageSize) * Number(this.queryParams.currentPage))) < 1;
    }
  },
  methods: {
    async initData () {
      this.loading = false;
      this.drawerInfo.visible = true;
      await this.getStationDevice(this.commonParams.psId);
      this.$nextTick(() => {
        this.initChart();
        this.getData();
      });
    },
    getDom () {
      return document.getElementById('monitor-content');
    },
    selectBtn (value) {
      this.queryParams.dateType = value;
      this.changeDevice(this.queryParams.deviceType);
    },
    getData () {
      // 请求接口数据
      this.loading = true;
      let params = {
        psId: this.commonParams.psId,
        ...this.queryParams
      };
      params.timeInterval = this.queryParams.dateType == 'day' ? 5 : null;
      params.startTimeCn = this.formData(params.startTimeCn);
      monitorInsightAnalysisDataCn(params).then(res => {
        this.renderChart(res.result_data);
        this.total = res.result_data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 获取电站设备
    getStationDevice (psId) {
      return new Promise((resolve) => {
        getCnMonitorDeviceTypePointMapping({ psId: psId }).then((res) => {
          let data = res.result_data;
          let isLength = data && data.length > 0;
          this.deviceList = data || [];
          this.queryParams.deviceType = isLength ? data[0].deviceType : '';
          if (data[0]) {
            this.changeDevice(data[0].deviceType);
          }
          resolve();
        });
      });
    },
    changeDevice (value) {
      this.deviceList.forEach((item) => {
        if (item.deviceType === value) {
          this.pointList = item.point[this.queryParams.dateType];
          this.queryParams.point = (this.pointList && this.pointList[0].point) || '';
        }
      });
    },
    resetEvent () {
      // 重置
      this.queryParams = {
        startTimeCn: moment(new Date()),
        deviceType: this.deviceList.length > 0 ? this.deviceList[0].deviceType : '',
        point: '',
        dateType: 'day',
        pageSize: 10,
        timeInterval: 5,
        currentPage: 1
      };
      if (this.queryParams.deviceType) {
        this.changeDevice(this.queryParams.deviceType);
      }
      this.getData();
    },
    onClose () {
      this.myChart && echarts.dispose(this.myChart);
      this.myChart = null;
      this.drawerInfo.visible = false;
      Object.assign(this.queryParams, this.$options.data().queryParams);
      Object.assign(this.baseChartOption, this.$options.data().baseChartOption);
    },
    // 初始化图表
    initChart () {
      if (this.myChart == null) {
        let chartDom = document.getElementById('staticChart');
        this.myChart = echarts.init(chartDom);
        this.unitArr = [];
        const options = this.baseChartOption;
        options && this.myChart.setOption(options);
        this.myChart.on('datazoom', (e) => {
          this.myChart.dispatchAction({
            type: 'hideTip'
          });
        });
        this.$once('hook:beforeDestroy', function () {
          echarts.dispose(this.myChart);
        });
      }
    },
    renderChart (data) {
      // 绘制曲线
      let option = this.baseChartOption;
      option.series = this.getSeries(data.yData);
      option.xAxis.data = data.xData;
      option.xAxis.axisLabel.interval = this.queryParams.dateType == 'day' ? 35 : (this.queryParams.dateType == 'month' ? 3 : 0);
      option.yAxis.name = (data.pointName ? data.pointName : '') + (data.unit ? '(' + data.unit + ')' : '');
      option.tooltip = {
        trigger: 'axis',
        enterable: true,
        confine: true,
        alwaysShowContent: false,
        appendToBody: true,
        // position: function (point) {
        //   return [point[0] + 1, point[1] - 10];
        // },
        backgroundColor: '#203D67;',
        borderColor: '#4F678A',
        className: 'solar-eye-tooptip deep-anly-tooptip',
        textStyle: {
          color: '#fff'
        },
        triggerOn: 'mousemove',
        // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式。
        formatter: params => {
          let result = '';
          params.forEach(item => {
            result += `<div style="display:flex;width:100%;margin: 16px 0;padding: 0 16px "><div style="color: #cfcfcf;max-width: 230px;overflow:hidden;text-overflow:ellipsis" title="${item.seriesName}">${item.seriesName}&nbsp;</div> :<div style=" flex:1;text-align: right;">${item.value}</div></div>`;
          });
          return `<div style="padding:8px 16px 0">${params[0].axisValue}</div><div style="margin: 8px 0;padding:0 16px">${data.pointName}（${data.unit}）</div>` + result;
        }
      };
      this.myChart.setOption(option, true);
    },
    getSeries (arr) {
      const seriesList = [];
      arr.forEach((item, index) => {
        const series = {
          name: item.deviceName,
          type: 'line',
          lineStyle: {

            color: '#EFAF00'
          },
          cursor: 'default',
          emphasis: {
            focus: 'series',
            lineStyle: {
              color: '#00CFEF'
            }
          },
          smooth: true,
          symbolSize: 4,
          symbol: 'circle',
          data: item.data
        };
        seriesList.push(series);
      });
      return seriesList;
    },
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      this.isOpen = status;
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.queryParams.startTimeCn = this.formData(value);
      this.isOpen = false;
    },
    moment,
    formData (value) {
      return moment(value).format(this.time);
    },
    nextEvent () {
      this.queryParams.currentPage++;
      this.getData();
    },
    previousEvent () {
      this.queryParams.currentPage--;
      this.getData();
    }
  }
};
</script>
<style lang="less" scoped>
.title {
  display: flex;
  background: #112f55;
  height: 44px;
  line-height: 44px;
  align-items: center;
  color: white;
  .split-line {
    width: 1px;
    height: 12px;
    background: #bbb;
    margin: 0 20px;
    position: relative;
    top: 1px;
  }
}
.btn_bg {
  border-radius: 3px;
  opacity: 1;
width: 46px;
height: 32px;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  color: #61A1D5;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid #61A1D5;
  margin-right: 8px;
  &.selected {
    border: 1px solid #33A6FF;
    background: rgb(11, 63, 164);
  }
}
.page-btn {
  width: 59px;
height: 28px;
border-radius: 3px;
opacity: 1;
cursor:pointer;
box-sizing: border-box;
border: 1px solid #51637C;
    line-height: 28px;
    text-align: center;
  margin-right: 8px;
&.disabled{
  cursor: not-allowed;
      pointer-events: none;
}
}
.solar-eye-search-model .search-item {
  margin: 24px 0 0 0;
  height: 32px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  width: 100%;
}
.drawer-form-foot {
  border-top: 1px solid #1c519d;
  text-align: center;
  padding: 10px 0;
}
</style>
