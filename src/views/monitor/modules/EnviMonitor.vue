<!--电表-->
<template>
  <div class="device">
    <div class="content">
      <template v-for="item in list">
        <div class="b-box" :key="item.ps_key">
          <header class="device_name">
            <div :title="item.ps_name + (item.point_value ?item.point_value :'')" class="device_name_lable over-flow">
              {{item.ps_name}}{{item.point_value ?item.point_value :''}}
            </div>
          </header>
          <section>
            <div class="left"><img src="@/assets/images/monitor/device/enviMonitor.png" /></div>
            <div class="right">
              <div class="desc_left">
                <div class="desc">
                  <div class="desc-left">总辐照度(w/m²)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2007)">{{isExistValue(item.p2007)}}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">总辐照量(MJ/m²)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2006)">{{isExistValue(item.p2006)}}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">总日照小时数(h)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2002)">{{isExistValue(item.p2002)}}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">环境湿度(%RH)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2015)">{{isExistValue(item.p2015)}}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">环境温度(℃)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2009)">{{isExistValue(item.p2009)}}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">风向度数(度)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2012)">{{isExistValue(item.p2012)}}</div>
                </div>
                <div class="desc">
                  <div class="desc-left">风速(m/s)：</div>
                  <div class="desc-right" :title="isExistValue(item.p2016)">{{isExistValue(item.p2016)}}</div>
                </div>
              </div>
             </div>
          </section>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import deviceType from '../mixins/deviceType';
export default {
  mixins: [deviceType],
  data () {
    return {
      url: {
        alarmList: '/monitorNew/getMeteoStationAlarmList'
      }
    };
  },
  methods: {
    getData () {}
  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
  flex: 1;
  padding-left: 20px;
  overflow: auto;
 align-items: flex-start;
  .b-box {
    width: 380px;
    height: 312px;
    margin: 6px 16px 16px 0;
  }
  .content {
    flex-wrap: wrap;
    // height: calc(100vh - 348px);
    // flex-grow:initial;
    // padding-left: 20px;
    // overflow: auto;
  }
  .b-box {
    section {
      padding: 16px;
      .left {
        width: 126px;
        padding-right: 16px;
        text-align: center;
      }
      .right {
        padding: 16px;
        justify-content: flex-start;
        height: 234px;
        background: rgba(00, 00, 00, 0.19);
        border-radius: 8px;
        display: flex;

        .desc_left {
          width: 100%;
        }
        .desc_left+.desc_left {
          margin-left:8px;
        }
        .desc_right {
          flex: 1;
        }
        .desc {
          display: flex;
          flex-direction: row;
          line-height: 22px;
          margin-bottom: 8px;
          color: white;
        }
        .desc-left {
         flex: 1;
        }
        .desc-right {
          width: 62px;
          overflow: hidden;
          text-overflow:ellipsis;
          text-align: right;
        }
      }
    }
  }
}
</style>
