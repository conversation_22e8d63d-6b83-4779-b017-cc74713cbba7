<template>
<a-spin size="large" :spinning="loading" class="card-spin-center">
    <div class="device" :style="{height: isShowMenu ? 'calc(100vh - 325px)' : 'calc(100vh - 201px)'}">
    <div style="margin: 0 auto">
     <BoostDetail :list="list" :isTop="true"></BoostDetail>
    </div>
    </div>
  </a-spin>
</template>
<script>
import BoostDetail from './BoostDetail.vue';
import { getBoosterStationAlarmList } from '@/api/monitor/device';
export default {
  components: { BoostDetail },
  inject: ['params'],
  data () {
    return {
      list: [],
      loading: true
    };
  },
  computed: {
    isShowMenu () {
      return this.$store.state.user.isShowMenu;
    }
  },
  created () {
    this.initData();
  },
  methods: {
    initData () {
      this.loading = true;
      getBoosterStationAlarmList(this.params()).then(res => {
        let data = res.result_data;
        this.list = this.getNodes(data);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    getNodes (data) {
      let result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      //  空对象
      let map = {};
      data.forEach((item) => {
        delete item.children;
        map[item.uuid] = item;
      });
      data.forEach((item) => {
        let parent = map[item.upUuid];
        if (parent) {
          ;(parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    }

  }
};
</script>
<style lang="less" scoped>
@import url('../less/base.less');
.device {
  flex-direction: column;
  flex: 1;
  overflow: auto;
  .first_device {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  margin-left: 20px;
}
</style>
