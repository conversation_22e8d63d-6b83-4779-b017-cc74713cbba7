<!--日常巡检/数字表计-->
<template>
  <div class="digital-meter" id="canvas3d">
<!--    <a-tabs v-model="activeTab">-->
<!--      <a-tab-pane :key="item.active" :tab="item.name"  v-for="item in dataList">-->
<!--&lt;!&ndash;        <component :is="item.component"></component>&ndash;&gt;-->
<!--        <span>{{item.name}}</span>-->
<!--      </a-tab-pane>-->

<!--    </a-tabs>-->
  </div>
</template>

<script>
// import { Application } from '@splinetool/runtime';
export default {
  name: "index",
  // components: {InsightTool, VideoTree},
  data() {
    return {
      activeTab: "",
      dataList:[{
        auth: 'equipment:realTime_data',
        component: 'InsightTool',
        active:'1',
        name:'实时数据'
      },{
        auth: 'equipment:realTime_video',
        component: 'VideoTree',
        active:'2',
        name:'实时视频'
      }]
    };
  },

  mounted() {
    // const canvas = document.getElementById('canvas3d');
    // const app = new Application(canvas);
    // app.load('https://prod.spline.design/rR6Rxin-KL0icNwP/scene.splinecode');
  },
  methods:{

  }
};
</script>

<style scoped lang="less">

</style>
