<!--日常巡检/数字表计-->
<template>
  <div class="digital-meter width-height-100">
    <a-radio-group v-model="activeTab" @change="goToScrollView" class="margin-b-16">
      <a-radio-button :value="item.value" v-for="item in scrollViewList" :key="item.value" :ref="item.value">{{ item.name }}</a-radio-button>
    </a-radio-group>
    <div class="flex-gap-16 flex-baseline width-100" ref="density">
      <ContentBox title="SF6密度表" :contentNeedPadding="false" :isNeedBg="false" >
        <template #content>
          <div v-if="dataSource.length" class="margin-t-16 width-height-100 density-content-box">
            <DigitalMeterCard v-for="item in dataSource" :key="item.id" :deviceInfo="item" />
          </div>
        </template>
      </ContentBox>
    </div>
    <div class="flex-gap-16 flex-baseline width-100 margin-t-16" ref="leakage">
      <ContentBox title="避雷器电流泄露表" :contentNeedPadding="false" :isNeedBg="false">
        <template #content>
          <div v-if="dataSource.length" class="margin-t-16 width-height-100 leakage-content-box">
            <DigitalMeterCard v-for="item in dataSource" :key="item.id" :deviceInfo="item" />
          </div>
        </template>
      </ContentBox>
    </div>
  </div>
</template>

<script>
import ContentBox from '@/views/holisticSecurity/fire/modules/ContentBox.vue';
import DigitalMeterCard from './modules/DigitalMeterCard.vue';
export default {
  name: 'DigitalMeter',
  components: { ContentBox, DigitalMeterCard },
  data() {
    return {
      activeTab: null,
      scrollViewList: [
        {
          value: 'density',
          name: 'SF6密度表'
        },
        {
          value: 'leakage',
          name: '避雷器电流泄露表'
        }
      ],
      dataSource: [
        {
          deviceName: '110kV润周T开关母侧接地刀闸密度表',
          id: 1,
          temp: 28.9,
          yali: 0.66,
          midu: 0.39,
          online: 1
        },
        {
          deviceName: 'GIS母线间隔密度表',
          id: 2,
          temp: 28.9,
          yali: 0.66,
          midu: 0.39,
          online: 1
        }
      ]
    };
  },

  methods: {
    goToScrollView() {
      const element = this.$refs[this.activeTab]
      if (element && element[0]) {
        element[0].scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
};
</script>

<style scoped lang="less">
.digital-meter {
  margin-top: -15px;
}
.density-content-box,
.leakage-content-box {
  display: grid;
  grid-template-columns: repeat(auto-fit, 250px);
  gap: 16px;
  .digital-meter-card {
    height: 191px;
  }
}
</style>
