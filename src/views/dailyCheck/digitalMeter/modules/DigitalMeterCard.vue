<template>
  <div class="digital-meter-card">
    <div class="title padding-8 flex-space-between">
      <div class="device-name">{{ getLabel(deviceInfo.deviceName, null) }}</div>
      <div class="device-status-box">
        <svg-icon icon-class="offline" class="margin-r-4" v-if="!deviceInfo.online" />
        <span class="device-status margin-r-4" v-else></span>
        <span :class="{ 'color-text-second': $isEmpty(deviceInfo.online), 'color-text-main': !$isEmpty(deviceInfo.online) }">{{ $isEmpty(deviceInfo.online) ? '离线' : '在线' }}</span>
      </div>
    </div>
    <div class="content">
      <div class="indicator">
        <span class="label">温度值(℃)：</span>
        <span class="value">{{ deviceInfo.temp }}</span>
      </div>
      <div class="indicator">
        <span class="label">压力值(MPa)：</span>
        <span class="value">{{ deviceInfo.yali }}</span>
      </div>
      <div class="indicator">
        <span class="label">密度值(Mpa)：</span>
        <span class="value color-text-error">{{ deviceInfo.midu }}</span>
        <svg-icon icon-class="overpressure-alarm" style="font-size: 75px;" class="alarm-icon" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'DigitalMeterCard',
  props: {
    deviceInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  }
};
</script>
<style scoped lang="less">
.digital-meter-card {
  height: 118px;
  border: 1px solid #255dae;
  background: linear-gradient(180deg, #1a4a8e 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
  border-radius: 8px;

  .title {
    padding: 8px 16px;
    background: linear-gradient(90deg, #0661ae 0%, rgba(5, 97, 175, 0) 100%);
    border-radius: 8px 8px 0 0;
    height: 60px;
    align-items: flex-start;
    .device-name {
      width: 80%;
      white-space: pre-line;
    }
  }

  .content {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    .indicator {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      width: 100%;
      position: relative;
      .alarm-icon{
        position: absolute;
        right: 15%;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
