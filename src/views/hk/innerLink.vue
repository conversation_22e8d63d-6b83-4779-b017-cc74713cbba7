<template>
  <div class="width-height-100 fiex-row flex-gap-16">
    <div v-if="menus && menus.length > 0" class="second-menu">
      <router-link
        :to="{ path: item.path }"
        v-for="item in menus"
        :key="item.name"
        class="second-menu-item"
      >
        <svg-icon
          v-if="item.meta.icon"
          :icon-class="item.meta.icon"
          class="second-menu-icon"
        />
        <span v-else>{{ item.meta.title }}</span>
      </router-link>
    </div>
    <div
      v-if="menus && menus.length > 0"
      class="width-height-100 flex-start flex-gap-16 safe-content"
    >
      <div
        class="left"
        style="width: 200px"
        v-if="thirdMenu && thirdMenu.length > 0"
      >
        <div class="flex-start flex-column">
          <div
            v-for="child in thirdMenu"
            :key="child.name"
            @click="openNewPath(child)"
            :class="{ 'menu-selected': $route.path.indexOf(child.path) > -1 }"
            class="menu-item"
          >
            <span>{{ child.meta.title }} </span>
          </div>
        </div>
      </div>
      <div class="third-menu-container">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
import LinkJump from "@/mixins/LinkJump";
export default {
  name: "OuterLink",
  mixins: [LinkJump],
  data() {
    return {
      menus: [],
      routeName: "",
      thirdMenu: [],
      menusIndex: 0,
      parenPath: "",
    };
  },
  created() {
    this.secondMenu("/holisticSecurity", true);
    // 自动跳转到第一个三级菜单
  },
  watch: {
    $route: {
      handler(newRoute) {
        this.resizeFun();
        const slashCount = newRoute.path.split("/").length - 1;
        if (slashCount <= 2) {
          console.log(newRoute.path, 111);
          this.getThirdMenu(newRoute.path, false);
        }
      },
    },
  },
  computed: {
    permissionMenuList() {
      return this.$store.state.user.permissionList;
    },
  },

  methods: {
    secondMenu() {
      this.permissionMenuList.forEach((item) => {
        if (item.path === "/holisticSecurity") {
          this.$nextTick(() => {
            this.resizeFun();
          });
          this.menus = item.children;
          this.getThirdMenu(this.$route.path);
        }
      });
    },
    getThirdMenu(newRoute) {
      const menu = this.menus.find((item) => newRoute.indexOf(item.path) > -1);
      this.thirdMenu = menu.children;
      this.parenPath = menu.path;
      const childIndex = this.thirdMenu.findIndex(
        (item) => newRoute.indexOf(item.path) > -1
      );
      console.log(childIndex);
      this.openNewPath(this.thirdMenu[childIndex > -1 ? childIndex : 0]);
    },
    /**
     *  打开新的页面
     * params {item}
     * params child
     */
    openNewPath(child) {
      const url = child.meta.url ?? "";
      let isIframe =
        child.meta.componentName === "IframeHkView" ||
        child.meta.componentName === "IframeView";

      // Determine if the link should open externally
      const isExternal = child.meta.internalOrExternal === true;
      if (url.indexOf("online=true") > -1) {
        const newUrl = url.replace("online=true", "");
        this.goToThirdParty(newUrl + "tenant_id=1");
      } else if (isExternal) {
        // Open external URL directly
        if (url) {
          window.open(url, "_blank");
        } else {
          console.error("External URL is missing");
        }
      } else {
        let path = "";
        if (isIframe) {
          path = this.parenPath + "/" + child.path;
        } else {
          path = child.path;
        }
        this.$router.push(path);
      }
      /*************  ✨ Windsurf Command 🌟  *************/
    },
    /**
     * Adjusts the layout of the second menu based on the window size.
     * This function calculates the scaling factor for the height and
     * applies transformations to the second menu element accordingly.
     */
    resizeFun() {
      // Define paths that do not require a dividing line under the second menu
      const h = $(window).height();
      const w = $(window).width();
      const heightScale = h / (1080 - 64);
      // 只有运行分析不需要显示二级菜单下面的分割线
      let top1 = 64 * heightScale - (64 * (1 - heightScale)) / 2 + "px";
      let width = (w - 64) / heightScale + "px";
      let marginLeft = -((w - 64) / heightScale - w) / 2 - 26 + "px";

      $(".second-menu").css({
        transform: `scale(${heightScale},${heightScale})`,
        "-ms-transform": `scale(${heightScale},${heightScale})`,
        "-webkit-transform": `scale(${heightScale},${heightScale})`,
        top: top1,
        width: width,
        marginLeft: marginLeft,
        right: "auto",
      });

      /*******  f467283c-ded6-49d8-805e-457cba4b8c34  *******/
    },
  },
};
</script>

<style lang="less" scoped>
.second-menu {
  position: absolute;
  top: 64px;
  display: flex;
  align-items: center;
  padding: 16px 0;

  width: 1856px;
  z-index: 1;
  border-bottom: 1px dashed rgb(55, 109, 162);
  .second-menu-item {
    width: 98px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    border: 1px solid #85caff;
    align-items: center;
    justify-content: center;
    color: #85caff;
    font-size: 20px;
    padding: 4px 8px;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
    }

    &.router-link-active {
      color: #fff;
      background: #0077d1;
    }

    &:not(:last-child) {
      margin-right: 16px;
    }

    .second-menu-icon {
      width: 100%;
      height: 100%;
    }
  }
}
.safe-content {
  .left {
    background: linear-gradient(180deg,#18488b,#0e4085);
    color: white;
    height: 100%;
    border-radius: 8px;
    padding: 24px 0;
    .menu-item {
      height: 48px;
      line-height: 48px;
      cursor: pointer;
      width: 100%;
      padding-left: 24px;
    }
    .menu-item:hover {
      background: linear-gradient(180deg,#2860af,#2259a4);
    }
    .menu-selected {
      background: linear-gradient(180deg,#2860af,#2259a4);
    }
  }
}
.third-menu-container {
  background: linear-gradient(180deg,#18488b,#0e4085);
  width: calc(100% - 216px);
  border-radius: 8px;
  height: 100%;
}
</style>
