<template>
  <div
    v-if="menus && menus.length > 0"
    class="smart-ecological width-height-100 fiex-row flex-gap-16"
  >
    <div
      v-for="(item, index) in menus"
      :key="item.path"
      class="menu-content"
    >
        <div
          :key="item.path"
          class="menu-child"
          :class="[
            isIncludeRouteName(item.meta.title, index),
            'bg' + (index % 6),
          ]"
          v-show="item.isShow"
          @click="openNewPath(item)"
        >
          <div class="menu-child-bg flex-center width-height-100">
            <a-icon
              :type="item.meta.icon"
              v-if="!isSolareyeCustom(item.meta.icon)"
              class="font-size-48 color-text-white"
            ></a-icon>
            <svg-icon
              v-else
              :iconClass="isSolareyeCustom(item.meta.icon, true)"
              class="font-size-48"
            ></svg-icon>
            <div class="child-div flex-center flex-column">
              <div>{{ getTitle(item.meta.title, 0) }}</div>
              <div v-if="isContainLine(item.meta.title)">
                {{ getTitle(item.meta.title, 1) }}
              </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import LinkJump from "@/mixins/LinkJump";
const PAGEPATH = '/integratedSafety'
export default {
  name: "OuterLink",
  mixins: [LinkJump],
  data() {
    return {
      menus: [],
      routeName: "",
    };
  },
  created() {
    this.secondMenu();
  },
  watch: {},
  computed: {
    permissionMenuList() {
      return this.$store.state.user.permissionList;
    },
  },

  methods: {
    secondMenu() {
      this.permissionMenuList.forEach((item) => {
        if (item.path === PAGEPATH && item.children.length > 0) {
          this.menus = item.children;
            this.menus = this.menus.filter((child) => {
            return (
              child.path != PAGEPATH
            );
          });
          this.fillMenuList();
        }
      });
    },
    fillMenuList() {
      this.menus.map((item, index) => {
        this.$set(this.menus[index], "isShow", true);
      });
    },
    /**
     *  判断是否是自定义的svg 图标
     *  parmas {string} icon 图标名称
     *  iconName {Boolean} true return svg 的name，false ，返回是否是自定义的svg 图标
     */
    isSolareyeCustom(icon, iconName) {
      if (!icon) {
        return null;
      }
      if (iconName) {
        return icon.split("solareye-custom-")[1];
      } else {
        return icon.indexOf("solareye-custom-") > -1;
      }
    },
    isIncludeRouteName(name, index) {
      let isIndexOf = this.routeName && name.indexOf(this.routeName) != -1;
      if (isIndexOf) {
        this.menus[index].isShow = true;
      }
      return isIndexOf ? "menu-selected" : "";
    },
    /**
     *  打开新的页面
     * params {item}
     * params child
     */
    openNewPath(item) {
      const url = item.meta.url ?? "";
      if (url.indexOf("online=true") > -1) {
        const newUrl = url.replace("online=true", "");
        this.goToThirdParty(newUrl + "tenant_id=1");
      } else {
        let isIframe =
          item.meta.componentName === "IframePageView" || item.meta.componentName === "IframeHkView";
        let path = "";
        if (isIframe) {
          path = PAGEPATH + "/" + item.path;
        } else {
          path = item.path;
        }
        this.$router.push(path);
      }
    },
    isContainLine(title) {
      return title.indexOf("-") > -1;
    },
    getTitle(title, index) {
      return this.isContainLine(title) ? title.split("-")[index] : title;
    },
  },
};
</script>

<style lang="less" scoped>
.smart-ecological {
  border-radius: 8px;
  background: linear-gradient(
    180deg,
    #1a4a8e 0%,
    rgba(26, 74, 142, 0.97) 0%,
    rgba(14, 64, 133, 0.94) 100%
  );
  box-sizing: border-box;
  border: 1px solid #255dae;
  overflow: hidden auto;
  padding: 16px 24px;
    
  .menu-title {
    margin-bottom: 16px;
    line-height: 22px;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    color: white;

    .solar-eye-arrow-down {
      transform: rotate(90deg);
    }

    .solar-eye-arrow-up {
      transform: rotate(-90deg);
    }
  }

  .menu-content {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fill, 287px);
  }

  .menu-child {
    white-space: pre-wrap;
    height: 128px;
    cursor: pointer;
    background-size: cover;
    position: relative;
    color: #ffffff;
    z-index: 0;

    .menu-child-bg {
      position: relative;
      z-index: 4;
      border-radius: 8px;
    }

    a,
    .child-div {
      padding-left: 16px;
      font-weight: 600;
      font-family: PingFang SC;
      font-size: 20px;
    }
  }

  .bg0 {
    background-image: url("../../assets/images/smartEcological/bg0.png");
  }

  .bg1 {
    background-image: url("../../assets/images/smartEcological/bg1.png");
  }

  .bg2 {
    background-image: url("../../assets/images/smartEcological/bg2.png");
  }

  .bg3 {
    background-image: url("../../assets/images/smartEcological/bg3.png");
  }

  .bg4 {
    background-image: url("../../assets/images/smartEcological/bg4.png");
  }

  .bg5 {
    background-image: url("../../assets/images/smartEcological/bg5.png");
  }
}

.menu-child::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.14); /* 白色半透明蒙版 */
  z-index: 1;
}

.menu-child:hover::before {
  display: block;
}
</style>
