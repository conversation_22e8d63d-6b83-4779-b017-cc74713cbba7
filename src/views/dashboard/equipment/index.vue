<template>
  <div class="equipment">
    <a-tabs v-model="activeTab">
      <a-tab-pane :key="item.active" :tab="item.name"  v-for="item in dataList" v-has="item.auth">
      <component :is="item.component"></component>
      </a-tab-pane>
      
    </a-tabs>
  </div>
</template>

<script>
import InsightTool from "./insightTool/InsightTool";
import VideoTree from "./realTimeVideo/VideoTree";
import { getNewListByAuth } from "@/utils/util";

export default {
  name: "index",
  components: {InsightTool, VideoTree},
  data() {
    return {
      activeTab: "",
      dataList:[{
        auth: 'equipment:realTime_data',
        component: InsightTool,
        active:'1',
        name:'实时数据'
      },{
        auth: 'equipment:realTime_video',
        component: VideoTree,
        active:'2',
        name:'实时视频'
      }]
    };
  },
  created () {
    this.dataList = getNewListByAuth(this.dataList);
    if(this.dataList.length > 0) {
      this.activeTab = this.dataList[0].active
    }
  },
  mounted() {
    this.resize();
    window.addEventListener("resize",this.resize);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("resize",this.resize);
    });
  },
  methods:{
    resize(){
      const h = $(window).height();
      const height = 64;
      const heightScale = h / (1080 - height);
      let marginTop = height * heightScale + "px";
      $(".equipment").css({
        "top": marginTop,
        height: "calc(100% - " + height * heightScale + "px)",
      });
    }
  }
};
</script>

<style scoped lang="less">

.equipment {
  width: 100%;
  //height: calc(100% - 64px);
  position: absolute;
  //top: 64px;
  padding-bottom: 38px;
  z-index: 2;

  :deep(.ant-tabs) {
    height: 100%;

    .ant-tabs-bar {
      padding: 0 24px;
      border-bottom-color: #376da2;
    }

    .ant-tabs-content {
      //padding: 0 24px;
      height: calc(100% - 60px);
      .ant-tabs-tabpane-active{
        padding: 0 24px;
      }
    }
  }
}
</style>
