<template>
  <a-row :gutter="16" justify="space-between" type="flex" class="height-100">
    <a-col :span="5" class="height-100 left-area-div">
      <div class="left-area" :style="{ height: `calc( 100% -  ${controlExpend ? 70 : 324}px)` }">
        <div class="flex-space-between">
          <span class="left-name">设备树</span>
          <span class="right-name" :class="checkedKeys.length ? 'active' : ''" @click="handleReset">取消选择</span>
        </div>
        <a-tree
          checkable
          class="live-video-tree"
          :checkedKeys="checkedKeys"
          ref="videoTree"
          :tree-data="treeData"
          :expandedKeys.sync="expandedKeys"
          :defaultExpandedKeys="expandedKeys"
          :replaceFields="replaceFields"
          @check="handleCheck"
          :selectedKeys="singleSelectKey"
          @select="handleSelect"
        >
          <template #title="{ dataRef }">
            <div :class="{ offline: dataRef.onlineStatus === 0 }">
              <span>{{ dataRef.deviceName }}</span>
              <span v-if="dataRef.onlineStatus === 0">(离线)</span>
            </div>
          </template>
        </a-tree>
      </div>
      <div :style="{ height: `${controlExpend ? 54 : 308}px` }" class="flex-shrink-0 video-card-bg live-control margin-t-16">
        <div class="flex-space-between">
          <span class="font-14 color-text-white">云镜控制</span>
          <div class="common-hover" @click="controlExpend = !controlExpend">
            <span>{{ controlExpend ? '展开' : '收起' }}</span>
            <svg-icon icon-class="arrow" :class="controlExpend ? 'rotate-90' : 'rotate-270'" class="font-16" />
          </div>
        </div>
        <div class="flex-space-between margin-t-16 margin-b-24">
          <div class="grid-container">
            <div
              v-for="(item, index) in controlBtns"
              :key="index"
              class="control-grid-item item-btn"
              :class="item.cornerClass"
              @mousedown="handleMouseDown($event, item.name)"
              @mouseup="handleMouseUp($event, item.name)"
            >
              <svg-icon :icon-class="index == 4 ? 'refresh-two-arrow' : 'arrow'" :style="{ transform: `rotate(${item.rotation}deg)` }" class="font-24" />
            </div>
          </div>
          <div class="grid-container">
            <div v-for="(item, index) in controlBtnsLevel" :key="index" :class="item.class" @mousedown="handleMouseDown($event, item.name)" @mouseup="handleMouseUp($event, item.name)">
              <span v-if="index % 3 == 1" class="btn-label">{{ item.label }}</span>
              <a-icon :type="index % 3 == 2 ? 'plus' : 'minus'" class="color-text-white add-label" v-else />
            </div>
          </div>
        </div>
        <p class="font-14 color-text-white margin-b-16">速度档位</p>
        <div class="progress-container">
          <div class="steps">
            <div v-for="(step, index) in steps" :key="index" class="step relative" :style="{ left: `${(index / (steps.length - 1)) * 99}%` }">
              <div class="step-click-area cursor-pointer" @click="changeSpeedLevel(index)"></div>
            </div>
          </div>
          <div class="current-progress" :style="{ width: `${currentProgress}%` }"></div>
          <div class="current-point" :style="{ left: `${currentProgress}%` }"></div>
        </div>
        <div class="progress-label flex-space-between margin-t-8">
          <span class="font-14" v-for="(item, index) in steps" :key="index">{{ index + 1 }}</span>
        </div>
      </div>
    </a-col>
    <a-col :span="19" class="height-100 relative">
      <div class="height-100 right-area">
        <div class="video-content" id="videoContent" v-if="singleSelectKey.length == 0">
          <div
            v-for="(item, index) in currentPageVideos"
            :key="item.psKey || index"
            :class="{
              'live-item-active': singleSelectKey.length && singleSelectKey[0] == item.psKey
            }"
            @click="selectLiveVideo(item)"
            class="live-item"
          >
            <template v-if="item.psKey">
              <div v-if="item.onlineStatus == 1" class="live-item-box">
                <LiveVideo v-if="item.liveUrl" :url="item.liveUrl"  :key="item.psKey" />
                <div v-else class="live-item-box">
                  <img src="@/assets/images/error-video.png" />
                  <span class="margin-t-12">未维护</span>
                </div>
              </div>
              <div v-else class="live-item-box">
                <!--                {{ `离线${item.deviceName}` }}-->
                <img src="@/assets/images/error-video.png" />
                <span class="margin-t-12">离线</span>
              </div>
              <div class="live-item-msg">
                <div>
                  <span v-if="item.parentDeviceName">{{ item.parentDeviceName }}#</span>
                  <span>{{ item.deviceName }}</span>
                </div>
                <DiThrottleButton label="关闭" class="di-cancel-btn" size="small" @click.stop="handleClose(item.psKey)" />
              </div>
            </template>
            <div class="live-item-box" v-else>
              <img src="@/assets/images/error-video.png" />
              <span class="margin-t-12">未配置</span>
            </div>
          </div>
        </div>
        <page-pagination :pageSize="pageMaxNum" :current="currentPage" :showSizeChanger="false" @size-change="changePage" :total="checkedPsKeyInfoList.length" class="video-pagination" />
      </div>
      <div class="height-100 right-area-full" v-if="nowSelectItem != null">
        <div class="full-video" v-if="nowSelectItem && nowSelectItem.onlineStatus == 1">
          <LiveVideo :url="nowSelectItem.liveUrl" :key="nowSelectItem.psKey" />
        </div>
        <div class="full-video flex-center flex-column" v-else>
          <img src="@/assets/images/error-video.png" />
          <span class="margin-t-12">离线</span>
        </div>

        <div class="flex-space-between margin-t-12">
          <div class="color-text-white">
            <svg-icon icon-class="2d-alarm-monitor" class="font-20 margin-r-4" />
            <span class="font-14">{{ nowSelectItem.parentDeviceName + nowSelectItem.deviceName }}</span>
          </div>
          <div class="flex-start color-text-white">
            <svg-icon icon-class="time" class="font-20 margin-r-4" />
            <span>时间：</span>
            <span>{{ nowTime }}</span>
          </div>
          <div class="color-text-white common-hover" @click="closeFull">
            <svg-icon icon-class="close" class="font-10 margin-r-4" />
            <span class="font-14">关闭</span>
          </div>
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { thirdDeviceTreeApi } from '@/api/health/AlarmEvents';
import { PSA_INFO } from '@/store/mutation-types';
import { controlLive, pLayLiveApi } from '@/api/common';
import LiveVideo from '@/components/Live';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import uuid from 'uuid/v4';

export default {
  name: 'RealTimeVideo',
  components: { LiveVideo },
  data() {
    return {
      loading: false,
      controlExpend: true,
      onlineStatus: null,
      treeData: [],
      replaceFields: {
        children: 'childrenNodeList',
        title: 'deviceName',
        key: 'psKey'
      },
      numberOfSteps: 4, // 默认档位数量
      steps: [],
      currentSpeedLevel: 4,
      currentProgress: 100, // 当前进度百分比
      controlBtns: [
        { name: 'UP_LEFT', cornerClass: 'control-top-left', rotation: 225 }, // 左上
        { name: 'TILT_UP', cornerClass: '', rotation: 270 }, // 上
        { name: 'UP_RIGHT', cornerClass: 'control-top-right', rotation: -45 }, // 右上
        { name: 'PAN_LEFT', cornerClass: '', rotation: 180 }, // 左
        { name: 'PAN_RIGHT', cornerClass: '', rotation: 0 }, // 中心
        { name: 'PAN_RIGHT', cornerClass: '', rotation: 0 }, // 右
        {
          name: 'DOWN_LEFT',
          cornerClass: 'control-bottom-left',
          rotation: 135
        }, // 左下
        { name: 'TILT_DOWN', cornerClass: '', rotation: 90 }, // 下
        {
          name: 'DOWN_RIGHT',
          cornerClass: 'control-bottom-right',
          rotation: 45
        } // 右下
      ],

      controlBtnsLevel: [
        {
          name: 'ZOOM_OUT',
          class: 'item-btn-add control-grid-item',
          iconClass: 'reduce',
          label: '-'
        }, // 变倍 -
        { name: '', class: 'item-btn-label', label: '变倍' },
        {
          name: 'ZOOM_IN',
          class: 'item-btn-add control-grid-item',
          iconClass: 'add',
          label: '+'
        }, // 变倍 +
        {
          name: 'IRIS_CLOSE',
          class: 'item-btn-add control-grid-item',
          iconClass: 'reduce',
          label: '-'
        }, // 光圈 -
        { name: '', class: 'item-btn-label', label: '光圈' },
        {
          name: 'IRIS_OPEN',
          class: 'item-btn-add control-grid-item',
          iconClass: 'add',
          label: '+'
        }, // 光圈 +
        {
          name: 'FOCUS_NEAR',
          class: 'item-btn-add control-grid-item',
          iconClass: 'reduce',
          label: '-'
        }, // 聚焦 -
        { name: '', class: 'item-btn-label', label: '聚焦' },
        {
          name: 'FOCUS_FAR',
          class: 'item-btn-add control-grid-item',
          iconClass: 'add',
          label: '+'
        } // 聚焦 +
      ],

      currentPage: 1,
      liveAreaDom: null,
      nowTime: '',
      nowSelectItem: null,
      checkedKeys: [], // check选中的psKey
      singleSelectKey: [], // 单选选中的psKey
      pageMaxNum: 9, // 每页显示个数
      checkedPsKeyInfoList: [], // 包含所有信息的psKey数组
      flattenedList: [], // 数据扁平化
      lastCheckedKeys: [], // 最近的选中key
      expandedKeys: [], // 当前展开的节点集合
      leafKeys: [], // 最深层keys
      requestSwitch: false, // 控制调整摄像头是否需要请求两次
      operateKey: null, // 摄像头多人操作时乱序问题
      timer: null
    };
  },
  created() {
    const timer = setInterval(() => {
      this.nowTime = moment().format('YYYY-MM-DD HH:mm:ss');
    }, 1000);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
    });
  },
  mounted() {
    this.getThirdDeviceTree();
    this.liveAreaDom = document.getElementById('videoContent');
    this.updateSteps();
  },
  computed: {
    // 获取psId
    psId() {
      return this.$ls.get(PSA_INFO).psId;
    },
    // 计算总页数
    totalPage() {
      const l = this.checkedKeys.length;
      if (l <= 9) return 1;
      return Math.floor(l / 9) + 1;
    },
    currentPageVideos() {
      // 截取当前页对应pageMaxNum个数
      const { checkedPsKeyInfoList, currentPage, pageMaxNum } = this;
      const nowPageList = checkedPsKeyInfoList.slice((currentPage - 1) * pageMaxNum, currentPage * pageMaxNum);
      const checkedListLength = nowPageList.length;
      // 默认根据pageMaxNum计算，如果小于pageMaxNum，填充｛psKey:null｝，否则不填充
      const list = [];
      for (let i = (currentPage - 1) * pageMaxNum; i < pageMaxNum * currentPage; i++) {
        if ((currentPage - 1) * pageMaxNum + checkedListLength > i) {
          list.push(checkedPsKeyInfoList[i]);
        } else {
          list.push({ psKey: null });
        }
      }
      return list;
    }
  },
  watch: {
    checkedKeys: {
      immediate: true,
      deep: true,
      handler(val) {
        const keys = this.flattenedList.filter((item) => item.isParent != 1 && item.deviceType != 100).map((item) => item.psKey);
        const list = val.reduce((acc, cur) => {
          if (keys.includes(cur)) {
            const findItem = this.flattenedList.find((item) => item.psKey == cur);
            acc.push(findItem);
          }
          return acc;
        }, []);
        this.checkedPsKeyInfoList = list;
      }
    },
    singleSelectKey: {
      immediate: true,
      deep: true,
      handler(val) {
        console.log('singleSelectKey', val);
        if (val.length == 0) {
          this.nowSelectItem = null;
        }
      }
    }

    // checkedPsKeyInfoList: {
    //   immediate: true,
    //   deep: true,
    //   handler(val) {
    //     // 截取当前页对应pageMaxNum个数
    //     const nowPageList = val.slice(
    //       (this.currentPage - 1) * this.pageMaxNum,
    //       this.currentPage * this.pageMaxNum
    //     );
    //     const checkedListLength = nowPageList.length;
    //     // 默认根据pageMaxNum计算，如果小于pageMaxNum，填充｛psKey:null｝，否则不填充
    //     const list = [];
    //     for (let i = 0; i < this.pageMaxNum; i++) {
    //       if (checkedListLength > i) {
    //         list.push(val[i]);
    //       } else {
    //         list.push({ psKey: null });
    //       }
    //     }
    //     this.currentPageVideos = list;
    //   },
    // },
  },
  methods: {
    async getThirdDeviceTree() {
      this.loading = true;
      const result = await thirdDeviceTreeApi({
        psId: this.psId,
        onlineStatus: this.onlineStatus,
        deviceTypeList: [54, 100]
      });
      this.treeData = result.result_data;
      this.flattenedList = this.flattenListFn(this.treeData);
      // 获取直播视频
      // this.checkedPsKeyInfoList =  this.flattenedList.filter((item) =>
      //     this.checkedKeys.includes(item.psKey)
      // );
      this.leafKeys = this.flattenedList.filter((item) => item.isParent != 1).map((item) => item.psKey);
      this.loading = false;
    },
    closeFull() {
      this.singleSelectKey = [];
    },
    async handleMouseDown(e, name) {
      if (this.singleSelectKey.length == 0) {
        this.$message.warn('请选中需要控制的摄像头');
        return;
      }
      if (this.nowSelectItem.onlineStatus != 1) {
        this.$message.warn('当前设备已离线');
        return;
      }

      if (name == '') {
        return;
      }
      if (e.button === 0) {
        console.log('=>(VideoTree.vue:456) start');
        try {
          this.operateKey = uuid();
          this.requestSwitch = await this.videoOperation(name, 'START');
          console.log('=>(VideoTree.vue:462) ', this.requestSwitch);
        } catch (err) {
          this.requestSwitch = false;
        }
      }
    },
    handleMouseUp(e, name) {
      if (this.singleSelectKey.length == 0) {
        return;
      }
      if (name == '') {
        return;
      }
      if (e.button === 0) {
        this.timer = setTimeout(() => {
          if (this.requestSwitch) {
            clearInterval(this.timer);
            this.videoOperation(name, 'END');
          }
        }, 200);
      }
      console.log(`方格 ${name} 被松开`);
    },

    async videoOperation(name, type) {
      return controlLive({
        psKey: this.nowSelectItem.psKey,
        startCmd: type,
        operateCode: name,
        speedCode: this.currentSpeedLevel,
        operateKey: this.operateKey
      });
    },

    changeSpeedLevel(index) {
      this.currentSpeedLevel = index + 1;
      this.currentProgress = (index * 100) / (this.numberOfSteps - 1);
    },

    updateSteps() {
      this.steps = Array.from({ length: this.numberOfSteps });
    },

    changePage(e) {
      this.currentPage = e;
    },

    async selectLiveVideo(item) {
      console.log('=>(VideoTree.vue:496) item', item);
      this.controlExpend = false;
      if (item.psKey) {
        this.singleSelectKey = [item.psKey];
        await this.setNowSelectItem();
        if (this.nowSelectItem.onlineStatus != 1) {
          this.$message.warn('当前设备已离线');
        }

        if (!this.expandedKeys.includes(item.psKey)) {
          let psKeys = this.loopParentKey(item.psKey);
          psKeys.shift();
          psKeys.map((el) => {
            if (!this.expandedKeys.includes(el)) {
              this.expandedKeys.push(el);
            }
          });
        }
        setTimeout(() => {
          let selectNodeDom = document.getElementsByClassName('ant-tree-treenode-selected');
          selectNodeDom[0].scrollIntoView({
            block: 'center',
            inline: 'nearest'
          });
        }, 500);
      }
    },

    async handleCheck(selectedKeys, e) {
      // 将所有的树结构全部展开
      const currentFlattenedList = this.flattenListFn([e.node.dataRef]);
      const currentCheckedPsKeys = currentFlattenedList.filter((item) => item.isParent != 1);
      // 去除未配置类型的摄像头个数
      let nowCheckKeys = [];
      const videoLength = this.currentPageVideos.filter((item) => item.psKey).length;
      // 首先判断全部未配置时
      if (!videoLength) {
        if (e.node.dataRef.childrenNodeList) {
          const parentAndSonKeys = currentCheckedPsKeys.reduce((acc, cur) => {
            const parentPsKey = this.flattenedList.find((item) => item.psKey == cur.psKey).parentPsKey;
            acc.push(...[parentPsKey, cur.psKey]);
            return acc;
          }, []);
          nowCheckKeys = Array.from(new Set(parentAndSonKeys));
        } else {
          nowCheckKeys = selectedKeys;
        }
      } else {
        nowCheckKeys = selectedKeys;
      }
      // 如果是勾选 将新勾选的keys放到数组后面
      if (e.checked) {
        let newKeys = nowCheckKeys.filter((item) => !this.lastCheckedKeys.includes(item));
        this.checkedKeys = this.lastCheckedKeys.concat(newKeys);
      } else {
        this.checkedKeys = nowCheckKeys;
        if (this.singleSelectKey.length && !this.checkedKeys.includes(this.singleSelectKey[0])) {
          this.singleSelectKey = [];
        }
      }

      this.lastCheckedKeys = this.checkedKeys;

      // 当前摄像头是否有在线的，有才请求接口
      const onLinePsKeys = currentFlattenedList.filter((item) => item.onlineStatus == 1 && item.isParent != 1).map((item) => item.psKey);
      if (!onLinePsKeys.length) return;
      // 选中时获取直播链接
      const resultData = await this.getLiveVideoUrl(onLinePsKeys);
      const keys = resultData.map((item) => item.psKey);
      this.checkedPsKeyInfoList.forEach((item) => {
        if (keys.includes(item.psKey)) {
          const findItem = resultData.find((o) => o.psKey == item.psKey);
          Object.assign(item, { liveUrl: findItem.liveUrl[0] });
        }
      });
      if (this.singleSelectKey.length) {
        this.nowSelectItem = this.checkedPsKeyInfoList.find((item) => item.psKey == this.singleSelectKey[0]);
      }

      this.$forceUpdate();

      // const { currentPageVideos } = this;
      // 判断当前视频数量非离线、在线的数量
      // if (this.totalPage != this.currentPage) {
      //   this.currentPage = this.totalPage;
      // }

      // 如果当前页选中的设备数目大于9，自动定位到最后一页
      if (e.checked) {
        this.setCurrentPageToLast();
      } else {
        // 取消勾选 当前页不变 当取消勾选后当前页超出总页数 置为最后一页
        if ((this.currentPage - 1) * this.pageMaxNum + 1 > this.checkedPsKeyInfoList.length) {
          this.setCurrentPageToLast();
        }
      }
    },

    setCurrentPageToLast() {
      this.currentPage = Math.ceil(this.checkedPsKeyInfoList.length / 9);
      this.currentPage = this.currentPage == 0 ? 1 : this.currentPage;
    },

    async handleSelect(selectedKeys, e) {
      if (selectedKeys.length == 0) {
        this.singleSelectKey = selectedKeys;
        this.controlExpend = true;
      } else {
        //  如果是区域直接返回
        if (e.node.dataRef.deviceType == 100) return;
        let selectKey = selectedKeys[0];
        // 如果是最底层 则选中
        if (this.leafKeys.includes(selectKey)) {
          this.controlExpend = false;
          // 如果单选选中 则勾选也选中
          this.singleSelectKey = selectedKeys;
          if (!this.checkedKeys.includes(selectKey)) {
            this.checkedKeys.push(selectKey);
            this.lastCheckedKeys = this.checkedKeys;
          }
          let timer = setTimeout(() => {
            let index = this.checkedPsKeyInfoList.findIndex((item) => item.psKey == selectedKeys[0]);
            this.currentPage = Math.floor(index / this.pageMaxNum) + 1;
            this.liveAreaDom.scrollTo(0, index % 9 < 6 ? 0 : 300);
            clearTimeout(timer);
            timer = null;
          }, 300);
        } else {
          this.singleSelectKey = [];
        }
      }

      await this.setNowSelectItem();

      if (this.nowSelectItem && this.nowSelectItem.onlineStatus != 1) {
        this.$message.warn('当前设备已离线');
      } else if (this.nowSelectItem && this.$isEmpty(this.nowSelectItem.liveUrl)) {
        this.$message.warn('当前设备未维护');
      }
    },
    async setNowSelectItem() {
      if (this.singleSelectKey.length) {
        let temp = this.checkedPsKeyInfoList.find(
          (item) => item.psKey == this.singleSelectKey[0]
        );
        if (temp) {
          this.nowSelectItem = temp;
          return;
        }
        const resultData = await this.getLiveVideoUrl(this.singleSelectKey);
        temp = this.checkedPsKeyInfoList.find(
          (item) => item.psKey == this.singleSelectKey[0]
        );
        temp.liveUrl = resultData.length ? resultData[0].liveUrl[0] : "";
        this.nowSelectItem = temp;
      }
    },
    //  获取直播视屏链接
    async getLiveVideoUrl(checkPsKeys) {
      const liveResultData = await pLayLiveApi({
        psKeys: checkPsKeys.join(','),
        psId: this.psId
      });
      return liveResultData;
    },
    // 将树结构扁平化
    flattenListFn(arrayList, parentObj = {}) {
      const cloneList = cloneDeep(arrayList);
      return cloneList.reduce((acc, cur) => {
        //   父级pskey做关联
        // cur.parentPsKey = parentPsKey;
        Object.assign(cur, parentObj);
        if (cur.childrenNodeList && cur.childrenNodeList.length) {
          // 除childrenNodeList单独存储
          const childrenNodeList = cur.childrenNodeList;
          cur.childrenNodeList = null;
          // isParent标记是否是父级，然后递归遍历
          const item = [
            { ...cur, childrenNodeList: null, isParent: 1 },
            ...this.flattenListFn(childrenNodeList, {
              parentPsKey: cur.psKey,
              parentDeviceName: cur.deviceName
            })
          ];
          acc.push(...item);
        } else {
          acc.push(cur);
        }
        return acc;
      }, []);
    },
    //   关闭摄像头
    handleClose(psKey) {
      const parentKeys = this.loopParentKey(psKey);
      const clonedCheckedKeys = cloneDeep(this.checkedKeys);
      this.checkedKeys = clonedCheckedKeys.filter((o) => ![...parentKeys, psKey].includes(o));
      this.lastCheckedKeys = this.checkedKeys;
      if (this.singleSelectKey[0] == psKey) {
        this.singleSelectKey = [];
      }
      let timer = setTimeout(() => {
        if ((this.currentPage - 1) * this.pageMaxNum + 1 > this.checkedPsKeyInfoList.length) {
          this.setCurrentPageToLast();
        }
        clearTimeout(timer);
        timer = null;
      }, 200);
    },

    getAllChildrenByKey(data, key) {
      return data.reduce((acc, item) => {
        if (item.parentId === key) {
          acc.push(item.id);
          acc = [...acc, ...this.getAllChildrenByKey(data, item.id)]; // 递归查找子节点的子节点
        }
        return acc;
      }, []);
    },

    // 包含当前key的所有父级key
    loopParentKey(parentKey, type = 'parent') {
      return this.flattenedList.reduce((acc, cur) => {
        if (cur.psKey == parentKey) {
          const keys = [parentKey, ...this.loopParentKey(cur.parentPsKey)];
          acc.push(...keys);
        }
        return acc;
      }, []);
    },
    //   取消选择
    handleReset() {
      if (!this.checkedKeys) return;
      this.lastCheckedKeys = [];
      this.checkedKeys = [];
      this.controlExpend = true;
      this.singleSelectKey = [];
    }
  }
};
</script>
<style lang="less">
.live-video-tree {
  scroll-behavior: smooth;

  .ant-tree-treenode-selected {
    .ant-tree-node-selected {
      background: @di-color-text-main !important;

      .ant-tree-title {
        div {
          background: @di-color-text-main !important;
        }
      }
    }
  }
}
</style>
<style scoped lang="less">
.left-area,
.video-card-bg,
.right-area {
  color: @di-color-text-main;
  background: linear-gradient(180deg, #1a4a8e 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
  border: 1px solid @di-border-color-second;
  border-radius: 8px;

  //.radio-group {
  //  width: 100%;
  //  padding: 24px 20px 12px;
  //
  //  .ant-radio-button-wrapper {
  //    width: 50%;
  //    text-align: center;
  //  }
  //}
}

.full-video {
  height: calc(100% - 40px);
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  width: 156px; /* 每个方格40px，总共3*40+8*2=136px */
}

.progress-container {
  position: relative;
  width: 100%; /* 根据需要调整宽度 */
  height: 3px;
  background-color: rgba(255, 255, 255, 0.25); /* 背景色 */
}

.steps {
  position: absolute;
  width: 100%;
  height: 100%;
}

.step {
  position: absolute;
  width: 20px; /* 每个档位的宽度 */
  background: #b0e1ff;
  position: absolute;
  top: 0;
  width: 4px;
  height: 3px;
  z-index: 2;

  .step-click-area {
    width: 20px;
    height: 20px;
    position: absolute;
    left: -8px;
    top: -8px;
  }
}

.current-progress {
  position: absolute;
  height: 100%;
  background: #0391e9;
  z-index: 1; /* 确保在背景上 */
}

.current-point {
  position: absolute;
  width: 11px; /* 圆形的直径 */
  height: 11px; /* 圆形的直径 */
  border: 1px solid #0391e9;
  transform: translate(-50%, -4px);
  background: @di-color-text-white;
  border-radius: 50%; /* 圆形 */
  z-index: 3; /* 确保在背景上 */
}

.control-grid-item {
  margin: 4px; /* 8px间距的一半 */
  border: 1px solid #5377a8;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column; /* 垂直方向排列内容 */
  position: relative;
  border-radius: 4px; /* 小圆角 */
  transition: background-color 0.3s ease, border-color 0.3s ease; /* 平滑过渡效果 */
}

.item-btn {
  width: 40px;
  height: 40px;
}

.item-btn-add {
  width: 32px;
  height: 32px;
}

.btn-label {
  color: #b8cfed;
}

.add-label {
  font-size: 12px;
}

.item-btn-label {
  width: 58px;
  height: 32px;
  margin: 4px; /* 8px间距的一半 */
  border: 1px solid #5377a8;
  transition: background-color 0.3s ease, border-color 0.3s ease; /* 平滑过渡效果 */
  border-radius: 4px; /* 小圆角 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* hover 样式 */
.control-grid-item:hover {
  background: rgba(61, 171, 255, 0.25);
  cursor: pointer;
}

/* active 样式 */
.control-grid-item:active {
  background: rgba(61, 171, 255, 0.45);
}

.control-arrow-icon {
  font-size: 16px; /* 指定箭头图标大小 */
}

/* 设置特定方格的大圆角 */
.control-top-left {
  border-top-left-radius: 20px; /* 调整大圆角 */
}

.control-top-right {
  border-top-right-radius: 20px; /* 调整大圆角 */
}

.control-bottom-left {
  border-bottom-left-radius: 20px; /* 调整大圆角 */
}

.control-bottom-right {
  border-bottom-right-radius: 20px; /* 调整大圆角 */
}

.left-area-div {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.live-control {
  padding: 16px 24px;
  overflow: hidden;
}

.right-area-full {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  padding: 24px;
  background: linear-gradient(180deg, #1a4a8e 0%, rgba(26, 74, 142, 1) 0%, rgba(14, 64, 133, 1) 100%);
  border: 1px solid @di-border-color-second;
  border-radius: 8px;
  color: @di-color-text-white;
}

.common-hover:hover {
  color: @di-btn-hover-bg;
  cursor: pointer;
}

.left-area {
  padding: 24px;

  .flex-space-between {
    .left-name {
      color: @di-color-text-white;
      font-weight: bold;
    }

    .right-name {
      color: rgba(61, 171, 255, 0.4);
      cursor: not-allowed;

      &.active {
        color: @primary-color;
        cursor: pointer;
      }
    }
  }

  .ant-tree {
    height: 100%;
    overflow: auto;

    .offline {
      color: @di-color-text-gray;
      background: transparent;
    }
  }
}

.right-area {
  padding: 24px;

  .video-content {
    height: calc(100% - 28px);
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    overflow: auto;

    .live-item {
      height: 312px;
      padding: 8px;
      border-radius: 4px;
      background: #1f559e;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(64, 170, 255, 0.53) 0%, rgba(64, 170, 255, 0) 32%, rgba(64, 170, 255, 0.0631) 79%, rgba(64, 170, 255, 0.12) 100%) 1;
      box-shadow: inset 0px 7px 10px 0px rgba(64, 170, 255, 0.14);

      .live-item-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: calc(100% - 36px);
        background-color: #001531;
      }

      .live-item-msg {
        height: 36px;
        display: flex;
        justify-content: space-between;
        padding: 0 12px;
        align-items: flex-end;
      }
    }

    .live-item:hover,
    .live-item-active {
      background: #3370c2;
    }
  }

  .pagination {
    text-align: center;
    line-height: 32px;
  }

  .video-pagination {
    position: absolute;
    bottom: 12px;
  }
}
</style>
