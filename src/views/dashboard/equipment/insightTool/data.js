import moment from 'moment';
// 指标配置数据单电站
export const indicatorsSinglePs = [
  {
    title: '电站',
    icon: 'insight-psa',
    items: [
      {
        title: '发电趋势',
        tip: '通过对比倾斜面辐照数据和电站交流功率归一化的变化趋势，帮助发现电站是否存在问题；仅支持日查询',
        value: 101,
        dates: [1],
        default: 1,
        defaultDate: moment().format('YYYY-MM-DD')
      }, {
        title: '发电同比',
        tip: '和去年各月对比系统效率及等效利用小时数，在时间维度上对比电站发电性能；仅支持年查询',
        value: 102,
        dates: [3],
        default: 3,
        defaultDate: moment().format('YYYY')
      }, {
        title: '等效利用小时数',
        tip: '支持单站和多站对比等效利用小时数，多站按发电能力从低到高排序；仅支持月、年、总查询',
        value: 103,
        dates: [2, 3, 4],
        default: 2,
        defaultDate: moment().format('YYYY-MM')
      }
    ]
  }, {
    title: '全站方阵',
    icon: 'insight-fangzheng',
    items: [
      {
        title: '发电趋势',
        tip: '通过全站方阵交流功率归一化对比不同方阵的发电变化，便于快速筛选功率下降的方阵；仅支持日查询',
        value: 201,
        dates: [1],
        default: 1,
        defaultDate: moment().format('YYYY-MM-DD')
      }, {
        title: '等效利用小时数',
        tip: '支持全站方阵等效利用小时数对比，筛选出发电差的方阵；仅支持日、月、年查询',
        value: 202,
        dates: [1, 2, 3],
        default: 1,
        defaultDate: moment().subtract(1, 'days').format('YYYY-MM-DD')
      }
    ]
  }, {
    title: '全站逆变器',
    icon: 'insight-nibianqi',
    items: [
      {
        title: '发电趋势',
        tip: '通过全站逆变器交流功率归一化对比不同逆变器的发电变化，便于快速筛选功率下降的逆变器；仅支持日查询',
        value: 301,
        dates: [1],
        default: 1,
        defaultDate: moment().format('YYYY-MM-DD')
      }, {
        title: '发电能力对比',
        tip: '支持全站逆变器等效利用小时数对比，筛选出发电差的逆变器；仅支持日、月、年查询',
        value: 302,
        dates: [1, 2, 3],
        default: 1,
        defaultDate: moment().subtract(1, 'days').format('YYYY-MM-DD')
      }, {
        title: '温度分析',
        tip: '通过全站逆变器最高温度评估逆变器存在的潜在故障，平均温度评估逆变器长期稳定性；仅支持日、月查询',
        value: 303,
        dates: [1, 2],
        default: 1,
        defaultDate: moment().subtract(1, 'days').format('YYYY-MM-DD')
      }, {
        title: '转换效率',
        tip: '通过全站逆变器转换效率查找发电效果差的逆变器；仅支持月查询',
        value: 304,
        dates: [2],
        default: 2,
        defaultDate: moment().format('YYYY-MM')
      }
    ]
  }
];

// 指标配置数据多电站
export const indicatorsMultiplePs = [
  {
    title: '等效利用小时数',
    tip: '通过等效利用小时数对比同区域不同电站的发电能力，并进行排名筛选发电差的电站',
    value: 103,
    dates: [1, 2, 3],
    default: 1,
    defaultDate: moment().subtract(1, 'days').format('YYYY-MM-DD')
  }
];

// 日月年总 数据
export const timeFilter = [
  {
    label: '日',
    disabled: false,
    value: 1,
    format: 'YYYY-MM-DD'
  }, {
    label: '月',
    disabled: false,
    value: 2,
    format: 'YYYY-MM'
  }, {
    label: '年',
    disabled: false,
    value: 3,
    format: 'YYYY'
  }, {
    label: '总',
    disabled: false,
    value: 4,
    format: 'YYYY'
  }
];

export const timeIntervalOptionsAll = {
  day: [
    {
      label: '1min',
      value: 1
    }, {
      label: '5min',
      value: 5
    }, {
      label: '15min',
      value: 15
    }, {
      label: '30min',
      value: 30
    }, {
      label: '60min',
      value: 60
    }
  ],
  week: [
    {
      label: '15min',
      value: 15
    }, {
      label: '30min',
      value: 30
    }, {
      label: '60min',
      value: 60
    }
  ],
  month: [
    {
      label: '30min',
      value: 30
    }, {
      label: '60min',
      value: 60
    }
  ]
};
// 图表基础配置
export const chartBaseOptions = {
  grid: {
    left: '5%',
    right: '5%'
  },
  legend: {
    data: [],
    type: 'scroll',
    textStyle: {
      color: '#fff'
    },
    pageIconColor: '#fff',
    pageTextStyle: {
      color: '#fff'
    },
    itemHeight: 20
  },
  tooltip: {
    className: 'solar-eye-tooptip deep-anly-tooptip',
    triggerOn: 'mousemove',
    enterable: true,
    confine: true,
    trigger: 'axis',
    alwaysShowContent: false,
    appendToBody: true
  },
  xAxis: [],
  axisPointer: {
    link: [{ xAxisIndex: 'all' }],
    snap: true,
    type: 'line',
    show: true
  },
  dataZoom: [{
    type: 'slider', // slider表示有滑动块的，
    show: true,
    showDetail: false,
    bottom: 7,
    xAxisIndex: [], // 表示x轴折叠
    start: 0, // 数据窗口范围的起始百分比
    end: 100// 数据窗口范围的结束百分比
  }, {
    type: 'inside',
    xAxisIndex: []
  }],
  yAxis: [],
  series: []
};

export const slightEnum = {
  'significant': {
    name: '低于平均值50%以上',
    background: '#ff1e10',
    value: 0
  },
  'slight': {
    name: '低于平均值0-50%',
    background: '#f9c85a',
    value: 0
  },
  'normal': {
    name: '高于平均值',
    background: '#1aad19',
    value: 0
  }
};

export const mutationObserverFun = (target, callback) => {
  const observer = new MutationObserver(callback);
  observer.observe(target, {
    childList: true, // 观察目标子节点的变化，是否有添加或者删除
    attributes: true, // 观察属性变动
    subtree: true ,// 观察后代节点，默认为 false
    attributeFilter: ['style']
  });
};
