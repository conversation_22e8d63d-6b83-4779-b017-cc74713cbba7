<template>
    <div>
      <virtual-tree
          class='tree-content'
          ref='veTree'
          node-key='openid'
          :check-strictly="true"
          :height='tableHeight - 22 + "px"'
          :data='treeDataDevice'
          :props='props'
           @check='changeDevice'
          :render-content='renderContent'
          @node-expand="nodeExpend"
          :current-node-key='selectId'
          :expand-on-click-node='false'
          :default-expanded-keys='defaultCheckedKeys'
          :default-checked-keys='defaultCheckedKeys'
        />
    </div>
</template>

<script>
import { getDeviceTypeTree } from '@/api/health/healthapi';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import virtualTree from '@/components/virtrulTree/ve-tree.vue';
import moment from 'moment';
export default {
  name: 'PsAndPointVirtualList',
  mixins: [tableHeight],
  components: { virtualTree },
  props: {
    psList: {
      type: Array,
      default: () => []
    },
    chosenPsList: {
      type: Array,
      default: () => []
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    fromPoint: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      typeFiveList: [], // 存类型为type5并且不是当前电站下的集合
      checked: true,
      selectId: null,
      props: {
        label: 'deviceName',
        children: 'children'
      },
      treeDataDevice: [], // 设备树
      bindChecked: [], // 选中测点双向绑定
      deviceExpandKeys: [],
      expandedPsList: [], // 展开电站列表
      expandedPsIndex: [], // 展开电站列表序号
      queryParams: {
        devices: [],
        timeInterval: 5,
        points: [],
        psId: '',
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
    };
  },
  created () {
  },
  watch: {
    psList: {
      immediate: true,
      handler (val, old) {
        this.reset();
      },
      deep: true
    }
  },
  methods: {
    // 重置
    reset () {
      this.$set(this.queryParams, 'devices', []);
      if (!this.fromPoint) {
        this.$emit('changeFilterDevice', { devices: this.queryParams.devices, changeRouter: false });
      }
      this.setTree();
    },
    // 组装树
    async setTree () {
      // 存psIdList
      if (this.psList && this.psList.length) {
        this.typeFiveList = [];
        this.queryParams.devices = [];
        this.expandedPsList = [];
        this.expandedPsIndex = [];
        this.treeDataDevice = [];
        let treeDataDeviceTemplt = [];
        let indexFlag = 0;
        let controlIndex = 0;
        this.psList.forEach((item, index) => {
          let obj = {
            deviceName: item.label,
            psName: item.label,
            deviceType: '11',
            id: item.value,
            psId: item.value,
            key: item.value,
            openid: item.value + ',' + item.value,
            isStation: true,
            isLeaf: false,
            isTree: item.isTree || false
          };
          // 切换电站分析 设备分析 选中电站 默认展开第一层
          let isExpandStation = false;
          if (!this.fromPoint && this.chosenPsList && this.chosenPsList[0] && this.chosenPsList[0] == obj.psId) {
            this.$set(obj, 'isTree', true);
            this.$set(item, 'isTree', true);
            this.$emit('changeCheckedKeys', obj.openid);
            isExpandStation = true;
          }
          treeDataDeviceTemplt.push(obj);
          indexFlag = index;
          // 展开
          if (item.isTree && (this.fromPoint || isExpandStation)) {
            this.expandedPsList.push(item.value);
            this.expandedPsIndex.push(index);
          } else {
            if (indexFlag === this.psList.length - 1) {
              this.$nextTick(() => {
                this.treeDataDevice = JSON.parse(JSON.stringify(treeDataDeviceTemplt));
              });
            }
          }
          if (this.expandedPsList && this.expandedPsList.length && indexFlag === this.psList.length - 1) {
            // 插入设备
            this.expandedPsList.forEach((item, index2) => {
              this.getDevice(item).then(res => {
                this.$set(treeDataDeviceTemplt[this.expandedPsIndex[index2]], 'deviceName', res[0].deviceName);
                // 引入设备时候显示正确的设备名
                this.$set(treeDataDeviceTemplt[this.expandedPsIndex[index2]], 'children', res[0].children);
                // 插入环境监测仪
                if (res && res[1] && res[1].deviceType === '5') {
                  treeDataDeviceTemplt.splice(this.expandedPsIndex[index2] + 1, 0, res[1]);
                }
                controlIndex++;
                if (controlIndex === this.expandedPsList.length) {
                  this.$nextTick(() => {
                    let deviceDataList = JSON.parse(JSON.stringify(treeDataDeviceTemplt));
                    if (this.fromPoint) {
                      let indexTemplt = 0;
                      // 处理环境监测仪
                      treeDataDeviceTemplt.forEach((item, index) => {
                        this.typeFiveList.forEach(list => {
                          if (item.psId == list.prePsId) {
                            indexTemplt++;
                            let data = {
                              ...list,
                              checkable: true,
                              isLeaf: 0,
                              key: list.psKey,
                              openid: list.psKey.split('_')[0] + ',' + list.psKey + ',' + item.psId,
                              psName: item.psName,
                              selectable: false,
                              title: list.deviceName
                            };
                            deviceDataList.splice(index + indexTemplt, 0, data);
                          }
                        });
                      });
                    }
                    this.treeDataDevice = JSON.parse(JSON.stringify(deviceDataList));
                    // 回显 遍历结束后 统一存  以防接口多次请求
                    if (this.fromPoint) {
                      if (this.typeFiveList && this.typeFiveList.length && this.defaultCheckedKeys && this.defaultCheckedKeys.length) {
                        this.typeFiveList.forEach(item => {
                          this.defaultCheckedKeys.forEach(list => {
                            if (item.psId + ',' + item.psKey + ',' + item.prePsId === list) {
                              this.queryParams.devices.push({
                                deviceName: item.deviceName,
                                deviceType: item.deviceType,
                                psKey: item.psKey || '',
                                psId: item.prePsId,
                                psName: item.prePsName
                              });
                            }
                          });
                        });
                      }
                      this.$emit('changeFilterDevice', { devices: this.queryParams.devices, changeRouter: false });
                    }
                  });
                }
              });
            });
          }
        });
      }
    },
    /* 点击节点展开方法 第一次点击非叶子节点时为懒加载
    * */
    async nodeExpend (data, node, vueComponent) {
      // 设置图标为收缩图标，等加载数据后再展开，达到与el-tree默认懒加载同样的效果
      node.expanded = false;
      if (!node.loaded && data.isStation && (!data.children || (data.children && data.children.length === 0))) { // 当没有加载过的节点才会请求数据
        node.loading = true;
        let res;

        res = await this.getDevice(data.psId);
        if (res && res[0] && res[0].children && res[0].children.length) {
          this.$refs.veTree.updateKeyChildren(node.key, res[0].children);
        } else {
          node.isLeaf = true;
        }
        // 插入环境监测仪
        if (res && res[1] && res[1].deviceType === '5') {
          this.$refs.veTree.insertAfter(res[1], node.key);
        }
        node.loaded = true;
        node.expanded = true;
        node.loading = false;
      } else {
        node.expanded = true;
      }
    },
    // element 自带节点渲染方法
    renderContent (h, { node, data, store }) {
      // 如果是叶子节点，就让展开/收缩按钮显示
      if (node.childNodes.length === 0) {
        node.isLeaf = false;
        node.expanded = false;
      } else if (data.isTree && this.fromPoint) {
        // node.expanded = true;
        // this.$refs.veTree.setCheckedKeys(["325630_1_1_1"],)
      } else {
        // 如果节点已经展开，并且存在子节点，则loaded设为true
        // 表示已加载过了，下次展开时，就不要请求数据了
        if (node.expanded) {
          node.loaded = true;
        }
      }
      node.isLeaf = !(data && data.children && data.children.length);
      // 电站级别 展开后
      if ((node.loaded && data.isStation && data.children && data.children.length) || (!node.loaded && data.isStation)) {
        node.isLeaf = false;
      }
      if (this.fromPoint) {
        // 展开的电站节点
        let psIdList = this.defaultCheckedKeys.map(item => {
          return item.split(',')[0];
        });
        if (psIdList && psIdList.length) {
          psIdList.forEach(item => {
            if (item == data.psId && data.isStation) {
              node.loaded = true;
            }
          });
        }
      }
      this.$set(node, 'checkable', data.checkable || false);
      if (data.disabled) {
        return (
          <span class='custom-tree-node'>
            <span class='node-disabled'>{node.label}</span>
          </span>);
      } else {
        return (
          <span class='custom-tree-node'>
            <span>{node.label}</span>
          </span>);
      }
    },
    async getDevice (psId) {
      let result = await getDeviceTypeTree({ psId }).then(res => {
        if (res.result_code === '1') {
          let result = res.result_data || [];
          if (result && result.length && this.fromPoint) {
            let prePsId = ''; // 如果类型为5 存入上级电站标识
            let prePsName = ''; // 如果类型为5 存入上级电站标识
            if (result[0] && result[0].deviceType == 11) {
              prePsId = result[0].psId;
              prePsName = result[0].deviceName;
            }
            // 非当前电站下的type5 去掉 存入集合 后添加进去
            let typeFive = result.filter(item => item.deviceType == 5 && item.psId != prePsId);
            if (typeFive && typeFive.length) {
              typeFive[0].prePsId = prePsId;
              typeFive[0].prePsName = prePsName;
              this.typeFiveList.push(typeFive[0]);
            }
            result = result.filter(item => item.psId == prePsId);
          }
          return this.arrayToTree(result, '0');
        }
      });
      return result;
    },
    //  高性能数组转树 此方法利用引用类型的内存 需要给出初始pid
    arrayToTree (items, minPid) {
      let psName = '';
      let prePsId = ''; // 如果类型为5  回显加上级psId
      // 取电站名
      if (items && items[0] && items[0].deviceType === '11') {
        psName = items[0].deviceName;
        prePsId = items[0].psId;
      }
      const result = []; // 存放结果集
      const itemMap = {}; // 8
      for (const item of items) {
        // 回显时候存设备节点
        if (this.fromPoint) {
          // let filterData = this.defaultCheckedKeys.filter(list => list === item.psId + ',' + item.psKey);
          let filterData = this.defaultCheckedKeys.filter(list => {
            // 类型为5的
            if (list.split(',').length === 3) {
              return list === item.psId + ',' + item.psKey + ',' + prePsId;
            } else {
              return list === item.psId + ',' + item.psKey;
            }
          });
          let psIdInfo = '';
          if (filterData && filterData.length) {
            psIdInfo = filterData[0].split(',')[0];
          }
          if ((this.fromPoint && filterData && item.psId && psIdInfo == item.psId)) {
            this.queryParams.devices.push({
              deviceName: item.deviceName,
              deviceType: item.deviceType,
              psKey: item.psKey || '',
              psId: item.deviceType == 5 && item.psId != prePsId ? prePsId : item.psId,
              psName: psName
            });
          }
        }
        const id = item.id;
        const pid = item.pid;
        if (!itemMap[id] || !items.find(ele => ele.id == pid)) {
          itemMap[id] = {
            children: []
          };
        }
        itemMap[id] = {
          ...item,
          title: item.deviceName,
          key: item.psKey || item.psId,
          psId: item.psId,
          openid: item.deviceType == 5 && item.psId != prePsId ? (item.psId + ',' + (item.psKey || item.psId) + ',' + prePsId) : (item.psId + ',' + (item.psKey || item.psId)),
          psName: psName,
          checkable: ['3', '17', '1', '4', '26', '37', '23', '24', '6', '13', '31', '30', '12', '29', '5', '7', '301', '8', '56', '33'].includes(item.deviceType),
          selectable: false,
          isLeaf: itemMap[id]['children'].length,
          children: itemMap[id]['children']
        };
        const treeItem = itemMap[id];
        // result.push(treeItem);
        if (pid === minPid) {
          result.push(treeItem);
        } else {
          if (!itemMap[pid] || !items.find(ele => ele.id == pid)) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    },
    // 设备树展开事件
    deviceExpand (expandedKeys) {
      this.deviceExpandKeys = expandedKeys;
    },
    // 改变设备
    changeDevice (val, e) {
      this.bindChecked.checked = [];
      this.queryParams.devices = e.checkedNodes.map(item => {
        let {
          deviceType,
          psKey,
          psId,
          psName,
          deviceName
        } = item;
        // 类型5特殊处理
        if (item.deviceType == 5) {
          psId = item.openid.split(',')[2] || item.openid.split(',')[0];
        }

        return {
          deviceType,
          psKey,
          psName,
          psId,
          deviceName
        };
      });
      this.$emit('changeFilterDevice', { devices: this.queryParams.devices, changeRouter: true });
      // 设备变化时 如果是单设备多测点状态 则重新获取最近点击的测点数据
      // if (this.getNowChartConfig() == '1n' && e.checked) {
      //   const { points } = this.getRealDeviceAndPoint();
      //   if (points.length == 1) {
      //     this.checkedPoint = points[points.length - 1].point;
      //     this.checkedFlag = true;
      //   }
      // }

      // this.refreshChart();
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.vue-recycle-scroller__item-wrapper){
    overflow-x: auto;
    height: 100%;
  }
  :deep(.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view){
    width: max-content;
  }
  :deep(.vue-recycle-scroller__item-wrapper){
    position: static;
  }
</style>
