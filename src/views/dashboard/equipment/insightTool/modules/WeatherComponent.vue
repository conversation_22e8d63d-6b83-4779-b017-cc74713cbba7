<!--  -->
<template>
  <div class='weather-info'>
    <a-popover placement='leftTop' trigger='click'>
      <a-space>
        <a-col>
          <img v-if='weatherInfo.iconDir' :src='weatherInfo.iconDir' class='weather-icon' />
        </a-col>
        <span
          v-if="(weatherInfo.tempNight+'') || (weatherInfo.tempDay+'')">{{ weatherInfo.tempNight
          }}℃~{{ weatherInfo.tempDay }}℃</span>
        <span>|</span><span>{{ weatherInfo.city &&weatherInfo.city.name }}</span>
      </a-space>
      <template slot='content'>
        <div class='weather-popper'>
          <vxe-table class="my-table" :data='weatherData' :maxHeight='260' ref='weatherTable'
                     resizable border="none" show-overflow highlight-hover-row size='small'>
            <vxe-table-column show-overflow='title' field='predictDate' title='日期' width='150'>
            </vxe-table-column>
            <vxe-table-column show-overflow='title' field='conditionDay' title='天气' width='150'>
              <template v-slot='{ row }'>
                <div class='weather-row'>
                  <img :src='mapWeatherIconById(row.conditionIdDay)' class='weather-icon' />
                  <span>{{ row.conditionDay }}</span>
                </div>
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow='title' field='tempDay' title='气温[℃]' width='150'>
              <template v-slot='{ row }'>
                <div class='weather-row'>
                              <span
                                v-if="(row.tempNight+'') || (row.tempDay+'')">{{ row.tempNight }}℃~{{ row.tempDay
                                }}℃</span>
                </div>
              </template>
            </vxe-table-column>
          </vxe-table>
        </div>
      </template>
    </a-popover>
  </div>
</template>

<script>
export default {
  name: 'WeatherComponent',
  props: {
    weatherInfo: {
      type: Object,
      default: () => {
      }
    },
    weatherData: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {};
  },
  created () {
  },
  methods: {
    // 时序分析-获取天气图标
    mapWeatherIconById (conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    }
  }
};
</script>

<style lang='less' scoped>
.weather-icon {
  height: 30px;
  object-fit: contain;
}

.weather-info {
  padding: 1px 20px;
  width: auto;

  .ant-space {
    cursor: pointer;
  }
}

.weather-popper {
  max-height: 300px;

  .weather-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 30px;

    img {
      margin-right: 5px;
    }
  }
}
</style>
