<!--洞察工具-->
<template>
  <a-spin :spinning="loading" class="insight-tool">
    <a-row class="flex-space-between width-height-100">
      <a-col :span="leftSpan" class="height-100">
        <a-card class="left-area">
          <a-spin :spinning="leftContentLoading">
            <LeftStationAndPointChoose
              :tabType.sync="tabType"
              @changeLeftFilter="changeLeftFilter"
              @changeFilterDevice="changeFilterDevice"
              @selectedPoint="selectedPoint"
              ref="stationAndPoint"
              @resizeChartOrTable="resizeChartOrTable"
              @changeChosenRange="changeChosenRange"
              @pointsDeviceList="pointsDeviceList"
              @updateLeftPsaList="updateLeftPsaList"
            />
          </a-spin>
        </a-card>
      </a-col>
      <a-col :span="24 - leftSpan" class="height-100">
        <div class="right-area height-100">
          <a-card class="width-100 height-100 radius-8">
            <a-row class="top-filter flex-space-between">
              <a-col :span="10" class="flex-start">
                <!--                <div-->
                <!--                    v-show="tabType == 1"-->
                <!--                    v-for="(item, index) in timeFilter"-->
                <!--                    :key="index"-->
                <!--                    class="time-btn margin-r-24"-->
                <!--                    @click="clickTimeType(item)"-->
                <!--                    :class="{-->
                <!--                  'time-btn-active': dateType == item.value && !item.disabled,-->
                <!--                  'time-btn-disabled': item.disabled,-->
                <!--                }"-->
                <!--                >-->
                <!--                  {{ item.label }}-->
                <!--                </div>-->
                <!--                <throttle-button-->
                <!--                    v-if="tabType == 1 && dateType != 4"-->
                <!--                    lable=""-->
                <!--                    icon="left"-->
                <!--                    @click="backward"-->
                <!--                    class="margin-r-12"-->
                <!--                />-->
                <!--                <a-date-picker-->
                <!--                    v-if="tabType != 2 && dateType == 1"-->
                <!--                    v-model="chosenTime"-->
                <!--                    @change="renderChartIndicator"-->
                <!--                    :format="timeFormat"-->
                <!--                    :value-format="timeFormat"-->
                <!--                    :disabled-date="disabledDate"-->
                <!--                    :allowClear="false"-->
                <!--                />-->
                <!--                <a-month-picker-->
                <!--                    v-if="tabType != 2 && dateType == 2"-->
                <!--                    placeholder="请选择月份"-->
                <!--                    v-model="chosenTime"-->
                <!--                    @change="renderChartIndicator"-->
                <!--                    :format="timeFormat"-->
                <!--                    :value-format="timeFormat"-->
                <!--                    :allowClear="false"-->
                <!--                />-->
                <!--                <year-picker-->
                <!--                    v-if="tabType != 2 && dateType == 3"-->
                <!--                    v-model="chosenTime"-->
                <!--                    @change="renderChartIndicator"-->
                <!--                    :format="timeFormat"-->
                <!--                    :value-format="timeFormat"-->
                <!--                    :allowClear="false"-->
                <!--                />-->
                <!-- <year-range v-else-if='dateType == 4' v-model='chosenRange' @change='renderChartIndicator'
                  :format='timeFormat' :value-format='timeFormat' /> -->
                <!--              <throttle-button-->
                <!--                v-if="tabType == 1 && dateType != 4"-->
                <!--                lable=""-->
                <!--                icon="right"-->
                <!--                @click="forward"-->
                <!--                class="margin-l-12"-->
                <!--                :disabled="forwardDisabled"-->
                <!--              />-->
                <a-range-picker
                  v-if="tabType == 2"
                  v-model="chosenRange"
                  @change="rangeDateChange"
                  :allowClear="true"
                  :disabled-date="disabledDate"
                  format="YYYY-MM-DD"
                  style="width: 70%"
                />
                <a-select
                  v-if="tabType == 2"
                  class="margin-l-8"
                  :getPopupContainer="(node) => node.parentNode"
                  :options="timeIntervalOptions"
                  @change="changeTimeInterval"
                  v-model="queryParams.timeInterval"
                  style="width: 100px"
                />
                <!--              <div-->
                <!--                v-show="tabType == 2"-->
                <!--                class="set-point"-->
                <!--                @click="addPointTemplate"-->
                <!--              >-->
                <!--                <svg-icon-->
                <!--                  style="font-size: 16px; cursor: pointer"-->
                <!--                  iconClass="point-add"-->
                <!--                ></svg-icon>-->
                <!--                <span class="point-tips">设为常用测点</span>-->
                <!--              </div>-->
              </a-col>
              <a-col :span="14" class="flex-end">
                <weather-component
                  v-if="showWeather && configNetConnect"
                  :weatherInfo="weatherInfo"
                  :weatherData="weatherData"
                />
                <a-radio-group
                  v-model="dataShowType"
                  @change="changeDataShowType"
                >
                  <a-radio-button value="chart">
                    <svg-icon
                      class="filter-icon solar-eye-hover-primary cursor-pointer margin-r-4"
                      iconClass="insight-chart"
                    ></svg-icon>
                    <span>图表</span>
                  </a-radio-button>
                  <a-radio-button value="table">
                    <svg-icon
                      class="filter-icon solar-eye-hover-primary cursor-pointer margin-r-4"
                      iconClass="insight-table"
                    ></svg-icon>
                    <span>表格</span>
                  </a-radio-button>
                </a-radio-group>
              </a-col>
            </a-row>
            <div class="second-level-area flex-space-between margin-t-16">
              <div>
                <span v-if="dataShowType == 'table' && tabType == 1">{{
                  tableTitle
                }}</span>
              </div>
              <div>
                <a-tooltip
                  :title="
                    toggleGridStatus ? '切换为单坐标轴' : '切换为多坐标轴'
                  "
                  placement="leftTop"
                >
                  <a
                    class="change-chart-icon solar-eye-hover-primary"
                    :class="[
                      'change-chart-icon-' +
                        (toggleGridStatus ? 'open' : 'close'),
                    ]"
                  >
                    <svg-icon
                      v-if="showToggleGridIcon"
                      iconClass="insight-change-chart"
                      @click="handleToggleGrid"
                    />
                  </a>
                </a-tooltip>
                <DiThrottleButton
                  label="导出"
                  @click="exportExcel"
                  :loading="downExcelLoading"
                  v-if="dataShowType == 'table'"
                  class="margin-l-16 di-cancel-btn"
                  v-has="'equipment:export'"
                />
              </div>
            </div>
            <a-spin :spinning="rightContentLoading">
              <!-- 图表展示区域 -->
              <div
                v-show="dataShowType == 'chart'"
                class="insight-chart"
                id="insight-chart"
              ></div>
              <!-- 指标表格展示区域 -->
              <IndicatorTable
                v-if="dataShowType == 'table' && (tabType == 1 || tabType == 2)"
                ref="indidatorTable"
                :indicatorChartParams="indicatorChartParams"
              />
            </a-spin>
            <!-- 筛选电站modal -->
            <!--            <a-modal-->
            <!--                title="设为常用测点"-->
            <!--                :visible="formModalVisible"-->
            <!--                :confirm-loading="formModalLoading"-->
            <!--                @ok="formModalOk"-->
            <!--                @cancel="formModalCancel"-->
            <!--            >-->
            <!--              <a-spin :spinning="formModalLoading">-->
            <!--                <a-form-model-->
            <!--                    ref="addPoint"-->
            <!--                    layout="horizontal"-->
            <!--                    style="width: 100%"-->
            <!--                    :model="pointForm"-->
            <!--                    :rules="rules"-->
            <!--                    v-bind="{-->
            <!--                  labelCol: { span: 4 },-->
            <!--                  wrapperCol: { span: 17 },-->
            <!--                }"-->
            <!--                    class="add-point-content"-->
            <!--                >-->
            <!--                  <a-form-model-item label="指标名称" style="margin-bottom: 10px">-->
            <!--                    <a-input-->
            <!--                        v-model="pointForm.templateName"-->
            <!--                        :maxLength="15"-->
            <!--                    ></a-input>-->
            <!--                  </a-form-model-item>-->
            <!--                  <a-form-model-item label="   " style="margin-bottom: 0px">-->
            <!--                    <a-checkbox v-model="pointForm.isChecked">-->
            <!--                      同步保存电站设备-->
            <!--                    </a-checkbox>-->
            <!--                  </a-form-model-item>-->
            <!--                </a-form-model>-->
            <!--              </a-spin>-->
            <!--            </a-modal>-->
          </a-card>
        </div>
      </a-col>
    </a-row>
  </a-spin>
</template>
<script>
import initDict from "@/mixins/initDict";
import moment from "moment";
import echarts from "@/utils/enquireEchart";
import LeftStationAndPointChoose from "./modules/LeftStationAndPointChoose";
import { timeFilter, chartBaseOptions, timeIntervalOptionsAll } from "./data";
import {
  getWeatherByTimeRange,
  createViewTemplate,
} from "@/api/health/AlarmEvents";
import { getIndicatorData } from "@/api/dataCenter";
import WeatherComponent from "./modules/WeatherComponent";
// import YearRange from '@/components/com/yearRange';
import indicator from "../mixins/indicator";
import pointInfo from "../mixins/pointInfo";
import { mixin as chartFuntions } from "../mixins/chartFuntions";
import IndicatorTable from "./modules/IndicatorTable";
import { uniqBy, cloneDeep } from "lodash";
import store from "@/store";
// import { getUrlParam } from "@/utils/util";
import { mapGetters } from "vuex";

export default {
  name: "InsightTool",
  components: { WeatherComponent, LeftStationAndPointChoose, IndicatorTable },
  mixins: [initDict, chartFuntions, indicator, pointInfo],
  data() {
    return {
      fromPoint: false,
      deviceList: [], // 选中的设备列表
      pointList: [], // 选中的测点列表
      formModalVisible: false, // 设为常用测点弹窗
      formModalLoading: false, // 设为常用测点弹窗loding
      pointForm: {
        // 常用测点
        templateName: "",
        isChecked: true,
      },
      rules: {
        templateName: [
          {
            required: true,
            message: "请输入指标名称",
          },
        ],
      },
      loading: false,
      // showWeather: false,
      timeFilter: timeFilter,
      dataShowType: "chart", // 数据展示类型 chart 图表 table 表格
      timeIntervalOptions: [],
      timeIntervalOptionsAll: timeIntervalOptionsAll,
      weatherInfo: {},
      weatherData: [],
      queryParams: {
        timeInterval: null,
      },
      pointsDeviceInfo: {},
      indicatorChartParams: {},
      chosenTime: "",
      chartBaseOptions: chartBaseOptions, // 图表基础配置
      chosenRange: [
        moment().format("YYYY-MM-DD"),
        moment().format("YYYY-MM-DD"),
      ],
      chosenIndicator: {}, // 选中的指标
      chosenPsList: [], // 选中的电站列表
      tabType: 2, // 电站分析：1，设备分析：2
      timeFormat: "YYYY-MM-DD",
      dateType: 1, // 1日 2月 3年 4总
      chartBaseData: {}, // 图表渲染的基础数据
      rightContentLoading: false, // 右侧图表渲染的loading
      downExcelLoading: false, // 下载文件loading
      gridHeight: 180, // 图表实例中一个y轴高度
      baseHeight: 60,
      height: 100,
      toggleGridStatus: false, // 切换Y轴是否平铺展开状态
      leftPsaList: [], // 左侧所筛选出的电站
      leftContentLoading: false, // 左侧区域loading
      firstLoading: true,
      leftSpan: 8,
    };
  },
  provide() {
    return {
      updateLoading: this.updateLoading,
      updateRightContentLoading: this.updateRightContentLoading,
      updateUnit: this.updateUnit,
      updateUnitFromCommonPoint: this.updateUnitFromCommonPoint,
      updateLeftContentLoading: this.updateLeftContentLoading,
    };
  },
  computed: {
    navTheme() {
      return store.state.app.theme;
    },
    tableTitle() {
      const params = this.formatParams();
      const isMultiplePsa = params.psNameList.length > 1;
      const dateType = this.dateType;
      const date =
        dateType == 1
          ? params.day
          : dateType == 2
          ? params.month
          : dateType == 3
          ? params.year
          : "";
      if (isMultiplePsa) {
        return date;
      }
      return `${(params.psNameList && params.psNameList[0]) || ""} ${date}`;
    },
    showWeather() {
        const weatherInfoBol = Object.keys(this.weatherInfo).length > 0;
        const weatherDataBol = this.weatherData.length > 0;
      if (this.tabType == 1) {
        const choosedPsa = this.chosenPsList.length === 1;
        return (
          weatherInfoBol &&
          weatherDataBol &&
          choosedPsa &&
          [1, 2].includes(this.dateType)
        );
      }
      return weatherDataBol && weatherInfoBol && this.leftPsaList.length === 1;
    },
    showToggleGridIcon() {
      const { tabType, dataShowType } = this;
      // 测点并且是图表
      const chartFlag = dataShowType == "chart" && tabType == 2;
      // 设备数量
      const psKeys = this.list.reduce((acc, cur) => {
        acc.push(...(cur.psKeys || []));
        return acc;
      }, []);
      // 测点数量
      const points = uniqBy(
        this.list.reduce((acc, cur) => {
          acc.push(...(cur.pointList || []));
          return acc;
        }, []),
        "point"
      );
      // 多设备多测点
      let moreDeviceAndPointFlag = psKeys.length > 1 && points.length > 1;
      return chartFlag && moreDeviceAndPointFlag;
    },
    ...mapGetters(['configNetConnect', 'configDataFrequency'])
  },
  activated() {
    const isComefromAlarmCenter = JSON.parse(localStorage.getItem('comefromAlarmCenter') || 'false');
    if (isComefromAlarmCenter && !this.firstLoading) {
      window.comefromAlarmCenter = false;
      localStorage.setItem('comefromAlarmCenter', false)
      this.$refs.stationAndPoint.dealRouterEvent();
    }
    this.firstLoading = false;
  },
  created() {
    const isComefromAlarmCenter = JSON.parse(localStorage.getItem('comefromAlarmCenter') || 'false');
    if (!isComefromAlarmCenter) {
      this.timeIntervalOptions = cloneDeep(this.timeIntervalOptionsAll.day);
      const setValue = this.configDataFrequency;
      if (setValue != "1") this.timeIntervalOptions.splice(0, 1);
      this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
    }
  },
  mounted() {
    this.initChart();
    window.addEventListener("resize", this.handleResize);
  },
  watch: {
    navTheme: {
      handler: function (newVal, oldVal) {
        if (this.myChart) {
          if (this.tabType == 1) {
            const resolvedOptions = this.handleIndicatorOption();
            this.myChart.setOption(resolvedOptions, true);
          } else {
            this.renderPointChart();
          }
        }
      },
      immediate: true,
    },
    deviceList: {
      deep: true,
      handler: function (newVal, oldVal) {
        // 请选择测点抖动效果
        this.showPointTips(newVal, oldVal);
      },
    },
  },
  methods: {
    // 测点类型与值集合
    pointsDeviceList(value) {
      this.pointsDeviceInfo = value;
    },
    // 诊断跳转 时间
    changeChosenRange(timer) {
      this.chosenRange = [
        moment(timer[0]).format("YYYY-MM-DD"),
        moment(timer[1]).format("YYYY-MM-DD"),
      ];
      let dayIntervel = moment(timer[1]).diff(timer[0], "day");
      if (dayIntervel === 0) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.day;
        const enableOneMin = this.configDataFrequency != "1";
        if (enableOneMin) this.timeIntervalOptions.splice(0, 1);
      } else if (dayIntervel >= 1 && dayIntervel < 10) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.week;
      } else if (dayIntervel >= 10 && dayIntervel <= 31) {
        this.timeIntervalOptions = this.timeIntervalOptionsAll.month;
      }
      this.queryParams.timeInterval = this.timeIntervalOptions[0].value;
    },
    // 选中设备列表
    changeFilterDevice(value) {
      // 去掉电站
      this.deviceList = value.value.filter((item) => item.deviceType != 11);
      if (this.deviceList && this.deviceList.length > 100) {
        this.$message.destroy();
        this.$message.warning("设备最多选择100条");
      } else {
        if (!this.fromPoint) {
          this.getInsightToolsInsightAnalysisData(value.fromRoute);
        } else {
          this.fromPoint = false;
        }
      }
    },
    // 选中测点列表
    selectedPoint(value) {
      this.pointList = value.value || [];
      this.cancelTips();
      if (this.pointList && this.pointList.length > 100) {
        this.$message.destroy();
        this.$message.warning("测点最多选择100条");
      } else {
        this.getInsightToolsInsightAnalysisData(value.fromRoute);
      }
    },
    // 测点如果有类型匹配设备就取消提示
    cancelTips() {
      // 设备类型集合
      let deviceTypeList = Array.from(
        new Set(this.deviceList.map((item) => item.deviceType))
      );
      // 测点类型集合
      let pointTypeList = Array.from(
        new Set(this.pointList.map((item) => item.deviceType))
      );
      deviceTypeList.forEach((item) => {
        if (pointTypeList.includes(item)) {
          this.$refs.stationAndPoint.$refs.customPointArea.interceptorPsa(
            "",
            false
          );
        }
      });
    },
    // 选中设备是否有相关测点 没有就提示
    showPointTips(newVal, oldVal) {
      let oldValPsKeyList = [];
      oldVal.map((item) => {
        // 去掉电站
        if (item.deviceType != 11) {
          oldValPsKeyList.push(item.psKey);
        }
      });
      let newDevice = [];
      newVal.map((item) => {
        if (!oldValPsKeyList.includes(item.psKey) && item.deviceType != 11) {
          newDevice.push(item);
        }
      });
      // 选择测点的类型集合
      let pointsDevicesList = this.pointList.map((item) => item.deviceType);
      if (
        newDevice &&
        newDevice.length &&
        !pointsDevicesList.includes(newDevice[0].deviceType)
      ) {
        this.$refs.stationAndPoint.$refs.customPointArea.interceptorPsa(
          this.pointsDeviceInfo[newDevice[0].deviceType] || "",
          true
        );
      }
    },
    // 修改时间间隔
    changeTimeInterval() {
      // 查询设备分析echarts数据
      if (this.tabType == 2) {
        this.getInsightToolsInsightAnalysisData();
      }
    },
    // 加常用测点
    addPointTemplate() {
      // 至少选中一个测点
      if (this.pointList && this.pointList.length) {
        this.formModalVisible = true;
      } else {
        this.$message.warning("请至少选择一个测点");
      }
    },
    // 保存常用测点
    formModalOk() {
      this.$refs.addPoint.validate((valid) => {
        if (valid) {
          this.formModalLoading = true;
          let pointData = [];
          // 处理测点
          if (this.pointList && this.pointList.length) {
            this.pointList.forEach((item) => {
              pointData.push({
                deviceType: item.deviceType,
                pointKey: item.key,
                pointName: item.pointName,
                pointUnit: item.unit,
              });
            });
          }
          // 处理设备
          let params = {
            templateName: this.pointForm.templateName,
            devices: this.pointForm.isChecked ? this.deviceList : null,
            points: pointData,
          };
          createViewTemplate(params)
            .then((res) => {
              this.$message.success("设置成功！");
              this.$refs.addPoint.resetFields();
              this.formModalVisible = false;
              // 刷新常用列表
              this.$refs.stationAndPoint.$refs.customPointArea.refreshPoint();
              this.formModalLoading = false;
              // 重置指标名称
              this.pointForm = this.$options.data().pointForm;
            })
            .catch(() => {
              this.formModalLoading = false;
            });
        }
      });
    },
    formModalCancel() {
      this.formModalVisible = false;
    },
    // 左侧筛选发生变化时
    changeLeftFilter({
      chosenPsList,
      chosenIndicator,
      tabType,
      psNameList,
      leftPsaList,
      fromPoint,
    }) {
      this.fromPoint = fromPoint || false;
      // 切换tab 默认图表
      if (this.tabType != tabType) this.dataShowType = "chart";
      this.chosenPsList = chosenPsList; // 选中的电站
      // 判断当前指标是否为统一指标或者电站等效小时数为是否单电站
      if (
        chosenIndicator.value != 103 &&
        chosenIndicator.value != this.chosenIndicator.value
      ) {
        this.dateType = chosenIndicator.default;
        this.chosenTime = chosenIndicator.defaultDate; // 默认时间
      } else if (chosenIndicator.value == 103 && chosenPsList.length <= 1) {
        this.dateType = chosenIndicator.default;
        this.chosenTime = chosenIndicator.defaultDate; // 默认时间
      } else if (chosenIndicator.value == 103 && chosenPsList.length == 2) {
        this.dateType = chosenIndicator.default;
        this.chosenTime = chosenIndicator.defaultDate; // 默认时间
      }
      this.chosenIndicator = chosenIndicator; // 选中指标
      this.tabType = tabType; // 设备/电站标识
      this.leftPsaList = leftPsaList; // 左侧电站列表
      this.indicatorChartParams = {
        indexType: chosenIndicator.value,
        dateType: this.dateType,
        psIdList: chosenPsList,
        psNameList: psNameList,
      };
      // 电站分析
      if (tabType == 1) {
        this.pointList = [];
        // 匹配支持的时间类型
        this.timeFilter = this.timeFilter.map((item) => {
          item.disabled = !chosenIndicator.dates.includes(item.value);
          return item;
        });
        // 根据类型匹配对应的时间格式
        this.timeFormat = this.timeFilter[this.dateType - 1].format;
        // 通过时间类型去请求数据
        this.renderChartIndicator();
      } else if (tabType == 2) {
        // 每次回到设备分析重置右侧单位栈
        if (!this.pointList.length || fromPoint) {
          this.updateUnitFromCommonPoint([]);
        }
        // 设备分析
        this.changeDataShowType();
        // 只有单电站时获取天气数据
        if (this.leftPsaList.length === 1) this.getWeatherInfo();
      }
    },
    // 更改数据展示类型
    async changeDataShowType() {
      await this.$nextTick();
      if (this.dataShowType === "table" && this.tabType == 1) {
        this.interceptorPsa(this.indicatorChartParams.psIdList).then(
          async (canRequest) => {
            if (canRequest) {
              const params = this.formatParams();
              await this.$refs.indidatorTable.init(params, this.tabType);
            }
          }
        );
      } else if (this.dataShowType === "chart" && this.tabType == 1) {
        await this.renderChartIndicator();
        this.resizeChartOrTable();
      } else if (this.dataShowType === "table" && this.tabType == 2) {
        this.$refs.indidatorTable.init(
          "",
          this.tabType,
          this.dataSource || [],
          this.columnMap || []
        );
      } else if (this.dataShowType === "chart" && this.tabType == 2) {
        this.resizeChartOrTable();
      }
    },
    // 初始化图表
    initChart() {
      if (this.myChart == null) {
        let chartDom = document.getElementById("insight-chart");
        this.myChart = echarts.init(chartDom);
        this.unitArr = [];
        const options = this.chartBaseOptions;
        options && this.myChart.setOption(options);
        this.myChart.on("datazoom", () => {
          this.myChart.dispatchAction({
            type: "hideTip",
          });
        });
        this.$once("hook:beforeDestroy", function () {
          echarts.dispose(this.myChart);
          window.removeEventListener("resize", this.handleResize);
        });
      }
    },
    // 获取接口返回的数据
    async getRequestData() {
      const params = this.formatParams();
      const res = await getIndicatorData(params);
      this.chartBaseData = res.result_data;
      return res;
    },
    // 渲染指标的图表
    async renderChartIndicator() {
      if (this.dataShowType === "chart" && this.tabType == 1) {
        this.interceptorPsa(this.indicatorChartParams.psIdList).then(
          async (canRequest) => {
            if (canRequest) {
              try {
                this.rightContentLoading = true;
                await this.getRequestData();
                const resolvedOptions = this.handleIndicatorOption();
                const height = $(".right-area").height() - 130;
                this.myChart.resize({ height });
                this.myChart.setOption(resolvedOptions, true);
                this.rightContentLoading = false;
              } catch (err) {
                this.rightContentLoading = false;
              }
            }
          }
        );
      } else if (this.dataShowType === "table" && this.tabType == 1) {
        this.interceptorPsa(this.indicatorChartParams.psIdList).then(
          async (canRequest) => {
            if (canRequest) {
              const params = this.formatParams();
              await this.$refs.indidatorTable.init(params, this.tabType);
            }
          }
        );
      }
      // 非“日” “月”不展示天气情况
      const flag = [1, 2].includes(this.dateType);
      if (
        !flag ||
        this.$isEmpty(this.chosenPsList) ||
        this.chosenPsList.length > 1
      )
        return;
      // 获取天气资源信息
      this.getWeatherInfo();
    },
    clickTimeType(item) {
      if (item.disabled) return;
      if (item.value != this.dateType) {
        this.changeTimeType(item.value);
      }
    },
    changeTimeType(type) {
      this.dateType = type;
      // 切换日期类型时同时改变indicatorChartParams
      this.indicatorChartParams.dateType = this.dateType;
      // 根据类型匹配对应的时间格式
      this.timeFormat = this.timeFilter[this.dateType - 1].format;
      // 处理时间
      this.chosenTime = moment(this.chosenIndicator.defaultDate).format(
        this.timeFormat
      );

      // 重置右侧日月年总默认选项后查询图表
      this.renderChartIndicator();
      // 查询设备分析echarts数据
      if (this.tabType == 2) {
        this.getInsightToolsInsightAnalysisData();
      }
    },
    // 时序分析-获取天气信息
    getWeatherInfo() {
      const params = this.formatParams();
      const weathParams = {
        psId: this.chosenPsList[0],
        startDate:
          params.dateType == 1
            ? params.day
            : moment(params.month).startOf("month").format("YYYY-MM-DD"),
        endDate:
          params.dateType == 1
            ? params.day
            : moment().isSame(params.month, "M")
            ? moment().format("YYYY-MM-DD")
            : moment(params.month).endOf("month").format("YYYY-MM-DD"),
      };
      // 设备分析
      if (this.tabType == 2) {
        weathParams.startDate = moment(this.chosenRange[0]).format(
          "YYYY-MM-DD"
        );
        weathParams.endDate = moment(this.chosenRange[1]).format("YYYY-MM-DD");
      }
      getWeatherByTimeRange(weathParams).then((res) => {
        if (Object.keys(res.result_data).length > 0) {
          this.weatherInfo.city = res.result_data.city;
          this.weatherData = res.result_data.weather.map((item) => {
            this.weatherInfo.tempDay = item.tempDay;
            this.weatherInfo.tempNight = item.tempNight;
            this.weatherInfo.iconDir = this.mapWeatherIconById(
              item.conditionIdDay
            );
            return {
              predictDate: item.timeDate.substring(0, 10),
              conditionDay: item.conditionDay,
              conditionIdDay: item.conditionIdDay,
              tempDay: item.tempDay,
              tempNight: item.tempNight,
            };
          });
        } else {
          this.weatherData = [];
        }
      });
    },
    // 时序分析-获取天气图标
    mapWeatherIconById(conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    },
    // 请选择电站的判断条件
    interceptorPsa(psIdList) {
      return new Promise((resolve, reject) => {
        if (this.$isEmpty(psIdList)) {
          this.$refs.stationAndPoint.updateTooltipVisible(true);
          setTimeout(() => {
            $(".insight-warning-tooltip").addClass("animation-jelly");
          }, 200);
          setTimeout(() => {
            $(".insight-warning-tooltip").removeClass("animation-jelly");
          }, 500);
          resolve(false);
        } else {
          this.$refs.stationAndPoint.updateTooltipVisible(false);
          resolve(true);
        }
      });
    },
    formatParams() {
      const params = {
        ...this.indicatorChartParams,
        day: this.dateType == 1 ? this.chosenTime : undefined,
        month: this.dateType == 2 ? this.chosenTime : undefined,
        year: this.dateType == 3 ? this.chosenTime : undefined,
      };
      return params;
    },
    updateRightContentLoading(bool) {
      this.rightContentLoading = bool;
    },
    updateLoading(bool) {
      this.loading = bool;
    },
    updateLeftContentLoading(bool) {
      this.leftContentLoading = bool;
    },
    // 文件导出
    exportExcel() {
      this.$refs.indidatorTable.exportExcel();
    },
    resizeChartOrTable() {
      this.leftSpan = this.$refs.stationAndPoint?.expend ? 4 : 8;
      this.$nextTick(() => {
        this.myChart.resize();
      });
    },
    updateLeftPsaList(list) {
      this.leftPsaList = list;
    },
    handleResize() {
      const height = $(".right-area").height() - 130;
      this.myChart?.resize({ height });
    },
  },
};
</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
  width: 100% !important;
  height: 100% !important;
}

:deep(.ant-spin-container) {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

:deep(.ant-card-body) {
  height: 100%;
}

.left-area {
  margin-right: 16px;
  height: 100%;
  flex-shrink: 0;
  border-radius: 8px;

  :deep(.ant-card-body) {
    padding-bottom: 8px;
  }

  :deep(
      .vue-recycle-scroller__item-view:not(:first-child)
        .is-checked
        .custom-tree-node
    ),
  :deep(.ant-tree-treenode-checkbox-checked .ant-tree-title) {
    color: @di-color-text-main;
  }
}

.time-btn {
  font-size: 14px;
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  box-sizing: border-box;
  border-radius: 3px;
  color: #666666;
  border: 1px solid #dcdcdc;
}

.time-btn:hover {
  border: 1px solid @primary-color;
  color: @primary-color;
  cursor: pointer;
}

.time-btn-disabled {
  color: #999999 !important;
  border: 1px solid #dcdcdc !important;
  cursor: not-allowed !important;
  background-color: #f5f5f5;
}

.time-btn-active {
  color: @primary-color;
  background: #fff2e5;
  border: 1px solid #ff8100;
}

.second-level-area {
  height: 32px;
  width: 100%;

  .change-chart-icon {
    font-size: 20px;

    &.change-chart-icon-open {
      color: @di-color-text-main;
    }

    &.change-chart-icon-close {
      color: @di-color-text-white;
    }
  }
}

.right-area {
  width: 100%;
  height: 100%;

  .top-search {
    height: 32px;
    width: 100%;
  }
}

.insight-chart {
  width: 100%;
  height: calc(100% - 70px);
  overflow-y: auto;
  overflow-x: hidden;
}

.set-point {
  display: flex;
  align-items: center;
  margin-left: 24px;
  color: #ff8100;
  cursor: pointer;

  .point-tips {
    margin-left: 4px;
  }

  &:hover {
    color: #ffab5c;
  }
}

:deep(.solar-eye-btn-primary-cancel) {
  &:hover {
    color: @primary-color !important;
    border: 1px solid @primary-color !important;
  }
}
</style>
<style lang="less">
.deep-anly-tooptip {
  max-width: 600px !important;
  max-height: 500px !important;
  overflow: auto;
}
</style>
