import { clone, orderBy } from 'xe-utils';
import { slightEnum } from '../insightTool/data';
import { max, groupBy } from 'lodash';
import moment from 'moment';

import { USER_INFO } from '@/store/mutation-types';
export default {
  data () {
    return {

    };
  },
  computed: {
    // 日期步进器向后是否置灰判断条件
    forwardDisabled () {
      if (this.dateType == 1) {
        return moment(this.chosenTime).isSameOrAfter(moment().format('YYYY-MM-DD'), 'days');
      }
      return false;
    }
  },
  methods: {
    // 处理指标分析
    handleIndicatorOption () {
      const { value: selectedIndicatorValue } = this.chosenIndicator;
      switch (selectedIndicatorValue) {
        case 101:
          return this.handle101();
        case 102:
          return this.handle102();
        case 103: case 304:
          return this.handle103();
        case 201: case 301:
          return this.handle201();
        case 202:
          return this.handle202();
        case 302:
          return this.handle302();
        case 303:
          return this.handle303();
        default:
          return {};
      }
    },
    // 判断是数字的位数
    checkIntDigits (num) {
      var digits = 0;
      while (num >= 1) {
        digits++;
        num /= 10;
      }
      return digits;
    },
    assignLegend (newOptions) {
      const isDarkColor = this.navTheme == 'dark' ? '#ffffff' : '#333';
      newOptions.legend.textStyle.color = isDarkColor;
      newOptions.legend.pageIconColor = isDarkColor;
      newOptions.legend.pageTextStyle.color = isDarkColor;
      // console.log('newOptions.series :>> ', newOptions.series);
      // newOptions.title = {
      //   show: newOptions.series.some(item=>), // 没数据才显示
      //   extStyle: {
      //     color: 'grey',
      //     fontSize: 20
      //   },
      //   text: '暂无数据',
      //   left: 'center',
      //   top: 'center'
      // };
      return newOptions;
    },
    // 发电趋势
    handle101 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData: [first] } = this.chartBaseData;
      const { unit, data, indexName } = first;
      newOptions.dataZoom[0].xAxisIndex = xData.map((item, index) => index);
      newOptions.dataZoom[1].xAxisIndex = xData.map((item, index) => index);
      newOptions.legend.data = indexName;
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.yAxis = data.map((item, index) => (
        this.makeYAxis(0, {
          name: unit[index],
          type: 'value'
        }, true)));

      newOptions.series = data.map((item, index) => {
        return this.makeSeries(0, 0, {
          name: indexName[index],
          data: item,
          yAxisIndex: index
        }, 'line');
      });
      return this.assignLegend(newOptions);
    },
    // 发电对比
    handle102 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData: [first] } = this.chartBaseData;
      const { unit, data, indexName } = first;
      newOptions.legend.data = indexName;
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.yAxis = unit.reduce((acc, cur) => {
        if (!acc.includes(cur)) acc.push(cur);
        return acc;
      }, []).map((item, index) => (
        this.makeYAxis(0, {
          name: item,
          type: 'value'
        }, true)
      ));
      newOptions.series = data.map((item, index) => {
        return this.makeSeries(0, 0, {
          name: indexName[index],
          data: item,
          yAxisIndex: indexName[index].includes('PR') ? 1 : 0
        }, indexName[index].includes('PR') ? 'line' : 'bar');
      });
      return this.assignLegend(newOptions);
    },
    // 电站等效利用小时数，逆变器转换效率
    handle103 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData: [first] } = this.chartBaseData;
      const { unit, data, indexName } = first;
      newOptions.legend.data = indexName;
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.yAxis = data.map((item, index) => (
        this.makeYAxis(0, {
          name: unit[index],
          type: 'value'
        }, true)));
      newOptions.series = data.map((item, index) => {
        return this.makeSeries(0, 0, {
          name: indexName[index],
          data: item
        }, 'bar');
      });
      return this.assignLegend(newOptions);
    },
    // 方阵发电趋势，逆变器发电趋势
    handle201 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData, psIrradiance } = this.chartBaseData;
      const unit = yData.reduce((acc, cur) => {
        if (!acc.includes(cur.unit[0])) {
          acc.push(...cur.unit);
        }
        return acc;
      }, []);
      unit.unshift(psIrradiance.unit);
      // 滚动条
      newOptions.dataZoom[0].xAxisIndex = yData.map((item, index) => index);
      newOptions.dataZoom[1].xAxisIndex = yData.map((item, index) => index);
      // 图例
      newOptions.legend.data = yData.map(o => o.deviceName);
      // 倾斜面辐照度
      newOptions.legend.data.unshift(psIrradiance.indexName);
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.yAxis = [
        this.makeYAxis(0, {
          name: unit[0],
          type: 'value'
        }, true),
        this.makeYAxis(0, {
          name: unit[1],
          type: 'value'
        }, true)
      ];
      newOptions.tooltip = {
        className: 'solar-eye-tooptip deep-anly-tooptip',
        triggerOn: 'mousemove',
        enterable: true,
        confine: true,
        trigger: 'axis',
        alwaysShowContent: false,
        appendToBody: true,
        position: (point, params, dom, rect, size) => {
          let content = document.getElementById('insight-chart');
          let minTop = content.scrollTop;
          let maxTop = content.scrollTop + content.offsetHeight - dom.offsetHeight - 30;
          let minLeft = 0;
          let maxLeft = content.offsetWidth - dom.offsetWidth;
          let top = 0;
          let left = 0;
          if (minTop <= point[1] && point[1] <= maxTop) {
            top = point[1];
          } else if (point[1] < minTop) {
            top = minTop;
          } else {
            top = maxTop;
          }
          if (minLeft < point[0] + 20 && point[0] + 20 <= maxLeft) {
            left = point[0] + 20;
          } else {
            left = point[0] - dom.offsetWidth - 20;
          }
          return { top: top, 'left': left };
        },

        formatter: (params) => {
          console.log('params :>> ', params);
          const params1 = params.filter(item => item.value) || [];
          const params2 = params.filter(item => !item.value) || [];
          const params3 = orderBy(params1, 'value');
          let customerHtml = '';
          customerHtml += `<div style="display: flex; flex-direction: column;margin-top: 10px">`;
          customerHtml += `<div>${params[0] && params[0].axisValue}</div>`;
          [...params3, ...params2].forEach(item => {
            customerHtml += `
                <div style="margin-top: 10px;display: flex; justify-content: space-between">
                 <span> ${item.marker} ${item.seriesName}</span>
                 <span style="margin-left: 20px; float:right"> ${item.value || '--'}</span>
                </div>`;
          });
          customerHtml += '</div>';
          return customerHtml;
        }
      };
      newOptions.series = yData.map((item, index) => {
        return this.makeSeries(0, 1, {
          name: item.deviceName,
          data: item.data[0]
        }, 'line');
      });
      // 倾斜面辐照度
      newOptions.series.unshift(
        this.makeSeries(0, 0, {
          name: psIrradiance.indexName,
          data: psIrradiance.data
        }, 'line')
      );
      return this.assignLegend(newOptions);
    },
    // 方阵发电对比
    handle202 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData } = this.chartBaseData;
      newOptions.legend.data = yData.map(o => o.indexName.join(''));
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.yAxis = yData.reduce((acc, cur) => {
        acc.push(this.makeYAxis(0, {
          name: cur.unit.join(''),
          type: 'value'
        }, true));
        return acc;
      }, []);
      newOptions.series = yData.map((item, index) => {
        return this.makeSeries(0, 0, {
          name: item.indexName[index],
          data: item.data[0]
        }, 'bar');
      });
      return this.assignLegend(newOptions);
    },
    // 逆变器发电能力对比
    handle302 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const clonedSlightEnum = clone(slightEnum, true);
      const { xData, yData: [first = {}] } = this.chartBaseData;
      const [data0 = [], data1 = []] = first.data;
      const maxInData0 = Number(max(data0));
      // 计算饼图高于平均值、偏差率0-50%、偏差率大于50%
      const newSlightEnum = data1.reduce((acc, cur, index) => {
        if (!this.$isEmpty(cur)) {
          clonedSlightEnum[cur].value += Number(data0[index]);
        }
        return clonedSlightEnum;
      }, clonedSlightEnum);
        // 滚动条
      newOptions.dataZoom[0].xAxisIndex = xData.map((item, index) => index);
      newOptions.dataZoom[1].xAxisIndex = xData.map((item, index) => index);
      // 寻找日期类型
      const findItem = this.timeFilter.find(item => item.value == this.dateType);
      newOptions.grid = { top: '50%' };
      newOptions.legend.data = Object.keys(newSlightEnum).map(item => findItem.label + '等效利用小时数-' + newSlightEnum[item].name);
      newOptions.xAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.yAxis = [this.makeYAxis(0, {
        name: first.unit && first.unit[0],
        type: 'value'
        // max: parseInt(Math.round(maxInData0 * 2.5))
      }, true)];
      newOptions.series = [
        this.makeSeries(0, 0, {
          name: '',
          data: data0.map((item, index) => {
            const indexObj = newSlightEnum[data1[index]];
            const bg = indexObj && indexObj.background;
            return {
              value: item,
              itemStyle: {
                color: bg
              }
            };
          })
        }, 'bar')
      ];
      // 如果有数据画圆
      if (maxInData0 > 0) {
        // 数据分类
        const groupedData = groupBy(data1);
        newOptions.series.push(
          this.makeSeries(1, 1, {
            name: '',
            center: ['50%', '25%'],
            radius: ['15%', '25%'],
            z: 100,
            data: Object.keys(newSlightEnum).map(item => ({
              name: findItem.label + '等效利用小时数-' + newSlightEnum[item].name,
              value: (groupedData[item] || []).length,
              itemStyle: {
                color: newSlightEnum[item].background
              },
              customLabel: newSlightEnum[item].name
            })),
            label: {
              formatter: parmas => {
                return `${parmas.data.customLabel} (${parmas.data.value} 台)`;
              }
            }
          }, 'pie')
        );
      }
      return this.assignLegend(newOptions);
    },
    // 逆变器温度分析
    handle303 () {
      const newOptions = clone(this.chartBaseOptions, true);
      const { xData, yData: [first = {}] } = this.chartBaseData;
      const [maxTemperature, maxWorkTemperature, meanTemperature] = first.data;
      newOptions.dataZoom = [{
        type: 'slider', // slider表示有滑动块的，
        show: true,
        showDetail: false,
        left: 0,
        yAxisIndex: xData.map((_, index) => index), // 表示x轴折叠
        start: 0, // 数据窗口范围的起始百分比
        end: 100// 数据窗口范围的结束百分比
      }, {
        type: 'inside',
        yAxisIndex: xData.map((_, index) => index)
      }];
      newOptions.grid = {
        left: '12%',
        bottom: '5%'
      };
      newOptions.yAxis = this.makeXAxis(0, { data: xData }, 1);
      newOptions.legend.data = [first.indexName[0], first.indexName[2]];
      newOptions.xAxis = this.makeYAxis(0, {
        name: first.unit[0],
        type: 'value',
        position: 'top'
      }, true);
      newOptions.series = [
        this.makeSeries(0, 0, {
          name: first.indexName[0],
          data: maxTemperature.map((item, index) => {
            const indexObj = { value: item };
            if (maxWorkTemperature && maxWorkTemperature[index] && maxTemperature[index] > Number(maxWorkTemperature[index])) {
              indexObj.itemStyle = {
                color: '#FF0000'
              };
            }
            return indexObj;
          })
        }, 'bar'),
        this.makeSeries(0, 0, {
          name: first.indexName[2],
          data: meanTemperature
        }, 'bar')
      ];
      return this.assignLegend(newOptions);
    },
    // 时间向前步进
    backward () {
      const { dateType, chosenTime, timeFormat } = this;
      const enums = { 1: 'days', 2: 'months', 3: 'years' };
      const newTime = moment(chosenTime).add(-1, enums[dateType]).format(timeFormat);
      this.$set(this, 'chosenTime', newTime);
      this.renderChartIndicator();
    },
    // 时间吼吼步进
    forward () {
      const { dateType, chosenTime, timeFormat } = this;
      const enums = { 1: 'days', 2: 'months', 3: 'years' };
      const newTime = moment(chosenTime).add(1, enums[dateType]).format(timeFormat);
      this.$set(this, 'chosenTime', newTime);
      this.renderChartIndicator();
    },
  }
};
