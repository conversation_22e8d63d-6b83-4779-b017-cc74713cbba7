import echarts from '@/utils/enquireEchart';
import {chartColor, textColor} from "@/utils/color";
const mixin = {
  data () {
    return {

    };
  },
  methods: {
    makeXAxis (gridIndex, opt, length) {
      return echarts.util.merge({
        type: 'category',
        axisLine: { onZero: false, lineStyle: { color:chartColor.splitLine } },
        axisTick: { show: false },
        axisLabel: { show: gridIndex == length - 1, color: textColor.main }, // 从上往下排列 最下面一个显示x轴label
        splitLine: { show: false, lineStyle: { color: chartColor.splitLine } },
        gridIndex: gridIndex
      }, opt || {}, true);
    },
    makeYAxis (gridIndex, opt, is1n) {
      return echarts.util.merge({
        type: 'value',
        gridIndex: gridIndex,
        nameLocation: is1n ? 'end' : 'middle',
        nameGap: is1n ? 20 : 80,
        axisPointer: { show: false },
        axisLine: { onZero: false, show: false },
        axisLabel: { show: true, color: textColor.main, showMaxLabel: gridIndex == 0 }, // y轴刻度线 最上面一个y轴才显示顶部刻度label
        nameTextStyle: {
          color:  '#fff' 
        },
        splitLine: {
          lineStyle: {
            color: chartColor.splitLine
          }
        },
        scale: true
      }, opt || {}, true);
    },
    makeGrid (top, index, length) {
      // let colorBg = index % 2 == 1 ? '#111C2D' : '#152236';
      const colorBg ='transparent';
      const borderColor = 'transparent';
      return echarts.util.merge({
        top: top,
        bottom: length == index ? 60 : undefined,
        show: true,
        right: 100,
        borderColor: borderColor,
        backgroundColor: colorBg, // 隔行背景色设置
        height: length == 1 ? this.height - 160 : this.gridHeight // 根据y轴个数不同计算不同的高度
      }, {}, true);
    },
    makeSeries (index, yIndex, opt, type) {
      return echarts.util.merge({
        type: type == 'stack' ? 'bar' : type,
        stack: type == 'stack' ? 'total' + index : undefined,
        symbol: 'circle',
        symbolSize: type == 'scatter' ? 8 : 4,
        barGap: 0,
        xAxisIndex: index,
        yAxisIndex: yIndex,
        large: true,
        cursor: 'default',
        emphasis: {
          focus: 'series'
        },
        smooth: true
      }, opt || {}, true);
    }

  }
};

export { mixin };
