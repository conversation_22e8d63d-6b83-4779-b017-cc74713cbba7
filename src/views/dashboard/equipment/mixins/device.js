import { getManufacturersList, getDeviceModel } from '@/api/dataCenter';
export const DeviceMixins = {
  data () {
    return {
      manufacturersList: [],
      deviceModelList: [],
      versionList: []
    };
  },
  methods: {
    // 下拉框选项搜索
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    // 获取生产厂商下拉选项值
    getManufacturersList (deviceType) {
      getManufacturersList({ deviceType: deviceType }).then((res) => {
        this.manufacturersList = res.result_data;
      });
    },
    // 获取设备型号下拉选项值
    getDeviceModelList (deviceType, id, factoryId) {
      getDeviceModel({ deviceType: deviceType, id: id == '--' ? '' : id, factoryId: factoryId == '--' ? '' : factoryId }).then((res) => {
        this.deviceModelList = res.result_data.deviceModel;
      });
    }
  }

};
