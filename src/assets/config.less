@color-text-main: #85CAFF;
@color-text-second: #61A1D5;
@color-text-gray: #A1AAB6;
@color-text-highlight: #BDE2FF;
@color-text-white: #FFFFFF;

@color-primary: #FF7300;
@color-success: #67C239;
@color-text-light: #cccccc;
@color-text-link: #0050B3;
@color-text-black: #000000;
@color-text-warning: #FF1313;
@color-unit-white: #C8E4FF;
@color-text-bg: #124083;

// 文字颜色
.color-text-white {
  color: @color-text-white;
}

.color-text-primary {
  color: @color-primary;
}

.color-text-highlight {
  color: @color-text-highlight;
}

.color-text-main {
  color: @color-text-main;
}
.color-text-error {
  color: @di-color-text-error;
}
.color-text-success {
  color: @color-success;
}
.color-text-second {
  color: @color-text-second;
}

.color-text-gray {
  color: @color-text-gray;
}

.color-text-light {
  color: @color-text-light;
}

.color-text-warning {
  color: @color-text-warning;
}

.color-text-link {
  color: @color-text-link;
}

.color-text-black {
  color: @color-text-black;
}

.color-unit-white{
  color: @color-unit-white;
}

.color-text-bg{
  background: @color-text-bg;
}

.color-text-primary{
    color: @di-color-text-primary;
}

.border-main {
  border: 1px solid @color-text-main;
}

.border-highlight {
  border: 1px solid @color-text-highlight;
}

// 文本单行超出... 需要设置宽度
.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  /*不换行 控制单行文本*/
}

// flex 布局相关
.flex-baseline {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.flex-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between !important;
}

.flex-space-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-row-wrap {
  flex-flow: row wrap;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
}

.fiex-row {
  display: flex;
  flex-direction: row;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

//字重
.font-500 {
  font-weight: 500;
}

.font-400 {
  font-weight: 400;
}

.font-600 {
  font-weight: 600;
}

// 10字号 通过缩放实现
.font-10 {
  font-size: 12px;
  transform: scale(.8333333);
  transform-origin: center;
}

.font-12 {
  font-size: 12px;
  line-height: 20px;
}

.font-14 {
  font-size: 14px;
  line-height: 22px;
}

.font-16 {
  font-size: 16px;
  line-height: 24px;
}

.font-18 {
  font-size: 18px;
  line-height: 26px;
}

.font-20 {
  font-size: 20px;
  line-height: 28px;
}

.font-22 {
  font-size: 22px;
  line-height: 30px;
}

.font-24 {
  font-size: 24px;
  line-height: 32px;
}

.num-font-400 {
  font-family: 'D-DIN-PRO-400';
}

.num-font-500 {
  font-family: 'D-DIN-PRO-500';
}

.num-font-700 {
  font-family: 'D-DIN-PRO-700';
}


.text-center {
  text-align: center;
}

.text-blod {
  font-weight: bold;
}

// 透明度
.opacity-20 {
  opacity: .2;
}

.opacity-50 {
  opacity: .5;
}

.opacity-0 {
  opacity: 0;
}

.opacity-40 {
  opacity: .4;
}

.opacity-70 {
  opacity: .7;
}

.opacity-100 {
  opacity: 1;
}

// 旋转
.rotate-180 {
  transform: rotate(180deg);
}

.rotate-90 {
  transform: rotate(90deg);
}

.rotate-270 {
  transform: rotate(270deg);
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

// 100%宽高
.height-100 {
  height: 100%;
}

.width-100 {
  width: 100%;
}

.width-20 {
  width: 20%;
}

.width-height-100 {
  width: 100%;
  height: 100%;
}

// 定位
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.no-padding {
  padding: 0 !important;
}

.no-margin {
  margin: 0 !important;
}

.no-border {
  border: none !important;
}

.no-bg {
  background: transparent !important;
}

.bg-white {
  background: white !important;
}

.border-box {
  box-sizing: border-box;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer-none {
  cursor: not-allowed;
}

.hover-primary:hover {
  cursor: pointer;
  color: @color-primary !important;
}

// 边距
@arr: 0, 2, 4, 8, 10, 12, 14, 16, 18, 20, 24, 26, 28, 30, 32, 36, 40, 44, 48, 52, 54, 56, 60, 70, 128;

each(@arr, {
  @num: extract(@arr, @index);

  .margin-t-@{value} {
    margin-top:~"@{num}px";
  }

  .margin-r-@{value} {
    margin-right:~"@{num}px";
  }

  .margin-b-@{value} {
    margin-bottom:~"@{num}px";
  }

  .margin-l-@{value} {
    margin-left:~"@{num}px";
  }
   .padding-@{value} {
    padding:~"@{num}px";
  }
  .padding-t-@{value} {
    padding-top:~"@{num}px";
  }

  .padding-r-@{value} {
    padding-right:~"@{num}px";
  }

  .padding-b-@{value} {
    padding-bottom:~"@{num}px";
  }

  .padding-l-@{value} {
    padding-left:~"@{num}px";
  }
});

.for-loop-font-size(@index) when (@index >=12) {

  /* the statement */
  .font-size-@{index} {
    font-size: @index * 1px;
  }

  /* end of the statement */

  .for-loop-font-size(@index - 1);
}

.for-loop-font-size(64);

// 圆角
.radius-8 {
  border-radius: 8px;
}

// flex 间距
.flex-gap-8 {
  gap: 8px;
}

.flex-gap-12 {
  gap: 12px;
}

.flex-gap-16 {
  gap: 16px;
}

.flex-gap-24 {
  gap: 24px;
}

.flex-gap-36 {
  gap: 36px;
}