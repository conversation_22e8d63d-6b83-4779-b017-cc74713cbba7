@import '~ant-design-vue/dist/antd.less';
//自定义antd的样式
@di-input-bg: linear-gradient(180deg, #164588 0%, #16478C 57%, #15529D 100%);
@content-bg: linear-gradient(180deg, #1A4A8E 0%, rgba(26, 74, 142, 0.97) 0%, rgba(14, 64, 133, 0.94) 100%);
// 输入框 input
.color {
  border-color: @di-border-color-high;
  background: linear-gradient(180deg, #2466B8 0%, #184F9B 57%, #20529B 100%);
}
.ant-input {
  background: @di-input-bg;
  color: @di-color-text-white;
  border-color: @di-border-color-second;

  &:hover {
    .color();
  }

  &[disabled] {
    background: @di-input-bg;
    color: @di-color-text-white;
    border-color: @di-border-color-second;

    &:hover {
      .color();
    }
  }
}

// spin撑满容器
.ant-spin-container {
  width: 100%;
  height: 100%;
}

// 下拉选择框 select
.ant-select.ant-select-enabled {
  color: @di-color-text-white;

  .ant-select-selection {
    background: @di-input-bg;
    border-color: @di-border-color-second;
    &:hover {
      .color();
    }
    .ant-select-arrow {
      color: @di-color-text-main;
    }
  }

  .ant-select-selection__clear {
    background: linear-gradient(180deg, #2466B8 0%, #184F9B 57%, #20529B 100%);
    color: @di-color-text-second;
    border-radius: 50%;
  }
}
.ant-select.ant-select-enabled.ant-select-open {
  .ant-select-selection {
    .color();
  }

}
//日期选择框 ant-calendar-picker
.ant-calendar-picker {
  .ant-calendar-range-picker-separator {
    color: @di-color-text-white;
  }

  .ant-calendar-picker-clear {
    background: linear-gradient(180deg, #2466B8 0%, #184F9B 57%, #20529B 100%);
    color: @di-color-text-second;
    border-radius: 50%;
  }
  .anticon.anticon-calendar{
    color: @di-color-text-main;
  }
}

//分页器  page-pagination
.ant-pagination {
  color: @di-color-text-second;

  .ant-pagination-prev, .ant-pagination-next {
    .ant-pagination-item-link {
      color: @di-color-text-second;
    }
  }

  .ant-pagination-item-active {
    background-color: transparent;
  }

  .ant-pagination-item {
    color: @di-color-text-second;
  }
}

// 页签 ant-tabs
.ant-tabs {
  color: @di-color-text-second;

  .ant-tabs-bar {
    border-bottom-color: @di-border-color-default;

    .ant-tabs-nav-container {
      .ant-tabs-nav-wrap .ant-tabs-nav {
        .ant-tabs-ink-bar {
          height: 4px;
          background-color: @di-border-color-high;
        }

        .ant-tabs-tab-active {
          color: @di-color-text-main;
          font-weight: 500;
        }
      }

    }
  }
}

// 卡片 ant-card
.ant-card {
  background: @content-bg;
  color: @di-color-text-white;

  &.ant-card-bordered {
    border: 1px solid #255DAE;
  }
}

// 虚拟列表的样式
.virtual-area {
  .el-tree {
    color: #fff;

    .el-tree-node.is-current {
      .custom-tree-node {
        background-color: transparent;
      }
    }

    .tree-node-loading {
      color: @primary-color !important;
    }
  }

  .el-checkbox {
    .el-checkbox__input {
      .el-checkbox__inner {
        border: 1px solid @di-border-color-default;
        background: transparent;
      }
    }

    .el-checkbox__input.is-checked {
      .el-checkbox__inner {
        background-color: @primary-color;
        border-color: @di-border-color-high;
      }
    }

    .el-checkbox__input.is-disabled {
      .el-checkbox__inner::after {
        border-color: @di-border-color-default;
      }

      .el-checkbox__inner {
        background-color: transparent;
        border-color: @di-border-color-default;
      }
    }

    .el-checkbox__input.is-indeterminate {
      .el-checkbox__inner {
        background-color: @primary-color;
        border-color: @di-border-color-high;
      }
    }
  }
}

// checkbox 选中框
.ant-checkbox:not(.ant-checkbox-checked) .ant-checkbox-inner,
.ant-checkbox.ant-checkbox-indeterminate .ant-checkbox-inner {
  border: 1px solid @di-border-color-default !important;
  background: transparent !important;
}

.ant-tree {
  .anticon {
    color: rgba(255, 255, 255, 0.6);
  }

  li .ant-tree-node-content-wrapper {
    color: @di-color-text-white;
  }

  .ant-tree-treenode-switcher-close:hover,
  .ant-tree-node-content-wrapper:hover,
  li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background: rgba(78, 121, 177, 0.20) !important;
  }
}

.ant-tree-checkbox:not(.ant-tree-checkbox-checked) .ant-tree-checkbox-inner,
.ztree .button.checkbox_false_full, .button.checkbox_false_part,
.vxe-table .vxe-cell--checkbox .vxe-checkbox--unchecked-icon:before,
.ant-checkbox:not(.ant-checkbox-checked) .ant-checkbox-inner,
.ant-checkbox.ant-checkbox-indeterminate .ant-checkbox-inner {
  border: 1px solid @di-border-color-default;
  background: transparent;
}

// modal confirm确认框
.ant-modal .ant-modal-header .ant-modal-title,
.ant-modal .ant-modal-confirm-body .ant-modal-confirm-content,
.ant-modal .ant-modal-confirm-title {
  color: @di-color-text-white;
}

// radio-button
.ant-radio-group {
  .ant-radio-button-wrapper {
    background: transparent;
    color: #6DB0E7;
    border: 1px solid #6DB0E7;

    &.ant-radio-button-wrapper-checked,&:hover {
      border-left: none;
      background: rgba(133, 202, 255, 0.1);
      border: 1px solid @di-color-text-highlight;
      box-shadow: none;
      color: @di-color-text-highlight;
    }
  }

  .ant-radio-button-wrapper:not(:first-child)::before {
    background-color: @di-border-color-high;
  }
}

// select 下拉框样式调整
.ant-select-dropdown {
  color: @di-color-text-main;
  background: #072E6A;
  border: 1px solid @di-border-color-default;
  /* 下拉菜单选择器气泡确认框 */
  box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05), 0px 8px 10px 1px rgba(0, 0, 0, 0.06), 0px 5px 5px -3px rgba(0, 0, 0, 0.1);

  .ant-select-dropdown-menu-item {
    color: @di-color-text-main;
    margin: 0 11px;
    padding: 5px 8px;
  }

  .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected:not(.ant-select-dropdown-menu-item-disabled) {
    background: #40AAFF;
    color: @di-color-text-white;
    border-radius: 4px;
  }

  .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
    background-color: transparent;
  }

  .ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled):not(.ant-select-dropdown-menu-item-selected) {
    background-color: rgba(64, 170, 255, 0.2);
    border-radius: 4px;
  }

}


// 多选select
.ant-select-dropdown--multiple .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected:not(.ant-select-dropdown-menu-item-disabled) {
  background: rgba(64, 170, 255, 0.2);
  margin-bottom: 2px;
}

.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item:not(.ant-select-dropdown-menu-item-selected):hover .ant-select-selected-icon {
  color: transparent;
}

.ant-select-selection--multiple .ant-select-selection__choice {
  background: #265BA5;
  border: none;
  color: @di-color-text-white;
}

.ant-select-selection--multiple .ant-select-selection__choice__remove {
  color: @di-color-text-white;
}

// dropdown 下拉框
.ant-dropdown {
  background: #274D81;
  /* 下拉菜单选择器气泡确认框 */
  box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05), 0px 8px 10px 1px rgba(0, 0, 0, 0.06), 0px 5px 5px -3px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  color: @di-color-text-white;
  z-index: 9999999;

  .ant-dropdown-menu {
    background-color: transparent;
    color: @di-color-text-white;

    .ant-dropdown-menu-item {
      color: @di-color-text-white;

      a {
        color: @di-color-text-white;

        &:hover {
          color: #7DC0F7;
        }
      }

      .router-link-active {
        color: @di-color-text-main;
      }

    }

    .ant-dropdown-menu-item:hover {
      color: #7DC0F7;
      background-color: transparent;
    }
  }
}

// mmzuo add 时间控件
.ant-calendar {
  border: 1px solid #344D72;
  background-color: #072E6A;
  box-sizing: border-box;
  border: 0.5px solid #61A1D5;

  /* 下拉菜单选择器气泡确认框 */
  box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05), 0px 8px 10px 1px rgba(0, 0, 0, 0.06), 0px 5px 5px -3px rgba(0, 0, 0, 0.1);

  .ant-calendar-date, .ant-calendar-year-select, .ant-calendar-month-select {
    color: #85CAFF;
  }

  .ant-calendar-column-header-inner {
    color: #61A1D5;
  }

  .ant-calendar-next-month-btn-day .ant-calendar-date, .ant-calendar-last-month-cell .ant-calendar-date {
    color: #36668D;
  }

  .ant-calendar-header .ant-calendar-prev-month-btn::before, .ant-calendar-header .ant-calendar-prev-month-btn::after {
    border-color: #85CAFF;
    border-width: 2.5px 0 0 2.5px;
  }
  // 月日期选择器
  .ant-calendar-month-panel {
    background-color: #072E6A;
    .ant-calendar-month-panel-header{
      border-bottom-color: rgba(189, 226, 255, 0.19);
      .ant-calendar-month-panel-year-select{
        color: #85CAFF;
      }
    }
    .ant-calendar-month-panel-body .ant-calendar-month-panel-month {
      color: #85CAFF;
    }
    .ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month{
      background: #15529d;
    }
    .ant-calendar-month-panel-prev-year-btn::before,  .ant-calendar-month-panel-prev-year-btn::after, .ant-calendar-month-panel-next-year-btn::before,  .ant-calendar-month-panel-next-year-btn::after{
      border-color: #85CAFF;
      border-width: 2.5px 0 0 2.5px;
    }
   .ant-calendar-month-panel-prev-year-btn:hover::before,  .ant-calendar-month-panel-prev-year-btn:hover::after, .ant-calendar-month-panel-next-year-btn:hover::before,  .ant-calendar-month-panel-next-year-btn:hover::after{
      border-color: #40AAFF;
    }
    .ant-calendar-month-panel-month:hover{
      background: #15529d;
    }
  }
}

.ant-calendar-header .ant-calendar-prev-century-btn::before, .ant-calendar-header .ant-calendar-prev-decade-btn::before, .ant-calendar-header .ant-calendar-prev-year-btn::before, .ant-calendar-header .ant-calendar-prev-century-btn::after, .ant-calendar-header .ant-calendar-prev-decade-btn::after, .ant-calendar-header .ant-calendar-prev-year-btn::after {
  border-color: #85CAFF;
  border-width: 2.5px 0 0 2.5px;
}
.ant-calendar-header .ant-calendar-prev-year-btn:hover::before, .ant-calendar-header .ant-calendar-prev-year-btn:hover::after{
  border-color: #40AAFF;
}
.ant-calendar-header .ant-calendar-prev-month-btn:hover::before{
  border-color: #40AAFF;
}

.ant-calendar-header .ant-calendar-pre-month-btn:hover::before, .ant-calendar-header .ant-calendar-pre-month-btn:hover::after {
  border-color: #40AAFF;
}

.ant-calendar-header .ant-calendar-next-month-btn:hover::before, .ant-calendar-header .ant-calendar-next-month-btn:hover::after {
  border-color: #40AAFF;
}

.ant-calendar-header .ant-calendar-next-month-btn::before, .ant-calendar-header .ant-calendar-next-month-btn::after {
  border-color: #85CAFF;
  border-width: 2.5px 0 0 2.5px;
}

.ant-calendar-header .ant-calendar-next-century-btn::before, .ant-calendar-header .ant-calendar-next-decade-btn::before, .ant-calendar-header .ant-calendar-next-year-btn::before, .ant-calendar-header .ant-calendar-next-century-btn::after, .ant-calendar-header .ant-calendar-next-decade-btn::after, .ant-calendar-header .ant-calendar-next-year-btn::after {
  border-color: #85CAFF;
  border-width: 2.5px 0 0 2.5px;
}

.ant-calendar-header .ant-calendar-next-century-btn:hover::before, .ant-calendar-header .ant-calendar-next-decade-btn:hover::before, .ant-calendar-header .ant-calendar-next-year-btn:hover::before, .ant-calendar-header .ant-calendar-next-century-btn:hover::after, .ant-calendar-header .ant-calendar-next-decade-btn:hover::after, .ant-calendar-header .ant-calendar-next-year-btn:hover::after {
  border-color: #40AAFF;
}

.ant-calendar-range .ant-calendar-input, .ant-calendar-range .ant-calendar-time-picker-input {
  background: #072E6A;
  color: #fff;
}


.ant-calendar-range .ant-calendar-body, .ant-calendar-range .ant-calendar-month-panel-body, .ant-calendar-range .ant-calendar-year-panel-body, .ant-calendar-range .ant-calendar-decade-panel-body {
  border-top-color: rgba(189, 226, 255, 0.19);
}

.ant-calendar-input-wrap {
  border-bottom-color: rgba(189, 226, 255, 0.19);
}

.ant-calendar-range .ant-calendar-in-range-cell::before, .ant-calendar-date:hover {
  background: #15529D;
}

.ant-calendar-range .ant-calendar-selected-start-date .ant-calendar-date, .ant-calendar-range .ant-calendar-selected-end-date .ant-calendar-date {
  background: #15529D;
}

.ant-calendar-today .ant-calendar-date {
  background: #134788
}
.ant-calendar-range-middle {
  color: #fff;
  opacity: .5;
}
.ant-calendar-disabled-cell .ant-calendar-date{
  background: #193E76;
  color: #36668D;
}
.ant-calendar-disabled-cell .ant-calendar-date:hover {
  background: #193E76;
}

// 日期选择器
.ant-calendar .ant-calendar-input{
  background: #072E6A;
  color: #fff;
}
.ant-calendar .ant-calendar-header{
  border-bottom-color: rgba(189, 226, 255, 0.19);
}
.ant-calendar .ant-calendar-footer{
  border-top-color: rgba(189, 226, 255, 0.19);
}
.ant-calendar .ant-calendar-selected-day .ant-calendar-date{
  background: #15529D;
}

// 气泡框样式
.ant-popover{
  .ant-popover-inner{
    background: #164587;
    border: 1px solid #255DAE;
    box-shadow: 0px 4px 10px 0px rgba(0, 34, 82, 0.3);
  }
}

  // empty空数据
.ant-empty{
  color: @di-color-text-white;
}

// 面包屑
.ant-breadcrumb{
  color: @di-border-color-default;
  span:last-child a{
    color: @di-border-color-default;
  }
  .ant-breadcrumb-separator{
    color: @di-border-color-second;
  }
  .ant-breadcrumb-link, a{
    color: @di-border-color-default;
  }
}

// checkbox
.ant-checkbox-wrapper{
  color: @di-color-text-white;
}