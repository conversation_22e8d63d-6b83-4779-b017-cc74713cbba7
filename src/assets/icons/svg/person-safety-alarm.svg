<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="35"
     height="32" viewBox="0 0 35 32">
    <defs>
        <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="master_svg0_507_53826"
                        gradientTransform="translate(17.5 15.999999046325684) rotate(90) scale(15.999999046325684 17.5)">
            <stop offset="9.38815250992775%" stop-color="#43A1FF" stop-opacity="0.20999999344348907"/>
            <stop offset="24.883882701396942%" stop-color="#B2D9FF" stop-opacity="1"/>
            <stop offset="36.06991767883301%" stop-color="#4AA4FF" stop-opacity="0.5799999833106995"/>
            <stop offset="46.59181237220764%" stop-color="#43A1FF" stop-opacity="1"/>
            <stop offset="60.21257042884827%" stop-color="#47A4FF" stop-opacity="0.2199999988079071"/>
            <stop offset="74.67275261878967%" stop-color="#BBFFFF" stop-opacity="1"/>
            <stop offset="83.94553065299988%" stop-color="#4EA7FF" stop-opacity="0.20999999344348907"/>
            <stop offset="90.6508207321167%" stop-color="#96CCFF" stop-opacity="1"/>
            <stop offset="94.42806839942932%" stop-color="#43A1FF" stop-opacity="0.20000000298023224"/>
        </radialGradient>
        <mask id="master_svg1_789_070746" style="mask-type:alpha" maskUnits="userSpaceOnUse">
            <g>
                <path d="M25.2776359375,2.0006253719329834L33.0554359375,16.000625371932983L25.2776359375,30.000625371932983L9.7221059375,30.000625371932983L1.9443359375,16.000625371932983L9.7221059375,2.0006253719329834L25.2776359375,2.0006253719329834Z"
                      fill="#FFFFFF" fill-opacity="1"/>
            </g>
        </mask>
        <linearGradient x1="0.8689643740653992" y1="0.5245713591575623" x2="0.17602230608463287" y2="0.8064851760864258"
                        id="master_svg2_507_50922">
            <stop offset="0%" stop-color="#59ADFF" stop-opacity="1"/>
            <stop offset="100%" stop-color="#59ADFF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient x1="0.8040124177932739" y1="0.5" x2="0.20526567101478577" y2="0.1510208398103714"
                        id="master_svg3_507_49908">
            <stop offset="0%" stop-color="#59ADFF" stop-opacity="1"/>
            <stop offset="100%" stop-color="#59ADFF" stop-opacity="0"/>
        </linearGradient>
        <radialGradient cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" id="master_svg4_507_53319"
                        gradientTransform="translate(17.499876976013184 16.000630140304565) rotate(90) scale(14.000004768371582 15.555541038513184)">
            <stop offset="9.38815250992775%" stop-color="#43A1FF" stop-opacity="1"/>
            <stop offset="24.883882701396942%" stop-color="#B2D9FF" stop-opacity="1"/>
            <stop offset="36.06991767883301%" stop-color="#4AA4FF" stop-opacity="1"/>
            <stop offset="46.59181237220764%" stop-color="#43A1FF" stop-opacity="1"/>
            <stop offset="60.21257042884827%" stop-color="#47A4FF" stop-opacity="1"/>
            <stop offset="74.67275261878967%" stop-color="#BBFFFF" stop-opacity="1"/>
            <stop offset="77.48953700065613%" stop-color="#59B0FF" stop-opacity="1"/>
            <stop offset="83.94553065299988%" stop-color="#4EA7FF" stop-opacity="1"/>
            <stop offset="90.6508207321167%" stop-color="#96CCFF" stop-opacity="1"/>
            <stop offset="94.42806839942932%" stop-color="#43A1FF" stop-opacity="1"/>
        </radialGradient>
        <filter id="master_svg5_789_070755" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB"
                x="-0.30523681640625" y="-0.30523681640625" width="4.313814878463745" height="2.965151309967041">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="0.0763092041015625" result="effect1_foregroundBlur"/>
        </filter>
        <filter id="master_svg6_789_070753" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB"
                x="-0.30523681640625" y="-0.30523681640625" width="4.313814878463745" height="2.965151309967041">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="0.0763092041015625" result="effect1_foregroundBlur"/>
        </filter>
        <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="master_svg7_507_53365">
            <stop offset="0%" stop-color="#FFFFFF" stop-opacity="1"/>
            <stop offset="100%" stop-color="#99E3FF" stop-opacity="1"/>
        </linearGradient>
    </defs>
    <g>
        <g>
            <path d="M35,16L26.25,0L8.74999,0L0,16L8.74999,32L26.25,32L35,16ZM34.5651,16L26.0237,0.381572L8.97622,0.381572L0.434904,16L8.97622,31.6184L26.0237,31.6184L34.5651,16Z"
                  fill-rule="evenodd" fill="url(#master_svg0_507_53826)" fill-opacity="1"/>
        </g>
        <g>
            <g>
                <path d="M25.2776359375,2.0006253719329834L33.0554359375,16.000625371932983L25.2776359375,30.000625371932983L9.7221059375,30.000625371932983L1.9443359375,16.000625371932983L9.7221059375,2.0006253719329834L25.2776359375,2.0006253719329834Z"
                      fill="#134387" fill-opacity="1"/>
            </g>
            <g>
                <path d="M18.82643476867676,23.719985600219726L7.212434768676758,23.719985600219726L7.212434768676758,23.338413600219727L18.82643476867676,23.338413600219727L18.82643476867676,23.719985600219726Z"
                      fill-rule="evenodd" fill="url(#master_svg2_507_50922)" fill-opacity="0.3499999940395355"/>
            </g>
            <g style="opacity:0.6000000238418579;">
                <path d="M13.170235633850098,5.434196949005127L14.388505633850098,6.736346949005127L17.398305633850097,6.736346949005127L21.3158656338501,10.495526949005127L30.345335633850098,10.495526949005127L29.724335633850096,9.758416949005127L21.5786156338501,9.758416949005127L17.135555633850096,5.434196949005127L13.170235633850098,5.434196949005127Z"
                      fill="#1A56A7" fill-opacity="1"/>
            </g>
            <g style="opacity:0.6000000238418579;">
                <path d="M8.730077375335693,10.76092386274707L13.817317375335694,15.99351676574707L8.730077375335693,21.22610676574707L7.355148792265694,19.811896765747072L11.067457375335692,15.99351676574707L7.355148792265694,12.17513676574707L8.730077375335693,10.76092386274707ZM15.644137375335694,10.76092386274707L20.731357375335694,15.99351676574707L15.644137375335694,21.22610676574707L14.269207375335693,19.811896765747072L17.981557375335694,15.99351676574707L14.269207375335693,12.17513676574707L15.644137375335694,10.76092386274707ZM22.557957375335693,10.76092386274707L27.645157375335693,15.99351676574707L22.557957375335693,21.22610676574707L21.183057375335693,19.811896765747072L24.895357375335692,15.99351676574707L21.183057375335693,12.17513676574707L22.557957375335693,10.76092386274707Z"
                      fill-rule="evenodd" fill="#1657AF" fill-opacity="1"/>
            </g>
            <g style="opacity:0.699999988079071;">
                <path d="M1.0720560550689697,13.73597184817791Q4.33317605506897,21.61857184817791,6.61599605506897,23.79887184817791Q21.86175605506897,15.74847184817791,27.07955605506897,4.76328184817791L27.07955605506897,0.8218748481779099L9.061846055068969,0.15117184817790985L1.0720560550689697,13.73597184817791Z"
                      fill="url(#master_svg3_507_49908)" fill-opacity="0.18000000715255737"/>
            </g>
            <g>
                <path d="M33.0554359375,16.000625371932983L25.2776359375,2.0006253719329834L9.7221059375,2.0006253719329834L1.9443359375,16.000625371932983L9.7221059375,30.000625371932983L25.2776359375,30.000625371932983L33.0554359375,16.000625371932983ZM31.7459359375,16.000625371932983L24.6041359375,3.1453453719329834L10.3956559375,3.1453453719329834L3.2538459375,16.000625371932983L10.3956659375,28.855925371932983L24.6041359375,28.855925371932983L31.7459359375,16.000625371932983Z"
                      fill-rule="evenodd" fill="url(#master_svg4_507_53319)" fill-opacity="1"/>
            </g>
            <g filter="url(#master_svg5_789_070755)">
                <path d="M9.467041015625,25.6722354888916L10.775191015625,27.1034454888916L11.074601015625,27.431015488891603L11.586051015625,27.5764354888916L13.170381015625,28.026915488891603L10.775191015625,28.026915488891603L9.467041015625,25.6722354888916Z"
                      fill-rule="evenodd" fill="#B7EBFF" fill-opacity="1"/>
            </g>
            <g transform="matrix(-1,0,0,-1,50.92229461669922,12.864063262939453)" filter="url(#master_svg6_789_070753)">
                <path d="M25.46114730834961,6.432031631469727L26.76929730834961,7.863241631469727L27.06870730834961,8.190811631469726L27.58015730834961,8.336231631469726L29.16448730834961,8.786711631469727L26.76929730834961,8.786711631469727L25.46114730834961,6.432031631469727Z"
                      fill-rule="evenodd" fill="#B7EBFF" fill-opacity="1"/>
            </g>
            <g>
                <path d="M15.554252109375,12.4356521875C15.459032109375,11.7650521875,15.361782109375,11.0951421875,15.266562109375,10.4245421875C15.254842109375,10.3488021875,15.245302109375,10.2727321875,15.237962109375001,10.1964421875C15.157622109375,9.2568081875,15.213452109375,9.1696531875,16.150362109375,9.0048771875C16.763312109375,8.8950709875,17.394242109375,8.9454980875,17.981972109375,9.1512691875C18.270672109375,9.2506801875,18.385062109375,9.4134131875,18.372802109375,9.7096031875C18.338762109375,10.5450621875,18.167172109375,11.3628121875,18.058232109375,12.1880521875C18.043812109375,12.2728321875,18.040382109375,12.3591021875,18.048012109375,12.4447521875C18.342842109375,11.7093921875,18.527372109375,10.9461021875,18.677162109375,10.1732921875L18.678462109375,10.1666321875C18.747972109375,9.8089801875,18.748342109375002,9.8070701875,19.028502109374998,10.0439221875C19.791792109375002,10.6880521875,20.260922109375002,11.4983121875,20.399142109375,12.4862921875C20.419572109375,12.6333621875,20.474042109375,12.7436621875,20.559832109375,12.8662321875C20.781762109375002,13.1857821875,20.756762109375,13.551632187500001,20.732162109375,13.912802187499999C20.728462109375002,13.9672021875,20.724762109375,14.0214921875,20.721862109375,14.0754921875C20.717162109375,14.1708321875,20.617752109374997,14.1708221875,20.539692109375,14.1708221875L20.538042109375,14.1708221875C20.504782109375,14.171052187499999,20.471522109375,14.1713121875,20.438262109375,14.1715821875C20.272042109375,14.1729021875,20.105892109375,14.1742221875,19.940222109375,14.1708221875C19.934102109375,14.1707421875,19.927992109374998,14.1706521875,19.921912109375,14.1705621875C19.772192109375,14.1683521875,19.632582109375,14.1662821875,19.535092109375,14.3226621875C19.478172109375002,14.4133821875,19.372712109375,14.4111521875,19.273572109375,14.4090721875C19.258872109374998,14.408762187499999,19.244302109375,14.4084521875,19.230052109375002,14.4084521875L14.348722109375,14.4084521875C14.333462109375,14.4084521875,14.316502109375,14.4093421875,14.299072109375,14.410262187499999C14.241822109375,14.413262187499999,14.179512109375,14.4165321875,14.156022109375,14.3893921875C13.930182109375,14.1319421875,13.648372109375,14.1507421875,13.369072109375,14.169372187499999C13.281232109375,14.1752321875,13.193652109375,14.1810821875,13.108132109375,14.178312187500001C12.909312109375,14.1715021875,12.826922109375,14.1156721875,12.840542109375,13.9114021875C12.846452109375,13.822652187500001,12.846462109375,13.7328121875,12.846462109375,13.643092187499999C12.846482109375,13.319832187500001,12.846492109375,12.9982521875,13.123112109375,12.7354921875C13.174862109374999,12.6857921875,13.166692109375,12.5714021875,13.178942109375,12.4856021875C13.342362109375,11.3866421875,13.889792109375,10.522592187499999,14.826712109375,9.8342051875C15.020582109374999,10.7362321875,15.192712109375,11.6104021875,15.554252109375,12.4356521875ZM13.427132109375,14.643132187500001C13.431072109375,14.689112187500001,13.435172109375,14.736972187500001,13.423432109375,14.7824721875C13.503102109375,15.344202187499999,13.552122109375,15.9290921875,14.024662109375,16.3601021875C14.069602109375,16.4016321875,14.093432109375,16.4731321875,14.113862109374999,16.5344121875C14.408692109375,17.401182187499998,14.988132109375,18.0337321875,15.752772109375,18.5083221875C16.360812109375,18.8855321875,17.002892109375,18.9393321875,17.637492109375,18.602282187500002C17.721172109375,18.5580021875,17.802642109375,18.5116021875,17.881832109375,18.4630421875L17.881832109375,16.6309421875L19.908622109375003,15.8202221875C20.052052109374998,15.4791521875,20.089482109375,15.0995721875,20.130922109375,14.7218721875C20.132622109375,14.7072721875,20.135062109375,14.6919421875,20.137562109375,14.6762421875C20.151032109375002,14.5915321875,20.166262109374998,14.495752187499999,20.077812109375,14.4440621875C19.970232109375,14.3814221875,19.865372109375002,14.4188721875,19.776862109375,14.5107921875C19.651572109375,14.6401621875,19.493602109375,14.6680721875,19.316572109375002,14.667392187499999C17.612972109375,14.6639921875,15.910062109375,14.6653521875,14.207142109374999,14.667392187499999C14.058032109375,14.667392187499999,13.916402109375,14.6585421875,13.796562109375,14.5570921875C13.782322109375,14.545152187500001,13.768142109374999,14.5320621875,13.753812109375,14.518842187499999C13.683202109375,14.4536821875,13.609072109375,14.3852821875,13.507182109375,14.4345321875C13.413162109375,14.4800021875,13.419872109375,14.558382187500001,13.427132109375,14.643132187500001ZM19.515702109375,16.3807321875L21.282962109375,15.6738321875L24.308862109375,16.8844621875L24.308862109375,19.3050421875C24.308862109375,20.9637421875,23.228962109375,22.3295421875,21.645862109375003,22.9369421875C21.287062109375,23.0744421875,21.278162109375,23.0744421875,20.919362109375,22.9369421875C20.552462109375,22.7961421875,20.212602109375,22.6146421875,19.906942109375002,22.3982421875L19.906862109375,22.3982421875C18.893662109375,21.6807421875,18.256332109375002,20.5793421875,18.256332109375002,19.3050421875L18.256332109375002,18.9011121875L18.256382109375,18.901082187500002L18.256382109375,18.2037621875L18.256332109375002,18.2038021875L18.256332109375002,16.884482187499998L18.256382109375,16.8844621875L19.515702109375,16.3807321875L19.515702109375,16.3807321875ZM20.141052109375,19.1014421875L20.404762109375,19.3673421875L20.618132109375,19.5825421875L20.881862109375,19.848442187499998L20.881862109375,19.848342187500002L20.881862109375,19.848342187500002L22.729162109375,17.984782187500002L23.113862109375,18.3728921875L22.285262109374997,19.209642187500002L22.285162109375,19.209642187500002L21.145762109375,20.3602421875L20.881862109375,20.6266421875L20.881862109375,20.6259421875L19.755712109375,19.4895421875L20.141052109375,19.1014421875ZM14.688482109375,18.3045321875C15.441872109375,19.3365421875,16.808812109375,19.6098421875,17.881832109375,19.1144421875L17.881832109375,19.3050421875Q17.881832109375,20.6547421875,18.694412109375,21.7397421875Q18.965372109375,22.1015421875,19.299332109375,22.3982421875C18.912632109375,22.3982421875,18.525942109375002,22.3981421875,18.139262109375,22.3981421875C17.688482109375002,22.3981421875,17.237722109375,22.3980421875,16.787002109375,22.3980421875L11.378659109375,22.3980421875C10.801261109375,22.3980421875,10.628995009375,22.2271421875,10.710021409375,21.6599421875C10.806708109375,20.9770421875,10.929269109375,20.296842187499998,11.073619109375,19.622742187500002C11.136942109375,19.3265421875,11.320102109375,19.0916421875,11.644208109375,18.9936421875C12.520522109375,18.7300921875,13.396832109375,18.4624921875,14.267692109375,18.1826521875C14.456302109375,18.1213721875,14.572732109375,18.1458821875,14.688482109375,18.3045321875Z"
                      fill-rule="evenodd" fill="url(#master_svg7_507_53365)" fill-opacity="1"/>
            </g>
        </g>
    </g>
</svg>