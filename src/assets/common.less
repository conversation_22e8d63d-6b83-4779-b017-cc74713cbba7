@import "./customAntd.less";

@font-face {
  font-family: 'D-DIN-PRO-400';
  src: url("font/D-DIN-PRO-400-Regular.otf");
}

@font-face {
  font-family: 'D-DIN-PRO-500';
  src: url("font/D-DIN-PRO-500-Medium.otf");
}

@font-face {
  font-family: 'D-DIN-PRO-700';
  src: url("font/D-DIN-PRO-700-Bold.otf");
}
// 文字相关
@font-face {
  font-family: 'NeoGram-DemiBold';
  src: url('font/NeoGram-DemiBold.ttf');
  font-display: swap;
}
.main-parent, .main-parent-custom {
  width: 100%;
  height: 100%;
  overflow: hidden auto;
}

.linear-bg-border {
  border: 1px solid transparent;
  border-radius: 4px;
  position: relative;
  background: linear-gradient(270deg, rgba(4, 28, 63, 0.2) 0%, rgba(9, 37, 75, 0) 100%);
  background-clip: padding-box; /*important*/
}

.linear-bg-border:before {
  opacity: .2;
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: -1;
  margin: -1px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  border-radius: inherit; /*important*/
  background: linear-gradient(270deg, #61A1D5 0%, rgba(97, 161, 213, 0) 100%);
}


.ant-spin-container {
  width: 100%;
  height: 100%;
}


.no-data {
  background: url("../assets/images/2dMap/no-data.png") no-repeat center;
  background-size: contain;
  width: 210px;
  height: 140px;
}

.no-config {
  background: url("../assets/images/2dMap/no-config.png") no-repeat center;
  background-size: contain;
  //width: 110px;
  //height: 88px;
  width: 99px;
  height: 79px;
}

/*
echart 公共样式
*/
.alarm-device-tooltip {
  //padding: 8px!important;
  background: linear-gradient(270deg, rgba(25, 73, 124, 0.89) 0%, rgba(26, 70, 117, 0.79) 100%);
  box-sizing: border-box;
  position: relative;
  border: 0 !important;
  display: flex;
  justify-content: space-between;
}

.di-chart-tooltip {
  box-sizing: border-box;
  background: #083A80 !important;
  border-radius: 4px;
  padding: 0 !important;

  .title {
    background-image: linear-gradient(180deg, #1752A5 0%, #0B479A 26%, #1154A5 44%, #328ED7 100%);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @di-color-text-white;

    .right-status {
      background: linear-gradient(135deg, #F87373 0%, #F6504D 100%);
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
      border-radius: 10px 2px 10px 2px;
      height: 20px;
      font-size: 12px;
      padding: 0 8px;
      color: #FFFFFF;
      font-weight: 600;
      line-height: 20px;
    }
  }

  .content {
    padding: 8px 0;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;

    .content-item {
      display: flex;
      justify-content: space-between;
      padding: 0 8px;
      align-items: center;
      flex-wrap: nowrap;

      &:not(:last-child) {
        margin-bottom: 4px;
      }

      .tooltip-item-label {
        color: rgba(255, 255, 255, .7);
        margin-left: 4px;
      }
    }

    .content-unit {
      width: 40px;
      display: inline-block;
      color: @di-color-text-gray;
    }
  }
}

.map-alarm-cyclic {
  z-index: 999;
  width: 320px;
  height: 257px;
  //transform: translate(-50%, -100%);

  .map-alarm-window {
    width: 320px;
    height: 216px;
    background-size: cover;
    padding: 12px 12px 0px;
    position: relative;

    .alarm-map-img {
      width: 296px;
      height: 166px;
      position: relative;
      z-index: 1;

      .img-label {
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 28px;
        background: linear-gradient(180deg, rgba(0, 17, 35, 0) 0%, rgba(0, 17, 35, 0.7) 100%);
      }
    }
  }

  .alarm-window-grade1 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-bg1.png");
  }

  .alarm-window-grade2 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-bg2.png");
  }

  .alarm-window-grade3 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-bg3.png");
  }

  .alarm-window-grade4 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-bg4.png");
  }

  .map-alarm-icon {
    width: 31px;
    height: 41px;
    background-size: cover;
  }

  .alarm-icon-grade1 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-marker1.png");
  }

  .alarm-icon-grade2 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-marker2.png");
  }

  .alarm-icon-grade3 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-marker3.png");
  }

  .alarm-icon-grade4 {
    background-image: url("../assets/images/2dMap/2d-map-alarm-marker4.png");
  }

  .alarm-window-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .map-alarm-window:hover {
    .window-bg1 {
      background: rgba(255, 142, 142, 0.2);
      box-shadow: 0px 2px 7px 0px rgba(255, 142, 142, 0.5);
      z-index: 0;
    }

    .window-bg2 {
      background: rgba(255, 165, 110, 0.2);
      box-shadow: 0px 2px 7px 0px rgba(255, 165, 110, 0.5);
      z-index: 0;
    }

    .window-bg3 {
      background: rgba(104, 85, 255, 0.2);
      box-shadow: 0px 2px 7px 0px rgba(104, 85, 255, 0.5);
      z-index: 0;
    }

    .window-bg4 {
      background: rgba(110, 166, 187, 0.2);
      box-shadow: 0px 2px 7px 0px rgba(110, 166, 187, 0.5);
      z-index: 0;
    }
  }

}

.leaflet-div-icon {
  background: transparent !important;
  border: none !important;
}

// 告警等级标志样式
.alarm-marker {
  width: 38px;
  height: 20px;
  border-radius: 9px;
}

.alarm-marker-bg-1 {
  background: linear-gradient(180deg, #F07072 0%, #8C1B1D 100%);
}

.alarm-marker-bg-2 {
  background: linear-gradient(180deg, #FFA56E 0%, #B6561B 100%);
}

.alarm-marker-bg-3 {
  background: linear-gradient(180deg, #BB6EFF 0%, #591BB6 100%);
}

.alarm-marker-bg-4 {
  background: linear-gradient(180deg, #B9CCD3 0%, #59737C 100%);
}


// 台账搜索项的公共样式
.search-item {
  display: inline-flex;
  width: 100%;
  align-items: center;

  .search-label {
    width: auto;
    text-align: right;
    align-items: center;
    white-space: nowrap;
    color: @di-color-text-main;
    font-size: 14px;

    &:after {
      content: ":";
      padding-right: 8px;
    }

    & + div {
      width: calc(100% - 4rem);
    }
  }

}

//公共分割线样式
.divide-line {
  width: 100%;
  height: 1px;
  border-bottom: 1px dashed #376DA2;
}

//取消按钮的样式
.di-cancel-btn.ant-btn[type="button"] {
  border: 1px solid @di-border-color-default;
  border-radius: 4px;
  background-color: transparent;
  color: @di-color-text-second;

  &:hover {
    color: @di-color-text-main;
    border-color: @di-border-color-high;
  }
}


.dark-scroll {
  /*滚动条样式*/

  ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 3px;
    //display: none;
  }

  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    background: #1A426F;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: transparent;
  }
}


// 2d弹窗公共样式
.detail-modal-2d {
  background: #164587;
  overflow: hidden;
  position: relative;
}

.detail-modal-bg-2d::after {
  position: absolute;
  content: ' ';
  left: 50%;
  transform: translate(-50%);
  bottom: 0;
  width: 56px;
  height: 3px;
  border-radius: 4px 4px 0 0;
  opacity: 0.7;
  background: #BDE2FF;
  box-shadow: 0px 0px 5px 0px #7AC1FF;
}

.detail-modal-bg-2d::before {
  position: absolute;
  content: ' ';
  left: 50%;
  transform: translate(-50%) rotate(180deg);
  top: 0;
  width: 56px;
  height: 3px;
  border-radius: 4px 4px 0 0;
  opacity: 0.7;
  background: #BDE2FF;
  box-shadow: 0px 0px 5px 0px #7AC1FF;
}


// 二维图表的公共样式

.matrix-name {
  min-width: 88px !important;
  width: auto !important;
  height: 22px !important;
  color: #FFF;
  line-height: 22px;
  text-align: center;
  font-size: 12px;
  background: url(../assets/images/2dMap/marker-bg.png) no-repeat;
  background-position: center;
  background-size: cover;
  white-space: nowrap;
  padding: 0 16px;

}

.matrix-name-hover {
  min-width: 106px !important;
  width: auto !important;
  height: 40px !important;
  color: #FFF;
  line-height: 38px;
  text-align: center;
  font-size: 12px;
  background: url(../assets/images/2dMap/marker-bg-hover.png) no-repeat;
  background-position: center;
  background-size: cover;
  white-space: nowrap;
  padding: 0 16px;
  margin-top: -38px !important;

}

.matrix-name-bottom {
  margin-top: -30px !important;
}

.matrix-name-device {
  margin-top: -92px !important;
}

.matrix-name-tower {
  margin-top: -52px !important;
}

.matrix-name-device-error {
  margin-top: -96px !important;
}

.matrix-name-device-error-half {
  margin-top: -55px !important;
}

.matrix-name-camera {
  margin-top: -116px !important;
}

.matrix-name-camera-half {
  margin-top: -66px !important;
}

.matrix-name-bottom:after,
.matrix-name-device::after,
.matrix-name-tower::after,
.matrix-name-device-error::after,
.matrix-name-camera::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, 100%);
  background: url(../assets/images/2dMap/mapIcon/triangle.png) no-repeat 100% 100%;
  width: 19px;
  height: 15px;
}

.matrix-name-right, .matrix-name-left {
  margin-top: -10px !important;
}

.matrix-name-right:after {
  content: '';
  position: absolute;
  left: 100%;
  bottom: 0;
  transform: translate(10%, -10%);
  background: url(../assets/images/2dMap/mapIcon/triangle-right.png) no-repeat 100% 100%;
  width: 15px;
  height: 19px;
}

.matrix-name-left:after {
  content: '';
  position: absolute;
  right: 100%;
  bottom: 0;
  transform: translate(-10%, -10%);
  background: url(../assets/images/2dMap/mapIcon/triangle-left.png) no-repeat 100% 100%;
  width: 15px;
  height: 19px;
}

.matrix-name-hover:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, 32%);
  background: url(../assets/images/2dMap/mapIcon/triangle-hover.png) no-repeat 100% 100%;
  width: 19px;
  height: 15px;
}

.map-left-icon {
  position: absolute;
  left: 6px;
  width: 11px;
  height: 22px;
  background: url(../assets/images/2dMap/mapIcon/left-icon.png) no-repeat 100% 100%;
}

.matrix-name-hover .map-left-icon {
  left: 14px;
  top: 7px;
}

.map-right-icon {
  position: absolute;
  right: 6px;
  top: 0;
  width: 11px;
  height: 22px;
  background: url(../assets/images/2dMap/mapIcon/right-icon.png) no-repeat 100% 100%;

}

.matrix-name-hover .map-right-icon {
  right: 14px;
  top: 7px;
}

// 公共抽屉样式
.ant-drawer-content,
.ant-modal-content,
.ant-table-placeholder,
.ant-upload.ant-upload-select-picture-card {
  background: linear-gradient(180deg, #18488b 0%, #0e4085 100%);
}

.drawer-box {
  .ant-drawer-header {
    background: transparent;
    border-bottom: 1px solid @di-border-color-second;

    .ant-drawer-title, .ant-drawer-close {
      color: @di-color-text-white;
    }
  }

  .ant-drawer-body {
    padding: 0;
    background: transparent;
    height: calc(100% - 55px) !important;

    .drawer-form-com {
      background: transparent;
      width: 100%;
      height: 100%;

      .drawer-form-content {
        width: 100%;
        height: calc(100% - 55px);
        padding-bottom: 12px;
        overflow-y: auto;
        overflow-x: hidden;
        //padding-right: 28px;

        .detail_layout .left, .detail_layout .right {
          color: @di-color-text-white;
        }
      }

      .drawer-form-foot {
        background: transparent;
        border-top: 1px solid @di-border-color-second;
        text-align: center;
        padding-top: 12px;
      }
    }
  }
}

// 图片预览组件关闭样式
.viewer-close::before {
  background-image: url("../assets/images/2dMap/2d-image-preview-close.svg") !important;
  background-repeat: no-repeat;
  background-size: 40px !important;
  background-position: 0 0 !important;
  left: 5px !important;
  bottom: 5px !important;
  color: transparent;
  display: block;
  font-size: 0;
  height: 40px !important;
  line-height: 0;
  width: 40px !important;
}

.viewer-close {
  transform: scale(1.2) !important;
}

/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 6px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: #4b78b0;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: transparent;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

.solar-eye-hover-primary:hover {
  color: @primary-color !important;
  transition: .3s;
}

// vxe table公共样式
.my-table.vxe-table {
  color: @di-color-text-white;
  .vxe-table--header-wrapper {
    background-color: rgba(1, 43, 98, 0.15) !important;
    //background: linear-gradient(180deg, #2464AC 0%, #1E529C 18%, #18529C 100%) !important;
  }
  .vxe-table--body-wrapper {
    background-color: transparent !important;
  }
  .vxe-body--column {
    border-bottom: 1px solid @di-border-color-second;
    &.col--right .vxe-cell {
      padding-right: 26px;
    }
  }
  .vxe-header--column {
    border-bottom: 1px solid @di-border-color-second;
    font-weight: normal !important;

    &.col--right .vxe-cell {
      padding-right: 26px;
    }
  }
  .vxe-resizable.is--line:before {
    background-color: transparent;
  }
  .vxe-table--fixed-right-wrapper, .vxe-table--fixed-right-wrapper.scrolling--middle{
    box-shadow:  -10px 0 25px -5px rgba(134, 184, 255, 0.17);
  }
  .vxe-table--fixed-left-wrapper, .vxe-table--fixed-left-wrapper.scrolling--middle{
    box-shadow: 10px 0 25px -5px rgba(134, 184, 255, 0.17);
  }
  .fixed-left--wrapper, .fixed-right--wrapper {
    background: linear-gradient(180deg, #18488b 0%, #0e4085 100%) !important;
  }
  .vxe-body--row.row--hover {
    //background: linear-gradient(180deg, #1D5096 0%, #12448B 100%) !important;
    background: #194E96 !important;
  }
}

// 表格操作按钮样式
.operation-icon {
  font-size: 16px;
  cursor: pointer;
  margin-right: 16px;
  color: @di-color-text-main;
}

// 表格分页样式
.ant-pagination-item a {
  color: #fff;
}

.ant-pagination-item-active a {
  color: #3DABFF;
}

.div-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.align-center {
  display: flex;
  align-items: center;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.solar-eye-tooptip {
  background-color: #27599E !important;
  border-color: #27599E !important;
  box-shadow: 0px 4px 16px 0px rgba(0, 44, 105, 0.4) !important;
  color: white !important;
  border-radius: 8px !important;
  overflow: auto;

  div,
  span {
    color: white !important;
  }
}

// 设备健康-时序分析图表悬浮框样式
// 由于ai诊断要求悬浮窗超出不隐藏  所以tooltip加了appendToBody: true,  导致样式在诊断文件中不生效， 通过全局搜索  这个类只有在诊断中使用 所以放在这里
.alarm-events-tooptips {
  max-width: 800px !important;
  max-height: 600px !important;
  font-size: 12px !important;

  .time {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 600;
  }

  .station-starus {
    background: linear-gradient(135deg, #F87373 0%, #F6504D 100%);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 10px 2px 10px 2px;
    height: 20px;
    font-size: 12px;
    padding: 0 8px;
    color: #FFFFFF;
    font-weight: 600;
    line-height: 20px;
  }

  .alarm-data {
    display: flex;

    .data-content {
      background: linear-gradient(270deg, rgba(240, 112, 114, 0) 0%, rgba(240, 112, 114, 0.5) 100%) !important;
      border-radius: 2px;
      padding: 4px 8px;
      margin-bottom: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #F56C6C;
      display: flex;
      align-items: center;

      .split-line {
        width: 1px;
        height: 12px;
        background: #F7E2E2;
        margin: 0 12px;
      }
    }
  }
}

// modal弹框头部样式
.ant-modal-header {
  background: transparent;
  border-bottom: 1px solid #376DA2;
}

// modal弹框底部样式
.ant-modal-footer {
  border-top: 1px solid #376DA2;
}

// ant-design的radio文本、form文本、modal弹框关闭图标颜色
.ant-radio-wrapper, .ant-form-item-label > label, .ant-modal-close-x {
  color: #fff;
}

// ant-design的radio禁用样式
.ant-radio-disabled + span {
  color: #fff;
  opacity: 0.5;
}

// ant-design的checkbox的hover样式
.ant-checkbox:hover:not(.ant-checkbox-checked) .ant-checkbox-inner,
.ant-tree-checkbox:hover:not(.ant-tree-checkbox-checked) .ant-tree-checkbox-inner {
  border: 1px solid #BDE2FF !important;
}

// 自定义a-button次按钮禁用样式
.di-ant-btn-cncel.ant-btn[disabled], .di-ant-btn-cncel.ant-btn[disabled]:hover {
  border: 1px solid #6DB0E7;
  background: transparent;
  color: #6DB0E7;
  opacity: 0.4;
}

// 自定义a-button次按钮正常样式
.di-ant-btn-cncel.ant-btn {
  border: 1px solid #6DB0E7;
  background: transparent;
  color: #6DB0E7;
}

// 自定义a-button次按钮正常hover样式
.di-ant-btn-cncel.ant-btn:hover {
  border: 1px solid @di-border-color-high;
  color: @di-color-text-main;
}

// vxe-table的checkbox正常样式
.vxe-table .vxe-cell--checkbox .vxe-checkbox--icon:before {
  border: 1px solid @di-border-color-default !important;
  background-color: transparent !important;
}

// vxe-table的checkbox hover样式
.vxe-table .vxe-cell--checkbox .vxe-checkbox--icon:hover:before {
  border: 1px solid @di-border-color-high !important;
}

// vxe-table的checkbox 选中r样式
.vxe-table .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before,
.vxe-table .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before {
  background-color: @di-btn-bg !important;
}

// ant-radio-group的正常样式
.ant-radio-group .ant-radio-button-wrapper:not(:first-child)::before {
  background-color: transparent;
}

// ant-radio-group的hover样式
.ant-radio-group .ant-radio-button-wrapper:hover {
  border: 1px solid @di-border-color-high;
}

// 升压站名称图标样式
.boost-name {
  width: 90px !important;
  height: 39px !important;
  color: #FFF;
  line-height: 40px;
  text-align: center;
  font-size: 12px;
  background: url(../assets/images/2dMap/mapIcon/boost-bg.png) no-repeat;
  background-position: center;
  background-size: cover;
  margin-left: -45px !important;
  margin-top: -50px !important;
}

.boost-name:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, 60%);
  background: url(../assets/images/2dMap/mapIcon/triangle.png) no-repeat 100% 100%;
  width: 19px;
  height: 15px;
}

.boost-name-hover {
  background: url(../assets/images/2dMap/mapIcon/boost-bg-hover.png) no-repeat;
}

.boost-name.boost-name-hover:after {
  background: url(../assets/images/2dMap/mapIcon/triangle-hover.png) no-repeat 100% 100%;
}

.health-row-clicked {
  color: rgba(255, 255, 255, 0.5);
}
.health-row-last-clicked {
  color: #40AAFF;
}
// 时间框placeholder的颜色
::-webkit-input-placeholder {
  color: white !important;
  opacity: .5;
}

// :deep(.ant-calendar-range-picker-input) {
//   text-align: left;
//   width: 32%;
// }
.ant-modal-confirm-btns {
  .ant-btn {
    border: 1px solid #61A1D5;
    border-radius: 4px;
    background-color: transparent;
    color: #61A1D5;
  }

  .ant-btn:hover {
    color: #85CAFF;
    border-color: #85CAFF;
  }

  .ant-btn-primary {
    background: @di-btn-bg;
    border: 1px solid #85caff;
    color: rgba(0, 0, 0, 0.65);

    &:focus {
      color: rgba(0, 0, 0, 0.65);
    }

    &:hover {
      color: rgba(0, 0, 0, 0.65);
      background: @di-btn-hover-bg;
    }
  }
}

.over-flow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 下拉选择框、输入搜索框palceholder字体颜色
.ant-select-selection__placeholder, .ant-select-search__field__placeholder {
  color: rgba(255, 255, 255, 0.5);
}


.drawer-content-title {
  font-family: PingFangSC-Medium;
  font-size: 14px;
  margin-bottom: 16px;
  font-weight: normal;

  &::before {
    content: ' ';
    width: 4px;
    height: 21px;
    border-radius: 0px 2px 2px 0px;
    opacity: 1;
    background: @di-btn-bg;
    display: inline-block;
    margin-right: 16px;
    vertical-align: middle;
  }
}

// 情景演示需要背景透明
.scene-modal-custom .ant-modal-content {
  background: transparent !important;
}

// 绝对定位垂直&水平居中
.position-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

input::-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px rgba(255, 255, 255, .1) inset !important;
  box-shadow: 0 0 0px 1000px rgba(255, 255, 255, .1) inset !important;
}

input:-internal-autofill-previewed,
input:-internal-autofill-selected {
  transition: background-color 5000s ease-out 0.5s;
  -webkit-text-fill-color: white !important;
}

// 用户操作下拉样式
.system-setting-dropdown {
  min-width: fit-content !important;
  background-color: #072e6a !important;

  .setting-popup {
    width: 138px;
    box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05),
    0px 8px 10px 1px rgba(0, 0, 0, 0.06), 0px 5px 5px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #6a82a6;
    padding: 8px 6px;
    border-radius: 6px;
    background-color: #072e6a !important;

    .ant-dropdown-menu-item {
      color: #bde2ff !important;

      &:hover {
        border-radius: 4px;
        opacity: 1;
        background: #244d8c !important;
      }
    }
  }

  .nav-icon {
    font-size: 16px;
    margin-right: 8px;
    color: #bde2ff;
  }
}
//  在线监测二级菜单样式
.overlay-second-menu {
  .ant-dropdown-menu {
    width: 116px;
    padding-left: 6px;
  }
}
.flex-1 {
  flex:1;
}
.device-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00a800;
  display: inline-block;
}
