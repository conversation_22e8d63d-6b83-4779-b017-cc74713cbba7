// 虚拟滚动树重写样式


  .el-tree-common{
    .node-disabled{
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
    }
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,.el-tree-node__content:hover {
    background-color: rgba(78, 121, 177, 0.2) !important;
  }

  .el-checkbox {
    color: #606266;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    display: inline-block;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin-right: 30px;

    .el-checkbox__input {
      white-space: nowrap;
      cursor: pointer;
      outline: none;
      display: inline-block;
      line-height: 1;
      position: relative;
      vertical-align: middle;

      .el-checkbox__inner {
        display: inline-block;
        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 2px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 14px;
        height: 14px;
        background-color: #FFFFFF;
        z-index: 1;
        transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
      }
      .el-checkbox__inner::after {
        -webkit-box-sizing: content-box;
        box-sizing: content-box;
        content: "";
        border: 1px solid #FFFFFF;
        border-left: 0;
        border-top: 0;
        height: 7px;
        left: 4px;
        position: absolute;
        top: 1px;
        -webkit-transform: rotate(45deg) scaleY(0);
        transform: rotate(45deg) scaleY(0);
        width: 3px;
        -webkit-transition: -webkit-transform .15s ease-in .05s;
        transition: -webkit-transform .15s ease-in .05s;
        transition: transform .15s ease-in .05s;
        transition: transform .15s ease-in .05s, -webkit-transform .15s ease-in .05s;
        -webkit-transform-origin: center;
        transform-origin: center;
      }
      .el-checkbox__original {
        opacity: 0;
        outline: none;
        position: absolute;
        margin: 0;
        width: 0;
        height: 0;
        z-index: -1;
      }
    }

    .el-checkbox__input.is-checked {
      .el-checkbox__inner {
        background-color: #3DABFF;
        border-color: #3DABFF;
      }
      .el-checkbox__inner::after {
        -webkit-transform: rotate(45deg) scaleY(1);
        transform: rotate(45deg) scaleY(1);
      }
    }
    .el-checkbox__input.is-disabled {
      .el-checkbox__inner::after {
        cursor: not-allowed;
        border-color: #C0C4CC;
      }
      .el-checkbox__inner {
        background-color: #edf2fc;
        border-color: #DCDFE6;
        cursor: not-allowed;
      }
    }

    .el-checkbox__input.is-indeterminate{
      .el-checkbox__inner {
        background-color: #3DABFF;
        border-color: #3DABFF;
      }
      .el-checkbox__inner::before {
        content: '';
        position: absolute;
        display: block;
        background-color: #FFFFFF;
        height: 2px;
        -webkit-transform: scale(0.5);
        transform: scale(0.5);
        left: 0;
        right: 0;
        top: 5px;
      }
    }
  }

  .el-tree__empty-block{
    text-align: center;
  }
  .el-tree {
    position: relative;
    cursor: default;
    color: rgba(0,0,0,.65);
    .el-tree-node {
      white-space: nowrap;
      outline: none;
      .el-tree-node__content {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        height: 26px;
        cursor: pointer;
        label.el-checkbox {
          margin-right: 8px;
        }
        .el-tree-node__expand-icon.expanded {
          -webkit-transform: rotate(90deg);
        }

        .tree-node-loading{
          width: 24px;
          height: 24px;
          padding-top:6px;
          font-size: 12px;
          color:@primary-color;
        }
        .el-tree-node__expand-icon {
          cursor: pointer;
          font-size: 12px;
          transform: rotate(0deg);
          transform-origin: 50% 50%;
          width: 24px;
          height: 24px;
          position: relative;
          -webkit-transition: -webkit-transform 0.3s ease-in-out;
          transition: -webkit-transform 0.3s ease-in-out;
          transition: transform 0.3s ease-in-out;
          transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
        }

        .is-leaf.el-tree-node__expand-icon::after {
          color: transparent;
          cursor: default;
          border:none;
          content: none;
        }
        .el-tree-node__expand-icon::after{
          content: ' ';
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translateY(-50%);
          width: 0;
          height: 0;
          border: 4px solid;
          border-left-color: rgba(255, 255, 255, 0.6);
          border-bottom-color: transparent;
          border-right-color: transparent;
          border-top-color: transparent;
        }
      }
    }
    .el-tree-node.is-current{
      .custom-tree-node{
        background-color: #ffeed6;
      }
    }

  }


