<template>
  <div
    v-show="node.visible"
    ref="node"
    class="el-tree-node"
    :class="{
      'is-expanded': expanded,
      'is-current': node.isCurrent,
      'is-hidden': !node.visible,
      'is-focusable': !node.disabled,
      'is-checked': !node.disabled && node.checked
    }"
    role="treeitem"
    tabindex="-1"
    :aria-expanded="expanded"
    :aria-disabled="node.disabled"
    :aria-checked="node.checked"
    :draggable="tree.draggable"
    @click.stop="handleClick"
    @contextmenu="$event => this.handleContextMenu($event)"
    @dragstart.stop="handleDragStart"
    @dragover.stop="handleDragOver"
    @dragend.stop="handleDragEnd"
    @drop.stop="handleDrop"
  >
    <div class="el-tree-node__content">
      <span
        aria-hidden="true"
        :style="{
          'min-width': (node.level - 1) * tree.indent + 'px'
        }"
      ></span>
      <a-icon type="loading" class="tree-node-loading" v-show="node.loading" />
      <span
        v-show="!node.loading"
        :class="[
          {
            'is-leaf': node.isLeaf,
            expanded: !node.isLeaf && expanded
          },
          'el-tree-node__expand-icon',
          'el-tree-node__expand-icon-no-transition',
          tree.iconClass ? tree.iconClass : 'el-icon-caret-right'
        ]"
        @click.stop="handleExpandIconClick"
      ></span>
      <el-checkbox
        v-if="showCheckbox || node.checkable"
        v-model="node.checked"
        :indeterminate="node.indeterminate"
        :disabled="!!node.disabled"
        @click.native.stop
        @change="handleCheckChange"
      ></el-checkbox>
      <node-content :node="node"></node-content>
    </div>
  </div>
</template>

<script type="text/jsx">
import ElCheckbox from './packages/checkbox';
import emitter from './mixins/emitter';
import commonMethods from './mixins/common-methods';

export default {
  name: 'ElTreeVirtualNode',
  componentName: 'ElTreeVirtualNode',

  components: {
    ElCheckbox,
    NodeContent: {
      props: {
        node: {
          required: true
        }
      },
      render (h) {
        const parent = this.$parent;
        const tree = parent.tree;
        const node = this.node;
        const { data, store } = node;
        return parent.renderContent ? (
          parent.renderContent.call(parent._renderProxy, h, {
            _self: tree.$vnode.context,
            node,
            data,
            store
          })
        ) : tree.$scopedSlots.default ? (
          tree.$scopedSlots.default({ node, data })
        ) : (
          <span class="el-tree-node__label">{node.label}</span>
        );
      }
    }
  },

  mixins: [emitter, commonMethods],

  props: {
    node: {
      default () {
        return {};
      }
    },
    renderContent: Function,
    showCheckbox: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      tree: null,
      expanded: false,
      childNodeRendered: false,
      oldChecked: null,
      oldIndeterminate: null
    };
  },

  watch: {
    'node.indeterminate' (val) {
      this.handleSelectChange(this.node.checked, val);
    },

    'node.checked' (val) {
      this.handleSelectChange(val, this.node.indeterminate);
    },

    'node.expanded' (val) {
      this.$nextTick(() => (this.expanded = val));
      if (val) {
        this.childNodeRendered = true;
      }
    }
  },
  methods: {},

  created () {
    this.init(this.$parent.$parent);
    this.$set(this.node, 'checkable', this.node.data.checkable || false);
  }
};
</script>
