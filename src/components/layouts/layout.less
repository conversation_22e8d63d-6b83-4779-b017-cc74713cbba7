.global-layout {
  width: 100vw;
  height: 100vh;

  .global-header {
    width: 100%;
    background-image: url("../../assets/images/head-bg.svg");
    background-repeat: no-repeat;
    background-size: cover;
    height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999999;
    overflow: hidden;
    transform: scale(1, 1);
    -ms-transform: scale(1, 1); /* IE 9 */
    -webkit-transform: scale(1, 1); /* Safari and Chrome */
    transform-origin: left top; /*设置左上角为缩放原点*/

    .head-top {
      position: absolute;
      top: 3px;
      left: 50%;
      transform: translateX(-50%);
      background-image: url("../../assets/images/head-top.svg");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 50%;
      height: 8px;
    }

    .head-left {
      background-image: url("../../assets/images/head-left.png");
      background-repeat: no-repeat;
      background-size: cover;
      min-width: 1544px;
      display: flex;
      align-items: center;
      padding-left: 480px;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      height: 100%;
      pointer-events: none;

      .logo {
        position: absolute;
        left: 4.6%;
        font-size: 24px;
        color: white;
        font-family: cursive;
        font-weight: bold;
        letter-spacing: 4px;
        .logo-icon {
          font-size: 285px;
        }
      }

      .first-menu {
        margin-bottom: 8px;
        pointer-events: auto;
        .first-menu-item {
          background-image: url("../../assets/images/first-menu-bg.svg");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          cursor: pointer;
          color: #61a1d5;
          padding: 12px 36px;
          font-size: 14px;
          font-weight: 600;
          line-height: normal;

          &.router-link-active {
            background-image: url("../../assets/images/first-menu-active-bg.svg");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            color: #fff;
            position: relative;

            &:after {
              content: url("../../assets/images/menu-active.svg");
              position: absolute;
              bottom: 0px;
              left: 32px;
              background-size: 100% 100%;
            }
          }

          &:hover:not(.router-link-active) {
            background-image: url("../../assets/images/first-menu-hover-bg.svg");
            color: #bde2ff;
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }
        }
      }
    }

    .head-right {
      background-image: url("../../assets/images/head-right.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      min-width: 1024px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      position: absolute;
      right: 0;
      top: 8px;
      color: #bde2ff;
      height: 56px;
      padding-bottom: 10px;

      .time {
        font-size: 18px;
        font-weight: 600;
      }

      .date-week {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        justify-content: center;
      }

      .setting-icon {
        font-size: 20px;
        cursor: pointer;
        color: @di-color-text-main;
      }

      .setting-icon:hover {
        color: @di-color-text-highlight;
      }

      .system-setting:hover {
        .down-arrow {
          transform: rotate(180deg);
          transition: 0.5s;
        }
      }

      .full-screen,
      .exit-full-screen {
        // font-size: 28px;
        width: 28px;
        height: 28px;
        background-image: url("../../assets/images/full-screen.png");
        background-size: cover;
      }

      .full-screen:hover {
        background-image: url("../../assets/images/full-screen-hover.png");
      }

      .exit-full-screen {
        background-image: url("../../assets/images/exit-full-screen.png");
      }

      .exit-full-screen:hover {
        background-image: url("../../assets/images/exit-full-screen-hover.png");
      }
    }
  }

  .global-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 9999;

    .bottom-line-bg-left {
      background: url("../../assets/images/2dMap/2d-bottom-bg-left.svg");
      background-size: contain;
      background-repeat: no-repeat;
      position: absolute;
      width: 958px;
      height: 20px;
      left: 0;
      bottom: 0;
      z-index: 2;
    }

    .bottom-line-bg-right {
      background: url("../../assets/images/2dMap/2d-bottom-bg-right.png");
      background-size: contain;
      background-repeat: no-repeat;
      position: absolute;
      width: 736px;
      height: 20px;
      right: 0;
      bottom: 0;
      z-index: 2;
    }

    .bottom-line-bg-center {
      background: url("../../assets/images/2dMap/2d-bottom-bg-center.svg");
      background-size: contain;
      background-repeat: no-repeat;
      position: absolute;
      width: 968px;
      height: 38px;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0;
      z-index: 2;
    }
  }

  .person-img {
    width: 28px;
    height: 28px;
  }
}

.modal-content {
  margin: 48px auto;
  color: white;
  text-align: center;
}

.footer {
  padding: 16px;
  border-top: 1px solid #376da2;
  text-align: center;

  .ant-btn + .ant-btn {
    margin-left: 16px;
  }
}

.modal-title {
  margin: 4px 2px 0;
}

