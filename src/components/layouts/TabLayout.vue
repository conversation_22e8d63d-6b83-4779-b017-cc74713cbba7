<template>
  <GlobalLayout :path="rootPath" :secondRouters="secondRouters">
    <div
      class="second-menu"
      v-if="secondRouters.length && !$route.meta.customHeader"
    >
      <router-link
        :to="{ path: item.path }"
        v-for="item in secondRouters"
        :key="item.name"
        class="second-menu-item"
      >
        <svg-icon
          v-if="item.svgIcon || item.meta.icon"
          :icon-class="item.svgIcon || item.meta.icon"
          class="second-menu-icon"
        />
        <span v-else>{{ item.meta.title }}</span>
      </router-link>
    </div>
    <div
      :class="{
        'main-parent': !$route.meta.customHeader,
        'main-parent-custom': $route.meta.customHeader,
      }"
    >
      <keep-alive :max="5" v-if="$route.meta.keepAlive">
        <router-view />
      </keep-alive>
      <router-view v-else />
    </div>
  </GlobalLayout>
</template>

<script>
import GlobalLayout from "@/components/layouts/GlobalLayout";
import { setWaterMark, removeWatermark } from "@/utils/addWaterMarker";
import { routeChange } from "@/utils/util";

export default {
  name: "TabLayout",
  components: { GlobalLayout },
  data() {
    return {
      secondRouters: [],
      rootName: "",
      rootPath: "",
      oldPath: "",
      activePath: "",
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.user.info;
    },
    addRouters() {
      return this.$store.getters.addRouters;
    },
  },
  mounted() {
    let userInfo = this.userInfo;
    if (userInfo && userInfo.realname) {
      let telp = userInfo.phone ? userInfo.phone.substr(-4) : "";
      setWaterMark("", userInfo.realname + " " + telp);
    } else {
      setWaterMark("");
    }
    // 监听屏幕缩放，自适应
    $(window).resize(this.resizeFun);
  },
  provide() {
    return {
      closeCurrent: this.closeCurrent,
      rootPath: () => this.rootPath,
    };
  },
  watch: {
    $route: {
      handler(newRoute, old) {
        routeChange(this, newRoute, old, this.resizeFun);
        let projectTitle = "iSolarDi";
        // internalOrExternal：是否外部打开
        if (!newRoute.meta.internalOrExternal) {
          document.title = newRoute.meta.title + " · " + projectTitle;
        }
      },
      immediate: true,
    },
  },
  methods: {
    /* for: 关闭当前tab页，供子页面调用->望菜单能配置外链，直接弹出新页面而不是嵌入iframe #428 */
    closeCurrent() {
      this.remove(this.oldPath);
    },
    remove() {
      setTimeout(() => {
        this.$router.push({ path: this.oldPath });
      }, 50);
    },
    resizeFun() {
      const h = $(window).height();
      const w = $(window).width();
      const heightScale = h / (1080 - 64);
      // 只有运行分析不需要显示二级菜单下面的分割线
      const noDividePaths = ["/operations", "/health/safe"];
      const noDivideLine = noDividePaths.some(
        (o) => this.rootPath.indexOf(o) > -1
      );
      let top1 = 64 * heightScale - (64 * (1 - heightScale)) / 2 + "px";
      let width = (w - 64) / heightScale + "px";
      let marginLeft = -((w - 64) / heightScale - w) / 2 + "px";
      if (
        this.secondRouters.length &&
        !(this.rootPath.indexOf("/dashboard") > -1)
      ) {
        $(".second-menu").css({
          transform: `scale(${heightScale},${heightScale})`,
          "-ms-transform": `scale(${heightScale},${heightScale})`,
          "-webkit-transform": `scale(${heightScale},${heightScale})`,
          top: top1,
          width: width,
          marginLeft: marginLeft,
          right: "auto",
          borderBottom: noDivideLine ? "none" : "1px dashed #376da2",
        });
      }
    },
  },
  destroyed() {
    removeWatermark();
  },
};
</script>

<style lang="less" scoped>
.second-menu {
  position: absolute;
  top: 64px;
  display: flex;
  margin-left: 32px;
  align-items: center;
  padding: 16px 0;

  width: 1856px;
  z-index: 1;

  .second-menu-item {
    width: 98px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    border: 1px solid #85caff;
    align-items: center;
    justify-content: center;
    color: #85caff;
    font-size: 20px;
    padding: 4px 8px;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
    }

    &.router-link-active {
      color: #fff;
      background: #0077d1;
    }

    &:not(:last-child) {
      margin-right: 16px;
    }

    .second-menu-icon {
      width: 100%;
      height: 100%;
    }
  }
}

.main-parent,
.main-parent-custom {
  background-color: #012b62;
}
</style>
