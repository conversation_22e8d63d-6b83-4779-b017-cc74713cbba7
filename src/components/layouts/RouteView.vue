<template>
  <div class="main width-height-100" :class="styleObj">
    <router-view />
  </div>
</template>

<script>
const dashboardSpecialRoutes = [
  { path: "/dashboard/monitor", height: 64 },
  { path: "/dashboard/insightTool", height: 64 },
];
const notDashboardSpecialRoutes = [
  { path: "/ecological", height: 64 },
  { path: "/integratedSafety", height: 64 },
  { path: "/operations", height: 110 },
  { path: "/health/safe", height: 115 },
];
export default {
  name: "RouteView",
  data() {
    return {
      styleObj: "",
      rootName: "",
    };
  },
  watch: {
    $route: {
      handler(newRoute) {
        this.getStyleObjFromPath(newRoute);
        this.rootName = newRoute.meta.title;
        this.$nextTick(() => {
          this.resizeFun();
        });
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    // 监听屏幕缩放，自适应
    $(window).resize(this.resizeFun);
  },
  methods: {
    findItemPathForHeight(pathList) {
      const findItem = pathList.find(
        (item) => this.$route.path.indexOf(item.path) > -1
      );
      return findItem?.height ?? 0;
    },
    resizeFun() {
      let height;
      if (
        this.$route.matched?.length > 2 &&
        !(this.$route.path.indexOf("/dashboard") > -1)
      ) {
        height = this.findItemPathForHeight(notDashboardSpecialRoutes) || 134;
      } else {
        // 处理在dashboard下的特殊路由集合
        height = this.findItemPathForHeight(dashboardSpecialRoutes);
      }
      const heightScale = $(window).height() / (1080 - 64);
      let marginTop = height * heightScale + "px";
      const element = $(".main-parent-custom>.main")?.length
        ? $(".main-parent-custom>.main")
        : $(".main-parent>.main");
      element.css({
        "margin-top": marginTop,
        height: "calc(100% - " + height * heightScale + "px)",
      });
    },
    getStyleObjFromPath(to) {
      if (to.path.indexOf("/dashboard") > -1) {
        if (dashboardSpecialRoutes.find((item) => item.path === to.path)) {
          this.styleObj = "main-dashboard-special";
        } else {
          this.styleObj = "main-dashboard";
        }
      } else if (to.path.indexOf("user/login") > -1) {
        this.styleObj = "main-user";
      } else {
        this.styleObj = "";
      }
    },
  },
};
</script>

<style lang="less" scoped>
.main-parent > .main,
.main-parent-custom > .main-dashboard-special {
  padding: 16px 32px 38px;
  overflow: hidden auto;
}

.main-parent > .main-user {
  padding: 0 !important;
}

.main-parent-custom > .main-dashboard {
  margin-top: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}
</style>
