<template>
  <a-spin :spinning="loading" size="large">
    <iframe
      title="iframePageContent"
      :id="id"
      :src="url"
      ref="testPage"
      frameborder="0"
      width="100%"
      :height="frameHeight"
      @load="sendBaseInfo()"
    ></iframe>
  </a-spin>
</template>

<script>
import Vue from "vue";
import {
  ACCESS_TOKEN,
  USER_INFO,
  DEFAULT_COLOR,
  HAS_ALL_DATA_HEALTH,
  PSA_INFO,
  TENANT_ID,
} from "@/store/mutation-types";
import { mixin, mixinDevice } from "@/utils/mixin.js";
import { hikvisionToken } from "@/api/common.js";
export default {
  name: "IframePageContent",
  inject: ["closeCurrent"],
  props: {
    path: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      url: "",
      id: "",
      loading: true,
      iframeWin: {},
      frameHeight: "",
      count: 0,
    };
  },
  mixins: [mixin, mixinDevice],
  created() {
    this.goUrl();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.getFrameHeight);
  },
  deactivated() {
    window.removeEventListener("resize", this.getFrameHeight);
  },
  mounted() {
    let that = this;
    this.iframeWin = this.$refs.testPage.contentWindow;
    const { testPage } = this.$refs;
    if (testPage.attachEvent) {
      // IE
      testPage.attachEvent("onload", () => {
        that.iframeWin = that.$refs.testPage.contentWindow;
        that.stateChange();
      });
    } else {
      // 非IE
      testPage.onload = () => {
        that.iframeWin = that.$refs.testPage.contentWindow;
        that.stateChange();
      };
    }
    this.getFrameHeight();
    window.addEventListener("resize", this.getFrameHeight);
    window.addEventListener("message", function (event) {
      that.loading = false;
      var data = event.data;
      // console.log(data);
      if (data) {
        switch (data.cmd) {
          case "menu":
            break;
          case "logout":
            that.$store.dispatch("Logout").then(() => {
              this.$ls.remove(ACCESS_TOKEN);
              window.location.reload();
            });
            break;
        }
      }
    });
  },
  watch: {
    $route() {
      if (!this.path) {
        if (this.count == 0) {
          this.loading = true;
        }
        this.count++;
        this.goUrl();
      }
    },
    primaryColor() {
      this.$nextTick(() => {
        this.sendBaseInfo();
      });
    },
    path() {
      this.loading = true;
      if (this.count == 0) {
        this.loading = true;
      }
      this.count++;
      this.$nextTick(() => {
        this.sendBaseInfo();
        this.goUrl();
      });
    },
  },
  computed: {
    multipage() {
      return this.$store.state.app.multipage;
    },
  },
  methods: {
    getFrameHeight() {
      let cardHeight = window.innerHeight - 172;
      this.frameHeight = `${cardHeight}px`;
    },
    sendBaseInfo() {
      if (this.iframeWin && typeof this.iframeWin.postMessage === "function") {
        this.iframeWin.postMessage(
          {
            cmd: "baseInfo",
            params: {
              token: Vue.ls.get(ACCESS_TOKEN),
              userInfo: Vue.ls.get(USER_INFO),
              device: this.$store.getters.device,
              color: this.primaryColor,
              hasAllData: Vue.ls.get(HAS_ALL_DATA_HEALTH),
              deviceId: localStorage.getItem("deviceId"),
            },
          },
          "*"
        );
      }
      // this.loading = false;
    },
    stateChange() {
      this.loading = false;
    },
    getTitle(title, flag) {
      let storageKey = "";
      JSON.parse(sessionStorage.getItem("menuList")).map((item) => {
        if (item.title == title) {
          storageKey = flag
            ? item.parent + "/" + item.path
            : "route:title:" + item.path;
        }
      });
      return storageKey;
    },
    haikangToken() {
      return hikvisionToken({
        userId: Vue.ls.get(USER_INFO).id,
        userAccount: "admin",
      }).catch((err) => {
        console.error("Failed to get Haikang token:", err);
        throw err;
      });
    },
    async goUrl() {
      let url = "";
      let internalOrExternal = "";
      const psaInfo = this.$ls.get(PSA_INFO);
      const deviceId = localStorage.getItem("deviceId");
      const tenantId = this.$ls.get(TENANT_ID);
      if (!this.path) {
        url = this.$route.meta.url;
        this.id = this.$route.path;
        internalOrExternal = this.$route.meta.internalOrExternal;
      } else {
        url = this.path.meta.url;
        this.id = this.path.path;
        internalOrExternal = this.path.meta.internalOrExternal;
      }
      if (url !== null && url !== undefined) {
        const hasQuestionMark = url.indexOf("?") > -1;
        if (internalOrExternal != undefined && internalOrExternal == false) {
          if (url.indexOf("isolareye.com") > -1) {
            this.url =
              url +
              (hasQuestionMark ? "&" : "?") +
              "token=" +
              Vue.ls.get(ACCESS_TOKEN) +
              "&primaryColor=" +
              Vue.ls.get(DEFAULT_COLOR) +
              `&psaInfo=${encodeURIComponent(JSON.stringify(psaInfo))}`;
          } else {
            let hkToken = await this.haikangToken();
            this.url = url + "&token=" + hkToken.result_data;
          }
        }
        if (this.count >= 1) {
          this.count = 0;
          this.loading = false;
        }
        /*判断打开方式，新窗口打开时this.$route.meta.internalOrExternal==true */
        if (internalOrExternal != undefined && internalOrExternal == true) {
          if (!this.path) {
            this.closeCurrent();
          } else {
            this.$emit("close");
          }
          let hkToken = await this.haikangToken();
          // 外部url加入token
          // eslint-disable-next-line no-template-curly-in-string
          let tokenStr = "${token}";
          if (url.indexOf(tokenStr) != -1) {
            let token = Vue.ls.get(ACCESS_TOKEN);
            this.url = url.replace(tokenStr, token);
          }

          window.open(
            this.url ? this.url : url + "&token=" + hkToken.result_data,
            "_blank"
          );
        }
      }
    },
  },
};
</script>

<style>
.iframe {
  position: relative;
  overflow: hidden;
  padding-top: 43.2%;
  height: calc(100% - 64px);
}
.iframe-container {
  width: 100%;
  height: calc(100% - 64px);
  border: 0;
}
</style>
