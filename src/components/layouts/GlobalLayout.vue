<template>
  <div class="global-layout">
    <div class="global-header">
      <div class="head-top"></div>

      <div class="head-left">
        <div class="logo">
          <svg-icon icon-class="logo" class="logo-icon" />
        </div>

        <div class="first-menu">
          <a :href="'#' + item.path" v-for="item in firstLevelRoutes" :key="item.path" class="first-menu-item" :class="{ 'router-link-active': path.indexOf(item.path) > -1 }">
            {{ item.meta.title }}
          </a>
        </div>
      </div>
      <div class="head-right">
        <div class="extra-operations flex-center">
          <div v-if="showAlarmByComputed" class="flex-start flex-column cursor-pointer margin-r-32" v-has="'overview:alarmInfo'" @click="alarmIconClickHandler">
            <div class="alarm-icon">
              <div class="alarm-num num-font-700 flex-center">
                <span class="color-text-white">
                  {{ alarmTotalCount }}
                </span>
              </div>
              <img src="@/assets/images/2dMap/2d-alarm-message.gif" alt="2d-alarm-message" class="width-height-100" />
            </div>
          </div>
          <!-- <div class="second-menu-custom margin-l-12 margin-r-24">
            <a-dropdown
              v-if="secondRouters.length && $route.meta.customHeader"
              class="second-menu-item-custom"
              overlayClassName="overlay-second-menu"
            >
              <div>
                <span>{{ $route.meta.title }}</span>
                <svg-icon
                  class="down-arrow margin-l-4"
                  icon-class="down-arrow"
                />
              </div>

              <a-menu slot="overlay">
                <a-menu-item :key="item.name" v-for="item in secondRouters">
                  <router-link :to="{ path: item.path }">
                    {{ item.meta.title }}
                    <svg-icon
                      v-if="$route.path == item.path"
                      class="down-arrow margin-l-4"
                      icon-class="completed"
                    />
                  </router-link>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div> -->
        </div>

        <div class="time margin-r-24">{{ timeInfo.nowTime }}</div>
        <div class="date-week margin-r-24">
          <span>{{ timeInfo.nowDate }}</span>
          <span>{{ timeInfo.week }}</span>
        </div>

        <!-- 用户操作 -->
        <a-dropdown class="system-setting" overlayClassName="system-setting-dropdown">
          <span class="margin-r-24">
            <img class="person-img" src="@/assets/images/public/personal.png" alt="personal" />
            <span style="padding-left: 16px" class="nick-name">{{ userInfo.realname }}</span>
            <svg-icon class="down-arrow margin-l-4" icon-class="down-arrow" />
          </span>
          <a-menu slot="overlay" class="setting-popup">
            <!--            <a-menu-item key="4" @click="updatePassword">-->
            <!--              <img-->
            <!--                src="@/assets/images/public/lock.png"-->
            <!--                class="nav-icon"-->
            <!--                alt="modify-pass"-->
            <!--              />-->
            <!--              <span>密码修改</span>-->
            <!--            </a-menu-item>-->
            <a-menu-item key="5" @click="openTab" v-if="hasStemConfigurationPermission">
              <svg-icon iconClass="setting" class="nav-icon"></svg-icon>
              <span>系统配置</span>
            </a-menu-item>
            <a-menu-item key="6" @click="visible = true">
              <svg-icon iconClass="logout" class="nav-icon"></svg-icon>
              <span>退出登录</span>
            </a-menu-item>
          </a-menu>
        </a-dropdown>
        <div v-if="isFullScreen" class="margin-r-24 exit-full-screen cursor-pointer" @click="exitFull"></div>
        <div v-else class="margin-r-24 full-screen cursor-pointer" @click="full"></div>
      </div>
    </div>
    <user-password ref="userPassword"></user-password>
    <div class="global-bg pointer-events-none">
      <div class="bottom-line-bg-left"></div>
      <div class="bottom-line-bg-right"></div>
      <div class="bottom-line-bg-center"></div>
    </div>
    <a-modal title="" width="410px" :closable="false" :maskClosable="false" centered :visible="visible" @cancel="visible = false" :footer="null" :bodyStyle="{ padding: 0 }">
      <div class="height-100 width-100 detail-modal-2d detail-modal-bg-2d">
        <modal-title title="提示" class="modal-title">
          <div slot="right" class="flex-end">
            <svg-icon icon-class="close" class="font-12 margin-r-8 cursor-pointer color-text-white" @click="visible = false" />
          </div>
        </modal-title>
        <div class="modal-content">确定退出当前账号吗？</div>
        <div class="footer">
          <template>
            <di-throttle-button @click="visible = false" class="di-cancel-btn" label="取消"></di-throttle-button>
            <di-throttle-button @click="handleLogout" :loading="nextLoading" label="确定"></di-throttle-button>
          </template>
        </div>
      </div>
    </a-modal>
    <slot></slot>
    <audio ref="alertAudioEl" :autoplay="false" muted preload :src="alertAudio" type="hidden" />
    <a-button ref="playBtn" @click="startPlay" hidden="hidden"></a-button>
  </div>
</template>

<script>
import Vue from 'vue';
import moment from 'moment';
import { mapActions } from 'vuex';
import { ACCESS_TOKEN, PSA_INFO } from '@/store/mutation-types';
import { getWeatherByTimeRange } from '@/api/health/AlarmEvents';
import { EventBus } from '../../views/2dMap/modules/eventBus';
import { getWeatherEffectSwitch, updateWeatherEffectSwitch, getAlarmHint } from '../../api/2dMap/psOverview';
import { USER_INFO } from '../../store/mutation-types';
import UserPassword from '../tools/UserPassword';
import findTree from 'xe-utils/findTree';
import LinkJump from '@/mixins/LinkJump';
import PropTypes from 'ant-design-vue/es/_util/vue-types';
import { permissionRouterGo } from '@/utils';
import { mapGetters, mapState } from 'vuex';

const I_SOLAR_HEALTH_DOCTOR = 'iSolarDoctor';
const I_SOLAR_HEALTH_BASIC = 'iSolarHealth';

export default {
  name: 'GlobalLayout',
  components: { UserPassword },
  mixins: [LinkJump],
  props: {
    secondRouters: PropTypes.array,
    path: PropTypes.string
  },
  data() {
    return {
      timeInfo: {
        nowDate: moment().format('YYYY-MM-DD'),
        nowTime: moment().format('HH:mm:ss'),
        week: moment().format('dddd')
      },
      isWeatherEffect: false,
      isFullScreen: false,
      userInfo: Vue.ls.get(USER_INFO),
      visible: false,
      nextLoading: false,
      alertAudio: require('@/assets/images/health/alarm.mp3'),
      alarmInterval: null,
      modalInstance: null
    };
  },
  created() {
    const timer = setInterval(() => {
      this.timeInfo.nowDate = moment().format('YYYY-MM-DD');
      this.timeInfo.nowTime = moment().format('HH:mm:ss');
      this.timeInfo.week = moment().format('dddd');
    }, 1000);
    // 五分钟轮询一次告警信息, 有新告警
    this.getAlarmInfo();
    this.alarmInterval = setInterval(this.getAlarmInfo, (this.systemConfigMap?.configDataFrequency?.setValue || 5) * 60 * 1000);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(timer);
      clearInterval(this.alarmInterval);
      this.alarmInterval = null;
    });
  },
  mounted() {
    document.addEventListener('fullscreenchange', () => {
      if (this.getFullscreenElement() == null) {
        this.isFullScreen = false;
      }
    });
    this.resizeFun();
    // 监听屏幕缩放，自适应
    $(window).resize(this.resizeFun);
    // 获取天气是否打开
    this.getWeatherEffect();
    // 在页面加载完成后执行
    if (this.hasStemConfigurationPermission) {
      if ('requestIdleCallback' in window) {
        // 使用 requestIdleCallback 在浏览器空闲时加载 iframe
        requestIdleCallback(this.loadIframe, { timeout: 5000 }); // 设置超时时间为 5 秒
      } else {
        // 如果不支持 requestIdleCallback，延迟 5 秒加载 iframe
        setTimeout(this.loadIframe, 5000);
      }
    }
  },
  computed: {
    token() {
      return this.$ls.get(ACCESS_TOKEN);
    },
    firstLevelRoutes() {
      const allRouters = this.$store.getters.addRouters;
      return (allRouters?.[0]?.children ?? []).filter((item) => !item.hidden);
    },
    addRouters() {
      return this.$store.getters.addRouters;
    },
    // 判断是否有系统配置权限，如果有则显示入口，反之隐藏
    hasStemConfigurationPermission() {
      const permissions = JSON.parse(sessionStorage.getItem('ALL_ORIGINAL_PERMISSION_MENU') || '[]');
      // 主应用是否配置“系统配置”路由
      const hasSystemConfigRoute = findTree([permissions.find((item) => item.name === I_SOLAR_HEALTH_DOCTOR)], (item) => item.path === '/system/config');
      const iSolarHealthBasicPerms = permissions.find((item) => item.name === I_SOLAR_HEALTH_BASIC);
      const systemConfigPermsObj = iSolarHealthBasicPerms?.children.find((item) => item.name === 'systemConfiguration');
      const count = this.loopNotHiddenPerms(systemConfigPermsObj?.children);
      return !!hasSystemConfigRoute && !systemConfigPermsObj?.hidden && count > 1;
    },
    showAlarmByComputed() {
      const notShowPaths = ['/dashboard/3d', '/dashboard/diagram'];
      const notShow = notShowPaths.some((item) => {
        return this.path.indexOf(item) > -1;
      });
      return !notShow;
    },
    ...mapGetters(['systemConfigMap']),
    ...mapState({
      alarmTotalCount: (state) => state.app.alarmTotalCount
    })
  },
  methods: {
    ...mapActions(['Logout']),
    loopNotHiddenPerms(children = []) {
      let count = 0;
      if (children && children.length > 0) {
        for (let i = 0; i < children.length; i++) {
          if (!children[i].hidden) {
            count++;
          }
          // alwaysShow: 是否聚合路由
          count += this.loopNotHiddenPerms(children[i]?.alwaysShow ? [] : children[i].children);
        }
      }
      return count;
    },
    //   查询天气
    async getTodayWeather() {
      const { nowDate } = this.timeInfo;
      const { psId } = this.$ls.get(PSA_INFO);
      const res = await getWeatherByTimeRange({
        psId,
        startDate: nowDate,
        endDate: nowDate
      });
      const weatherInfo = res.result_data.weather[0] || {};
      weatherInfo.iconDir = this.mapWeatherIconById(weatherInfo.conditionIdDay);
      this.weatherInfo = weatherInfo;
    },
    async getWeatherEffect() {
      let res = await getWeatherEffectSwitch({
        useraccount: this.userInfo.username || ''
      });
      this.isWeatherEffect = res.result_data == 1;
    },
    async updateWeatherEffect() {},
    // 根据conditionIdDay获取天气图标
    mapWeatherIconById(conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    },
    async changeWeatherSwitch(value) {
      await updateWeatherEffectSwitch({
        useraccount: this.userInfo.username || '',
        switchValue: value ? 1 : 0
      });
      this.isWeatherEffect = value;
      // 更新2d的效果
      EventBus.$emit('messageSent', value); // 发送事件
      // 更新3d的小姑婆
      window.g.event.fire('change_weather', value);
    },
    // 系统全屏
    full() {
      this.isFullScreen = true;
      const element = document.documentElement;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    // 退出全屏
    exitFull() {
      this.isFullScreen = false;
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      }
    },
    // 获取全屏状态
    getFullscreenElement() {
      return document['fullscreenElement'] || document['mozFullScreenElement'] || document['msFullScreenElement'] || document['webkitFullscreenElement'] || null;
    },
    resizeFun() {
      // var w = $(window).width();
      const h = $(window).height();
      // var widthScale = w / 1920; // 根据页面设计，动态修改宽高
      const heightScale = h / (1080 - 64);
      $('.global-header').css({
        transform: `scale(${1},${heightScale})`,
        '-ms-transform': `scale(${1},${heightScale})`,
        '-webkit-transform': `scale(${1},${heightScale})`
      });
      let right = (1024 * (heightScale - 1)) / 2 + 'px';
      $('.head-right').css({
        transform: `scale(${heightScale},${1})`,
        '-ms-transform': `scale(${heightScale},${1})`,
        '-webkit-transform': `scale(${heightScale},${1})`,
        right: right
      });
      let left = (1524 * (heightScale - 1)) / 2 + 'px';
      $('.head-left').css({
        transform: `scale(${heightScale},${1})`,
        '-ms-transform': `scale(${heightScale},${1})`,
        '-webkit-transform': `scale(${heightScale},${1})`,
        left: left
      });
    },
    // 修改密码
    updatePassword() {
      let username = this.userInfo.username;
      this.$refs.userPassword.show(username);
    },
    // 退出
    handleLogout() {
      this.nextLoading = true;
      this.Logout({})
        .then(() => {
          this.nextLoading = false;
          this.$router.push({
            path: '/user/login'
          });
          document.title = 'iSolarHealth数智全科登录页';
          let logingUserInfo = localStorage.getItem('logingUserInfo');
          let deviceId = localStorage.getItem('deviceId');
          // 清除浏览器localStorage、sessionStorage
          window.localStorage.clear();
          window.sessionStorage.clear();
          window.localStorage.setItem('logingUserInfo', logingUserInfo);
          window.localStorage.setItem('deviceId', deviceId);
        })
        .catch((err) => {
          this.$message.error({
            title: '错误',
            description: err.message
          });
        });
    },
    openTab() {
      // window.open(window.env.settingUrl);
      this.$router.push({
        name: '/system/config',
        query: { timestamp: parseInt(Date.now() / 1000) } // 时间戳
      });
    },
    // 查询实时监测是否有新的告警
    getAlarmInfo() {
      let dataRolesArr = this.userInfo.dataRoles || [];
      const { psId } = this.$ls.get(PSA_INFO);
      getAlarmHint({ dataRoles: dataRolesArr.join(','), psId }).then((res) => {
        if (res.result_data) {
          this.$refs.playBtn.$el.click();
          if (!this.modalInstance) {
            this.modalInstance = this.$confirm({
              title: '告警提示',
              iconType: 'exclamation-circle',
              centered: true,
              content: (h) => <span>您有新的告警信息未查看，请及时查看！</span>,
              okText: '去查看',
              cancelText: '忽略',
              onOk: () => {
                this.modalInstance = null;
                permissionRouterGo('/health/environment', this);
              },
              onCancel: () => {
                this.modalInstance = null;
              }
            });
          }
        }
      });
      this.$store.dispatch('app/GetGlobalRealTimeAlarmCount', {
        psId,
        needLuminousPower: 1,
        modelName: 'alertList'
      });
    },
    // 有新告警，播放告警提示音
    startPlay() {
      this.$refs.alertAudioEl.muted = false;
      this.$refs.alertAudioEl.currentTime = 0;
      this.$refs.alertAudioEl && this.$refs.alertAudioEl.play();
    },
    // 动态加载 iframe
    loadIframe() {
      const iframeContainer = document.getElementById('iframe-container');
      if (!iframeContainer) {
        console.error('iframe-container 元素未找到');
        return;
      }

      // 创建 iframe 元素
      const iframe = document.createElement('iframe');
      iframe.src = window.location.origin + '/YmFzaWN2ZXJ0aW9u/#/systemConfiguration';

      // 监听 iframe 加载完成事件
      iframe.onload = function () {
        console.log('iframe 加载完成，准备移除容器');
        iframeContainer.remove(); // 移除 iframe 容器
      };

      // 将 iframe 添加到容器中
      iframeContainer.appendChild(iframe);
    },
    alarmIconClickHandler() {
      if (this.$store.state.app.showAlarm) return;
      const dashboardRouterPath = '/dashboard/2d';
      if (this.$router?.currentRoute.fullPath == dashboardRouterPath) {
        EventBus.$emit('handleAlarmShow', true);
      } else {
        permissionRouterGo(dashboardRouterPath, this, () => {
          this.$store.commit('app/SET_SHOW_ALARM', true);
          setTimeout(() => {
            EventBus.$emit('handleAlarmShowNotAnimation');
          });
        });
      }
    }
  }
};
</script>

<style scoped lang="less">
@import './layout.less';

:deep(.extra-operations) {
  z-index: 999999;
  position: relative; // 确保z-index生效

  .alarm-icon {
    width: 38px;
    height: 38px;
    position: relative;

    .alarm-num {
      position: absolute;
      left: 0;
      height: 14px;
      padding: 0 4px;
      background: linear-gradient(180deg, #fa8c8c 0%, #e44242 43%, #86688d 100%);
      box-sizing: border-box;
      border: 0.5px solid #ffffff;
      border-radius: 14px;
      font-size: 12px;
      line-height: 12px;
      text-align: center;
      transform: translateX(25px);
    }
  }

  // 实时监测的二级菜单路由
  .second-menu-custom {
    //width: 120px;
    .second-menu-item-custom {
      border: 1px solid @di-border-color-default;
      border-radius: 4px;
      background-color: rgba(14, 50, 90, 0.76);
      color: @di-color-text-second;
      padding: 5px 16px;

      &.router-link-active {
        background: #0a3b6e;
        color: @di-color-text-main;
      }

      &:hover {
        background: #0a3b6e;
        color: @di-color-text-main;
        border-color: @di-border-color-high;

        .down-arrow {
          transform: rotate(180deg);
          transition: 0.5s;
        }
      }

      &:not(:last-child) {
        margin-right: 8px;
      }
    }
  }

  .solar-eye-d {
    color: @di-color-text-second;
    padding: 4px 8px;
    border-radius: 4px;
    z-index: 999999;
    width: 100px;

    &:hover {
      background: rgba(74, 119, 172, 0.31);
    }
  }
}
</style>
