<template>
  <div class="live-container">
    <div class="video" :id="id + liveUuid" style="width: 100%; height: 100%"></div>
    <template v-if="show">
      <a-icon type="fullscreen" class="full-screen" @click="singleFullScreen" />
    </template>
  </div>
</template>

<script>
import moment from "moment";
import {v4 as uuid} from "uuid";
const MSE_IS_SUPPORT = !!window.MediaSource; // 是否支持mse
export default {
  props: {
    id: {
      type: String,
      default: "player",
    },
    url: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      player: null,
      splitNum: 1,
      mseSupport: MSE_IS_SUPPORT,
      tabActive: MSE_IS_SUPPORT ? "mse" : "decoder",
      urls: {
        realplay: "wss://************:6014/proxy/************:559/EUrl/KjuVUic",
        talk: "wss://************:6014/proxy/************:559/EUrl/KjuVUic",
        playback: "wss://************:6014/proxy/************:559/EUrl/KjuVUic",
      },
      playback: {
        startTime: "2023-08-16T00:00:00",
        endTime: "2023-08-16T23:00:00",
        valueFormat: moment.HTML5_FMT.DATETIME_LOCAL_SECONDS,
        seekStart: "2023-08-16T10:00:00",
        rate: "",
      },
      volumeOnSvg: {
        template: `<svg t="1624453273744" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1421" width="200" height="200"><path d="M597.994667 138.005333q130.005333 28.010667 213.994667 132.992t84.010667 241.002667-84.010667 241.002667-213.994667 132.992l0-88q93.994667-28.010667 153.002667-106.005333t59.008-180.010667-59.008-180.010667-153.002667-106.005333l0-88zM704 512q0 120-106.005333 172.010667l0-344q106.005333 52.010667 106.005333 172.010667zM128 384l170.005333 0 213.994667-213.994667 0 684.010667-213.994667-213.994667-170.005333 0 0-256z" p-id="1422"></path></svg>`,
      },
      volumeOffSvg: {
        template: `<svg t="1624453193279" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9147" width="200" height="200"><path d="M512 170.005333l0 180.010667-90.005333-90.005333zM181.994667 128l714.005333 714.005333-53.994667 53.994667-88-88q-74.005333 58.005333-156.010667 77.994667l0-88q50.005333-13.994667 96-50.005333l-181.994667-181.994667 0 288-213.994667-213.994667-170.005333 0 0-256 202.005333 0-202.005333-202.005333zM810.005333 512q0-101.994667-59.008-180.010667t-153.002667-106.005333l0-88q130.005333 28.010667 213.994667 132.992t84.010667 241.002667q0 96-44.010667 178.005333l-64-66.005333q21.994667-53.994667 21.994667-112zM704 512q0 18.005333-2.005333 26.005333l-104-104 0-93.994667q106.005333 52.010667 106.005333 172.010667z" p-id="9148"></path></svg>`,
      },
      judgeurlname:
        "wss://************:6014/proxy/************:559/EUrl/KjuVUic",
      rate: 1,
      audioType: 2,
      muted: true,
      volume: 50,
      InstantParam: 10,
      token: "",
      show: false,
      liveUuid: uuid()
    };
  },
  watch: {
    // 判断url合法后直接开始播放
    url: {
      deep: true,
      handler: function (val, oldVal) {
        console.log("url", val);
        this.realplay(val);
      },
    },
  },
  computed: {
    mode: function () {
      return this.tabActive === "mse" ? 0 : 1;
    },
  },
  methods: {
    init() {
      // 设置播放容器的宽高并监听窗口大小变化
      window.addEventListener("resize", () => {
        this.player.JS_Resize();
      });
    },
    createPlayer() {
      this.player = new JSPlugin({
        szId: this.id + this.liveUuid, //父窗口id，需要英文字母开头 必填
        szBasePath: "./", // 必填,与h5player.min.js的引用路径一致
        iMaxSplit: 1,
        iCurrentSplit: 1,
        openDebug: true,
        mseWorkerEnable: false, //是否开启多线程解码，分辨率大于1080P建议开启，否则可能卡顿
        bSupporDoubleClickFull: true, //是否支持双击全屏，true-双击是全屏；false-双击无响应
        oStyle: {
          borderSelect: "#FFCC00",
        },
      });

      // 事件回调绑定
      this.player.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {
          //插件选中窗口回调
          console.log("windowSelect callback: ", iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
          //插件错误回调
          console.log("pluginError callback: ", iWndIndex, iErrorCode, oError);
        },
        windowEventOver: function (iWndIndex) {
          //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex) {
          //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex) {
          //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {
          //全屏切换回调
          console.log("fullScreen callback: ", bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
          //首帧显示回调
          console.log(
            "firstFrame loaded callback: ",
            iWndIndex,
            iWidth,
            iHeight
          );
        },
        performanceLack: function (iWndIndex) {
          //性能不足回调
          console.log("performanceLack callback: ", iWndIndex);
        },
        StreamEnd: function (iWndIndex) {
          //性能不足回调
          console.log("recv StreamEnd: ", iWndIndex);
        },
        StreamHeadChanged: function (iWndIndex) {
          console.log("recv StreamHeadChanged: ", iWndIndex);
        },
        ThumbnailsEvent: (iWndIndex, eventType, eventCode) => {
          console.log(
            "recv ThumbnailsEvent: " +
              iWndIndex +
              ", eventType:" +
              eventType +
              ", eventCode:" +
              eventCode
          );
        },
        InterruptStream: (iWndIndex, iTime) => {
          console.log(
            "recv InterruptStream: " + iWndIndex + ", iTime:" + iTime
          );
        },
        ElementChanged: (iWndIndex, szElementType) => {
          //回调采用的是video还是canvas
          console.log(
            "recv ElementChanged: " +
              iWndIndex +
              ", szElementType:" +
              szElementType
          );
        },
      });
    },
    arrangeWindow() {
      let splitNum = this.splitNum;
      this.player.JS_ArrangeWindow(splitNum).then(
        () => {
          console.log(`arrangeWindow to ${splitNum}x${splitNum} success`);
        },
        (e) => {
          console.error(e);
        }
      );
    },
    wholeFullScreen() {
      this.player.JS_FullScreenDisplay(true).then(
        () => {
          console.log(`wholeFullScreen success`);
        },
        (e) => {
          console.error(e);
        }
      );
    },
    singleFullScreen() {
      this.player.JS_FullScreenSingle(this.player.currentWindowIndex).then(
        () => {
          console.log(`singleFullScreen success`);
        },
        (e) => {
          console.error(e);
        }
      );
    },
    /* 预览&对讲 */
    realplay(realplayUrl) {
      let { player, mode, token } = this;
      player.JS_SetTraceId(0, true);
      player
        .JS_Play(realplayUrl, { realplayUrl, mode, keepDecoder: 0, token }, 0)
        .then(
          () => {
            this.show = true;
            console.log("realplay success");
            player.JS_GetTraceId(0).then((id) => {
              console.log("traceid:", id);
            });
          },
          (e) => {
            console.error(e);
            this.show = false;
          }
        );
    },
    stopPlay() {
      this.player.JS_Stop().then(
        () => {
          this.playback.rate = 0;
          console.log("stop realplay success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    talkStart() {
      let url = this.urls.talk;
      this.player.JS_StartTalk(url, { token: this.token }).then(
        () => {
          console.log("talkStart success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    talkStop() {
      this.player.JS_StopTalk().then(
        () => {
          console.log("talkStop success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    /* 声音、抓图、录像 */
    openSound() {
      this.player.JS_OpenSound().then(
        () => {
          console.log("openSound success");
          this.muted = false;
        },
        (e) => {
          console.error(e);
        }
      );
    },
    closeSound() {
      this.player.JS_CloseSound().then(
        () => {
          console.log("closeSound success");
          this.muted = true;
        },
        (e) => {
          console.error(e);
        }
      );
    },
    setVolume(value) {
      let player = this.player,
        index = player.currentWindowIndex;
      this.player.JS_SetVolume(index, value).then(
        () => {
          console.log("setVolume success", value);
        },
        (e) => {
          console.error(e);
        }
      );
    },
    capture(imageType) {
      let player = this.player,
        index = player.currentWindowIndex;

      player.JS_CapturePicture(index, "img", imageType).then(
        () => {
          console.log("capture success", imageType);
        },
        (e) => {
          console.error(e);
        }
      );
      //player.JS_CapturePicture(index, 'img', imageType, function () { //数据恢复
      // console.log(arguments, "arguments")
      //}
      //).then(
      //  () => { console.log('capture success', imageType) },
      // e => { console.error(e) }
      //)
    },
    streamcb(data) {
      console.log(data);
    },
    recordStart(type) {
      const codeMap = { MP4: 5, PS: 2 };
      //let options = {irecordType: 2, cbStreamCB: streamcb}
      let player = this.player,
        index = player.currentWindowIndex,
        fileName = `${moment().format("YYYYMMDDHHmmss")}.mp4`;
      typeCode = codeMap[type];

      player
        .JS_StartSaveEx(index, fileName, typeCode, {
          irecordType: 1,
          cbStreamCB: this.streamcb,
        })
        .then(
          () => {
            console.log("record start ...");
          },
          (e) => {
            console.error(e);
          }
        );
    },

    recordStop() {
      let player = this.player;
      index = player.currentWindowIndex;

      player.JS_StopSave(index).then(
        () => {
          console.log("record stoped, saving ...");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    /* 电子放大、智能信息 */
    enlarge() {
      let player = this.player,
        index = player.currentWindowIndex;

      player.JS_EnableZoom(index).then(
        () => {
          console.log("enlarge start..., select range...");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    enlargeClose() {
      let player = this.player,
        index = player.currentWindowIndex;

      player.JS_DisableZoom(index).then(
        () => {
          console.log("enlargeClose success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    intellectTrigger(openFlag) {
      let player = this.player,
        index = player.currentWindowIndex;

      player.JS_RenderALLPrivateData(index, openFlag).then(
        () => {
          console.info("JS_RenderALLPrivateData success");
        },
        (err) => {
          console.error("JS_RenderALLPrivateData failed:", err);
        }
      );
    },
    getvideoInfo() {
      let player = this.player,
        index = player.currentWindowIndex;

      player.JS_GetVideoInfo(index).then(function (videoInfo) {
        console.log("videoInfo:", videoInfo);
      });
    },
    getOSDTime() {
      let player = this.player,
        index = player.currentWindowIndex;

      player.JS_GetOSDTime(index).then(function (time) {
        //console.log("osdTime:", time);
        let data = new Date(time);
        console.log("osdTime:" + time + ",UTC:" + data);
      });
    },
    jsdecoderVersion() {
      let v = this.player.JS_GetSdkVersion();
      console.log("JS_GetSdkVersion:", v);
    },
    ChangeMode() {
      let player = this.player,
        index = player.currentWindowIndex;
      this.player.JS_ChangeMode(index);
    },
    Speed() {
      let player = this.player,
        index = player.currentWindowIndex;
      this.player.JS_Speed(index, parseFloat(this.rate)).then(
        (rate) => {
          this.playback.rate = rate;
          console.log("Speed success, current rate", rate);
        },
        (e) => {
          console.error(e);
        }
      );
    },
    judgeUrl() {
      let player = this.player,
        index = player.currentWindowIndex;
      this.player
        .JS_OptimizeCapability(index, this.judgeurlname)
        .then((type) => {
          console.log("JS_OptimizeCapability success:", type);
        });
    },
    recordStartTalk() {
      let fileName = `${moment().format("YYYYMMDDHHmmss")}.mp3`;
      this.player.JS_StartSaveTalk(fileName, parseInt(this.audioType)).then(
        () => {
          console.log("JS_StartSaveTalk success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    recordStopTalk() {
      this.player.JS_StopSaveTalk().then(
        () => {
          console.log("JS_StopSaveTalk success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    OpenInstant() {
      let option = {
        bOpenflag: true,
        bInstantTime: parseInt(this.InstantParam),
      };
      this.player
        .JS_InstantSetParam(this.player.currentWindowIndex, option)
        .then(
          () => {
            console.log("JS_InstantSetParam success");
          },
          (e) => {
            console.error(e);
          }
        );
    },

    StartInstant() {
      this.player.JS_StartInstant(this.player.currentWindowIndex).then(
        () => {
          console.log("JS_StartInstant success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    StopInstant() {
      this.player.JS_StopInstant(this.player.currentWindowIndex).then(
        () => {
          console.log("JS_StopInstant success");
        },
        (e) => {
          console.error(e);
        }
      );
    },
    InstantTatolduration() {
      this.player.JS_InstantTotalDuration(this.player.currentWindowIndex).then(
        (time) => {
          console.log("JS_InstantTotalDuration:", time);
        },
        (e) => {
          console.error(e);
        }
      );
    },
    InstantCurrduration() {
      this.player.JS_InstantCurrDuration(this.player.currentWindowIndex).then(
        (time) => {
          console.log("JS_InstantCurrDuration:", time);
        },
        (e) => {
          console.error(e);
        }
      );
    },
  },
  mounted() {
    this.$el.style.setProperty("display", "block");
    this.init();
    this.createPlayer();
    if (this.url) {
      this.realplay(this.url);
    }
  },
};
</script>

<style scoped lang="less">
.live-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.volume-icon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1000;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.full-screen {
  position: absolute;
  bottom: 20px;
  right: 10px;
  z-index: 1000;
  cursor: pointer;
  color: #fff;
}

:deep(.sub-wnd){
  border: none !important;
}
</style>
