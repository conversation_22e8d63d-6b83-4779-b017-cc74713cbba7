<!-- 直播组件 -->
<template>
  <video
    controls
    autoplay
    muted
    class="width-100 height-100"
    controlsList="nodownload  noplaybackrate"
    disablePictureInPicture
    :id="'myVideo' + url"
    :poster="computedPoster"
    v-on:fullscreenchange="onFullscreenChange"
    v-on:webkitfullscreenchange="onFullscreenChange"
  ></video>
</template>

<script>
import flvjs from "flv.js";
import emptyPng from "@/assets/images/2dMap/no-data.png";
import { clone } from "xe-utils";

export default {
  name: "Live",
  props: {
    url: {
      type: String,
      default: "",
    },
    hasControl: {
      type: Boolean,
      default: true,
    },
    firstFramePlace: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      player: null,
      lastDecodedFrame: 0,
      timerId: null,
      count: 0,
    };
  },
  watch: {
    // 判断url合法后直接开始播放
    url: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (JSON.stringify(val) != JSON.stringify(oldVal)) {
          this.$nextTick(() => {
            this.clearPlay();
            this.createdPlay();
          });
        }
      },
    },
  },
  computed: {
    computedPoster() {
      return this.firstFramePlace ? emptyPng : "";
    },
  },
  created() {},
  mounted() {
    this.createdPlay();
  },
  beforeDestroy() {
    if (this.player) {
      this.clearPlay();
    }
  },
  methods: {
    // 检测浏览器是否支持 flv.js
    createdPlay() {
      if (flvjs.isSupported()) {
        // 如果地址为空 屏蔽之
        if (!this.url) {
          return;
        }
        let videoDom = document.getElementById("myVideo" + this.url);
        // 创建一个播放器实例
        this.player = flvjs.createPlayer(
          {
            type: "flv", // 媒体类型，默认是 flv,
            isLive: true, // 是否是直播流
            // hasAudio: true, // 是否有音频
            // hanVideo: true, // 是否有视频
            url: this.url, // 流地址
          },
          {
            cors: true, // 是否跨域
            autoCleanupSourceBuffer: true, //对SourceBuffer进行自动清理缓存
            autoCleanupMaxBackwardDuration: 120, //    当向后缓冲区持续时间超过此值（以秒为单位）时，请对SourceBuffer进行自动清理
            autoCleanupMinBackwardDuration: 60, //指示进行自动清除时为反向缓冲区保留的持续时间（以秒为单位）。
            enableStashBuffer: false, //关闭IO隐藏缓冲区
            reuseRedirectedURL: true, //重用301/302重定向url，用于随后的请求，如查找、重新连接等。
            stashInitialSize: 128,
          }
        );
        this.player.attachMediaElement(videoDom);
        this.player.load();
        setTimeout(() => {
          this.player.play();
        }, 500);
        // 监听视频
        this.listenVideo();
        // 先清除timerId
        if (this.timerId !== null) {
          clearInterval(this.timerId);
        }
        // 开始播放追帧
        this.timerId = setInterval(() => {
          this.jumpToEndBuffer();
        }, 60 * 1000);
      } else {
        console.log("当前浏览器不支持flvjs");
      }
    },
    // 直播跳到最后
    jumpToEndBuffer() {
      let buffered = this.player ? this.player.buffered : 0;
      if (buffered.length > 0) {
        let end = buffered.end(0);
        if (end - this.player.currentTime > 2) {
          this.player.currentTime = end - 0.1;
        }
      }
    },
    // 清除flv播放
    clearPlay() {
      if (this.player) {
        this.player.pause(); //停止播放
        this.player.unload(); //停止加载
        this.player.detachMediaElement(); //销毁实例
        this.player.destroy();
        this.player = null;
        if (this.timerId) {
          clearInterval(this.timerId);
          this.timerId = null;
        }
      }
    },
    // 重新加载直播视频
    reloadVideo() {
      this.clearPlay();
      this.createdPlay();
    },
    listenVideo() {
      const that = this;
      // 监听播放器状态
      that.player.on(
        flvjs.Events.ERROR,
        (errorType, errorDetail, errorInfo) => {
          console.log("errorType:", errorType, errorDetail, errorInfo);
          //视频出错后销毁重新创建
          if (that.player) {
            that.reloadVideo();
          }
        }
      );
      // 监听播放器是否卡死
      that.player.on("statistics_info", function (res) {
        // console.log("直播状态", res.decodedFrames, that.lastDecodedFrame);
        if (that.lastDecodedFrame == 0) {
          that.lastDecodedFrame = res.decodedFrames;
          return;
        }
        if (that.lastDecodedFrame != res.decodedFrames) {
          that.lastDecodedFrame = res.decodedFrames;
          that.count = 0;
        } else {
          that.count++;
          if (that.player && that.count > 10) {
            that.lastDecodedFrame = 0;
            that.count = 0;
            that.reloadVideo();
          }
        }
      });
    },
    onFullscreenChange() {
      const clonedUrl = clone(this.url, true);
      if (document.fullscreenElement || document.webkitFullscreenElement) {
        // 全屏状态
        console.log("=>(SceneVideoPopup.vue:58) video全屏时触发");
        // eslint-disable-next-line vue/no-mutating-props
        this.url = clonedUrl + "&&&streamMode=01";
      } else {
        console.log("=>(SceneVideoPopup.vue:58) video还原时触发");
        // eslint-disable-next-line vue/no-mutating-props
        this.url = clonedUrl.replace("&&&streamMode=01","")
      }
    },
  },
};
</script>

<style lang="less" scoped>
//播放按钮
video::-webkit-media-controls-play-button {
  display: none;
}

//进度条
video::-webkit-media-controls-timeline {
  display: none;
}

//音量按钮
//video::-webkit-media-controls-mute-button {
//  display: none;
//}

//观看的当前时间
video::-webkit-media-controls-current-time-display {
  display: none;
}

video::-webkit-media-controls-time-remaining-display {
  display: none;
}
</style>
