<template>
  <div class="page-pagination">
    <a-pagination
      :size="size"
      :default-current="1"
      :show-size-changer="showSizeChanger"
      :pageSizeOptions="pageSizeOptions"
      @change="currentChange"
      @showSizeChange="onChange"
      v-model="current"
      :page-size="pageSize"
      :total="total"
      :show-total="(total) => `共 ${total} 条`"
      :disabled="disabled"
    />
  </div>
</template>

<script>

export default {
  name: "pagePagination",
  props: {
    size: {
      type: String,
      default: "small",
    },
    current: {
      type: Number,
      default: 1,
      required: true,
    },
    pageSize: {
      type: Number,
      default: 10,
      required: true,
    },
    total: {
      default: 0,
      required: true,
    },
    showSizeChanger: {
      type:Boolean,
      default: true,
    },
    pageSizeOptions: {
      type: Array,
      default: () => {
        return ['5', '10', '15', '20', '50', '100', '500'];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    currentChange(current) {
      // this.$emit("current-change",current); //页码change事假取消，合并到一个方法中
      this.$emit("size-change", current, this.pageSize);
    },
    onChange(current, pageSize) {
      console.log('onChange')
      this.$emit("size-change", 1, pageSize);
    },
  },
};
</script>

<style lang="less" scoped>
.page-pagination {
  width: 100%;
  padding: 12px 0 6px 0;
  text-align: center;
}
</style>
