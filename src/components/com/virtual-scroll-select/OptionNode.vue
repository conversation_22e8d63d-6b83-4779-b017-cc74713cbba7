<template>
  <div class="select-option" @click="selectItem" @mouseover="isShow = true" @mouseleave="isShow = false">
    <div v-if="isMultiple" class="is-multiple" :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}">
      <span :title="source['name']" class="option-name">{{source[label]}}</span>
      <svg-icon  :class="{ 'isShow': isShow }" v-if="source['checked'] || isShow" iconClass="selected"></svg-icon>
    </div>
    <div v-else :class="{'selected': source['checked'], 'isDisabled': source['isDisabled']}">
      <span :title="source[label]" class="option-name">{{source[label]}}</span>
    </div>
  </div>
</template>
<script>
import { EventBus } from './bus.js';
export default {
  name: 'OptionNode',
  props: {
    // 每一行的索引
    index: {
      type: Number
    },
    // 每一行的内容
    source: {
      type: Object,
      default () {
        return {};
      }
    },
    // 需要显示的名称
    label: {
      type: String
    },
    // 绑定的值
    value: {
      type: String
    },
    // 是否多选
    isMultiple: {
      type: Boolean,
      default () {
        return false;
      }
    }
  },
  data () {
    return {
      isShow: false // 多选hover后面√是否显示
    };
  },
  methods: {
    selectItem () {
      if (!this.source.isDisabled) {
        EventBus.$emit('updateSelect', this.source);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.select-option {
  width: 100%;
  cursor: pointer;
  color: @di-color-text-white;
  font-size: 14px;
  & > div {
    padding: 5px 12px;
  }
  &:hover {
    background-color: rgba(64, 170, 255, 0.2);
    border-radius: 4px;
  }
  .selected {
    background: #40AAFF;
    color: @di-color-text-white;
    border-radius: 4px;
    .checked {
      color: @di-color-text-main !important;
    }
  }
  & > div {
    display: flex;
    align-items: center;
    .option-name {
      padding-right: 32px;
      display: flex;
      display: block;
      position: relative;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

  }
  .isDisabled {
    color: @di-color-text-gray;
    cursor: not-allowed;
    .isShow {
      color: @di-color-text-gray !important;
    }
  }
}
</style>
