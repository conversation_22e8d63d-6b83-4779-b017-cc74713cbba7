<template>
  <a-button
    class="di-primary-button"
    :size="size"
    :disabled="disabled"
    v-bind="$attrs"
    v-on="{
      ...$listeners,
      click: debouncedClick,
    }"
  >
    {{ label }}
  </a-button>
</template>

<script>
import { debounce } from "xe-utils";
export default {
  name: "DiThrottleButton",
  props: {
    label: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "default",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    delay: {
      type: Number,
      default: 200,
      validator: (value) => value >= 0,
    },
  },
  data() {
    return {
      debouncedClick: () => {},
    };
  },
  created() {
    // 在created钩子中创建防抖函数，确保只创建一次
    this.debouncedClick = debounce(
      (event) => {
        this.$emit("click", event);
      },
      this.delay,
      { leading: true, trailing: false }
    );
  },
};
</script>

<style scoped lang="less">
.di-primary-button:not(.di-cancel-btn) {
  background: @di-btn-bg;
  border: 1px solid @di-border-color-high;

  &:focus {
    color: fade(@di-color-text-black, 65%);
  }

  &:hover {
    color: fade(@di-color-text-black, 65%);
    background: @di-btn-hover-bg;
  }
}
</style>
