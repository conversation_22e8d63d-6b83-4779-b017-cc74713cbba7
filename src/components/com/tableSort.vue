
<template>
  <div @click="mySortChange('text', column.property)" class="sort-head">
    <span>{{ column.title }}</span>
    <a-icon type="caret-up" title="升序" @click="mySortChange('up', column.property)" class="sortIcon up-icon"
      :style="{ 'color': (upSort && sortFiled == filed) ? '#1890FF' : 'inherit' }" />
    <a-icon type="caret-down" title="降序" class="sortIcon down-icon" @click="mySortChange('down', column.property)"
      :style="{ 'color': (downSort && sortFiled == filed) ? '#1890FF' : 'inherit' }" />
  </div>
</template>
<script>
export default {
  props: {
    // 表格参数
    column: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 首次单击是否默认向下
    isUp: {
      type: Boolean,
      default: false
    },
    // 表格字段
    filed: {
      type: String,
      default: ''
    },
    // 排序字段
    sortFiled: {
      type: String,
      default: ''
    },
    // 排序类型
    sortKind: {
      type: String,
      default: ''
    },
    // 降序图标是否高亮
    downSort: {
      type: Boolean,
      default: false
    },
    // 升序图标是否高亮
    upSort: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 自定义表格排序事件
    mySortChange (val, property) {
      if (val == 'up') {
        this.stopEvenet();
        this.upSort = this.sortFiled == property ? !this.upSort : true;
        this.downSort = false;
        this.sortKind = this.upSort ? 'asc' : '';
        this.sortFiled = this.upSort ? property : '';
      } else if (val == 'down') {
        this.stopEvenet();
        this.downSort = this.sortFiled == property ? !this.downSort : true;
        this.upSort = false;
        this.sortKind = this.downSort ? 'desc' : '';
        this.sortFiled = this.downSort ? property : '';
      } else {
        if (this.isUp && this.sortFiled != property) {
          this.upSort = false;
          this.downSort = true;
          this.sortKind = 'desc';
        } else if ((!this.downSort && !this.upSort) || this.sortFiled != property) {
          this.upSort = true;
          this.downSort = false;
          this.sortKind = 'asc';
        } else {
          if (this.upSort) {
            this.upSort = false;
            this.downSort = true;
            this.sortKind = 'desc';
          } else {
            this.upSort = true;
            this.downSort = false;
            this.sortKind = 'asc';
          }
        }
        this.sortFiled = property;
      }
      this.$emit('sortChange', this.sortKind, this.sortFiled, this.downSort, this.upSort);
    },
    // 阻止冒泡方法
    stopEvenet (e) {
      e = e || window.event;
      if (e.stopPropagation) { // W3C阻止冒泡方法
        e.stopPropagation();
      } else {
        e.cancelBubble = true; // IE阻止冒泡方法
      }
    }
  }
};
</script>
<style lang="less" scoped>
.sortIcon {
  position: absolute;
  cursor: pointer;
  margin-left: 4px;
}

.up-icon {
  top: 10px;
}

.down-icon {
  top: 19px;
}

.sort-head {
  cursor: pointer;
}
</style>
