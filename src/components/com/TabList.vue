<template>
  <div class="tab-list">
    <div
      v-for="(item, index) in listCopy"
      :key="index"
      class="tab-item cursor-pointer"
      @click="handleChange(index)"
    >
      <div class="left-img-box">
        <img
          class="width-100 height-100"
          :src="item.imgSrc"
          :alt="item.remark"
        />
      </div>
      <div class="right-text-box">
        <div class="text-box flex-space-between" style="line-height: 26px">
          <span class="text">{{ item.remark }}</span>
          <span
            class="total num-font-700"
            v-if="Object.hasOwn(item, 'total')"
            >{{ item.total }}</span
          >
        </div>
        <div
          v-if="Object.hasOwn(item, 'addNum')"
          class="sub-text-box flex-space-between"
          style="line-height: 20px"
        >
          <span class="sub-text">{{ item.subText || "今日新增" }}</span>
          <span class="add-num num-font-700">{{ item.addNum }}</span>
        </div>
      </div>
    </div>
    <div class="active-tab" :style="{ left: left + 'px' }" v-show="left">
      <img
        class="width-100"
        src="@/assets/images/tab-active.png"
        alt="当前激活的导航栏"
      />
    </div>
  </div>
</template>

<script>
import { USER_AUTH } from "@/store/mutation-types";
export default {
  name: "TabList",
  props: {
    list: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      activeKey: null,
      left: null,
      listCopy: [],
    };
  },
  created() {
    let authList = JSON.parse(sessionStorage.getItem(USER_AUTH) || "[]");
    let arr = [];
    authList.forEach((element) => {
      arr.push(element.action);
    });
    this.listCopy = this.list.filter((item) => {
      return arr.indexOf(item.auth) > -1 || !item.auth;
    });
    if (this.listCopy.length > 0) {
      this.activeKey = 0;
      this.left = 0;
    }
  },
  mounted() {
    window.addEventListener("resize", this.setLeft);
  },
  methods: {
    handleChange(index) {
      this.activeKey = index;
    },
    async setLeft() {
      await this.$nextTick();
      const tabItemEl = $(".tab-item");
      this.left =
        this.activeKey * (tabItemEl.innerWidth() + 16) +
        (tabItemEl.innerWidth() - 200) / 2;
    },
  },
  watch: {
    activeKey: {
      immediate: true,
      handler(val) {
        if (val || val == 0) {
          this.setLeft();
          this.$emit("change", val, this.listCopy[val]);
        }
      },
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setLeft);
  },
};
</script>
<style scoped lang="less">
.tab-list {
  display: flex;
  position: relative;

  .tab-item {
    display: flex;
    align-items: center;
    padding: 4px 17px 4px 13px;
    border-radius: 4px;
    width: 280px;
    height: 58px;
    background: linear-gradient(180deg, #18488b 0%, #0e4085 100%);

    &:hover {
      background: linear-gradient(180deg, #2860af 0%, #2259a4 100%);
    }

    &:not(:last-child) {
      margin-right: 16px;
    }

    .left-img-box {
      width: 50px;
      height: 50px;

      img {
        object-fit: cover;
      }
    }

    .right-text-box {
      margin-left: 8px;
      flex: 1;

      .text-box {
        .text {
          font-size: 16px;
          font-weight: 600;
          color: #85caff;
        }

        .total {
          font-size: 20px;
          font-weight: bold;
          color: #ffffff;
        }
      }
    }

    .sub-text-box {
      .sub-text {
        color: #85caff;
        font-size: 12px;
      }

      .add-num {
        font-size: 16px;
        font-weight: bold;
        color: #85caff;
      }
    }
  }

  .active-tab {
    position: absolute;
    width: 200px;
    top: 75%;
    transition: 0.5s;
  }
}
</style>
