<template>
  <a-drawer
    :width="width"
    :header-style="{ height: '55px', background: 'transparent' }"
    :visible="visible"
    @close="close"
    :destroyOnClose="true"
    :get-container="getContainer"
    :maskClosable="false"
    :wrap-style="{ position: 'absolute' }"
    :afterVisibleChange="afterVisibleChange"
    class="drawer-box"
    v-bind:class="{ 'no-parentId': noContainer }"
  >
    <template slot="title">
      <div style="width: 100%">
        <span>{{ title }}</span>
        <span
          v-if="statusLabel"
          :class="`title-statusCol color-${$getColorByName(statusLabel)}`"
          >{{ statusLabel }}</span
        >
      </div>
    </template>
    <component
      :parentId="parentId"
      ref="formComponent"
      v-bind:is="form_component"
      @cancel="cancel"
      @doClose="doClose"
      @operateInDetails="operateInDetailsChange"
    ></component>
  </a-drawer>
</template>

<script>
// perf: 由于部分title通过api获取，导致高度塌陷，render阶段上下跳动
export default {
  name: "drawerView",
  props: {
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      default: "",
    },
    singleOpenId: {
      type: String,
      required: false,
      default: "",
    },
    width: {
      type: String,
      required: false,
      default: "100%",
    },
  },
  data() {
    return {
      visible: false,
      title: "",
      statusLabel: null,
      operateInDetails: false,
      type: "",
      row: null,
      old: null,
      form_component: null,
      from_path: null,
      noContainer: false,
    };
  },
  methods: {
    /*
        获取抽屉组件挂在的DOM
      */
    getContainer() {
      let parentId = this.parentId;
      if (!parentId) return document.querySelector(".main-parent");
      let mains = document.querySelectorAll(".main");
      return (
        document.getElementById(parentId) ||
        (mains.length
          ? mains[mains.length - 1]
          : document.querySelector(".main-parent"))
      );
    },
    /*
        抽题初始化方法
        type：新增、编辑。。。等模式区分
        row：行数据
      */
    init(type = null, row = null, from_path = null) {
      if (!from_path) {
        return;
      }
      this.operateInDetails = false;
      let parentId = this.parentId;
      let el = document.getElementById(parentId);
      let mains = document.querySelectorAll(".main");
      this.noContainer = !parentId || (!el && !mains.length);
      this.from_path = from_path;
      this.form_component = (resolve) =>
        require(["@/views" + from_path], resolve);
      this.type = type;
      this.row = JSON.parse(JSON.stringify(row));
      this.visible = true;
    },
    /*
        抽屉关闭回调事件
      */
    cancel(map) {
      if (this.type != "3" || map) {
        /* 查看详情时不需要刷新主页面 */
        this.$emit("cancel", map);
      }
      this.reset();
    },
    /*
        抽屉关闭按钮事件
      */
    close() {
      let formComponent = this.$refs.formComponent;
      // 无人机上传文件、上传巡检图页面需要判断是否正在上传文件中，如果是则需要用户确认是否关闭
      if (this.row && ["32", "33"].includes(this.row.workbenchType)) {
        formComponent && formComponent.getUploadStatus();
        return false;
      } else if (this.row && this.row.isTips) {
        if (this.title !== "详情") {
          // 电站管理 履约项目 新增和编辑时候以及详情调整到编辑点击关闭提示
          this.$confirm({
            title: "数据尚未保存，是否确定关闭?",
            okText: "确定",
            cancelText: "取消",
            centered: true,
            onOk: () => {
              // 从详情页到编辑页面 返回
              if (this.type == "4" && this.title == "编辑") {
                this.operateInDetailsChange(false, "", "详情");
                // isBackDetail 从编辑返回详情判断参数初始化
                this.$refs.formComponent.init("4", {
                  show: false,
                  keyId: this.row.keyId,
                  isTips: true,
                  isBackDetail: false,
                  id: this.row.id,
                });
              } else {
                this.cancel();
              }
            },
          });
        } else {
          // 详情页面点击关闭也要刷新
          this.cancel();
        }
      } else {
        if (this.operateInDetails) {
          // 在页面有编辑终止重启等操作刷新了数据 关闭页面需要列表刷新
          this.$emit("cancel");
        } else {
          this.$emit("close");
        }
        formComponent && formComponent.reset && formComponent.reset();
        this.reset();
      }
    },
    // 无人机上传文件、上传巡检图页面执行关闭页面操作
    doClose() {
      let formComponent = this.$refs.formComponent;
      formComponent && formComponent.reset && formComponent.reset();
      this.reset();
      this.visible = false;
    },
    // 有编辑终止重启等操作后 刷新页面回调方法
    operateInDetailsChange(val, statusLabel, title) {
      this.operateInDetails = val;
      this.statusLabel = statusLabel;
      if (title) this.title = title;
    },
    /*
        参数重置
      */
    reset() {
      this.type = "";
      this.row = null;
      this.title = "";
      this.statusLabel = "";
      this.visible = false;
    },
    /*
        切换抽屉时动画结束回调事件
      */
    afterVisibleChange() {
      this.getFormComponent();
    },
    getFormComponent() {
      if (this.visible) {
        if (this.$refs.formComponent) {
          let formComponent = this.$refs.formComponent;
          this.getTitle(formComponent);
        } else {
          setTimeout(this.getFormComponent, 100);
        }
      }
    },
    async getTitle(formComponent) {
      let initReturn = await formComponent.init(this.type, this.row);
      if (this.isObject(initReturn)) {
        this.title = initReturn.title;
        this.statusLabel = initReturn.statusLabel;
      } else {
        this.title = initReturn;
      }
    },
    isObject(obj) {
      return Object.prototype.toString.call(obj) === "[object Object]";
    },
  },
  beforeDestroy() {
    this.visible = false;
    Object.assign(this.$data, this.$options.data());
  },
};
</script>
<style lang="less" scoped>
.no-parentId {
  position: fixed !important;

  :deep(.ant-drawer-content-wrapper) {
    width: calc(100vw - 32px) !important;
    height: calc(100% - 118px);
    margin-top: 80px;
    padding-right: 32px;
  }
}

@noSubmit-bg: #a441d5;
@waitBegin-bg: #d54941;
@undo-bg: #ff8100;
@processing-bg: #1366ec;
@check-bg: #e2aa11;
@finish-bg: #2ba471;
@stop-bg: #c5c5c5;

.title-statusCol {
  width: 52px !important;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  margin-left: 10px;
  padding: 0 8px;
  border: 1px solid;
  box-sizing: border-box;
  border-radius: 2px;
  opacity: 1;
}

.color-noSubmit {
  color: @noSubmit-bg;
  border-color: @noSubmit-bg;
  background: rgba(@noSubmit-bg, 0.2);
}

.color-waitBegin {
  color: @waitBegin-bg;
  border-color: @waitBegin-bg;
  background: rgba(@waitBegin-bg, 0.2);
}

.color-undo {
  color: @undo-bg;
  border-color: @undo-bg;
  background: rgba(@undo-bg, 0.2);
}

.color-processing {
  color: @processing-bg;
  border-color: @processing-bg;
  background: rgba(@processing-bg, 0.2);
}

.color-check {
  color: @check-bg;
  border-color: @check-bg;
  background: rgba(@check-bg, 0.2);
}

.color-finish {
  color: @finish-bg;
  border-color: @finish-bg;
  background: rgba(@finish-bg, 0.2);
}

.color-stop {
  color: @stop-bg;
  border-color: @stop-bg;
  background: rgba(@stop-bg, 0.2);
}
</style>
