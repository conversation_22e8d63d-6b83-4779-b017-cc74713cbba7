import DiThrottleButton from './DiThrottleButton'
import DrawerView from './DrawerView';
import TabList from './TabList'
import PagePagination from "./PagePagination";
import DetailLayout from "./DetailLayout";
import tableSort from "./tableSort";
import ModalTitle from "@/views/2dMap/modules/ModalTitle";
import VirtualScrollSelect from "./virtual-scroll-select";
export default {
    install(Vue) {
        Vue.component('DiThrottleButton', DiThrottleButton);
        Vue.component('DrawerView', DrawerView);
        Vue.component('TabList',TabList)
        Vue.component('PagePagination',PagePagination)
        Vue.component('DetailLayout',DetailLayout)
        Vue.component('tableSort', tableSort);
        Vue.component('ModalTitle', ModalTitle);
        Vue.component('VirtualScrollSelect', VirtualScrollSelect);
    }
}