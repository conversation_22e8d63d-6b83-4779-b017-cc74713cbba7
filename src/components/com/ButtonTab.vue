<!-- 数智电站 按钮tab组合 -->
<template>
  <div class="flex-start width-100">
    <div class="date-item flex-center font-12 cursor-pointer"
         v-for="(item,index) in options"
         :style="item.style || {}"
         :key="index"
         :class="getClass(item.value)"
         @click="change(item)">
      <span>
      {{ item.label }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ButtomTab",
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value:{
      type: String | Number | null | undefined,
      default: () => {
        return null;
      }
    },
    options:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      activeValue:null,
    }
  },
  created() {
  },
  methods: {
    change(item){
      this.$emit('input',item.value)
      this.$emit('change',item.value,item)
    },
    getClass(value){
      if(value == this.value){
        return 'color-text-highlight border-highlight active-item'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.date-item{
  padding: 0 8px;
  height: 24px;
  text-align: center;
  box-sizing: border-box;
  border-radius: 4px;
  margin-right: 8px;
  color: #61A1D5;
  box-sizing: border-box;
  border: 1px solid #61A1D5;
}
.date-item:hover{
  color: @di-color-text-main;
  border: 1px solid @di-color-text-main;
}
.date-item:last-of-type{
  margin-right: 0;
}
.active-item{
  background: rgba(133, 202, 255, 0.2);
}
</style>