import Vue from "vue";
import { USER_INFO, ACCESS_TOKEN } from "@/store/mutation-types";

const getters = {
  userInfo: (state) => {
    state.user.info = isHasProperty() ? Vue.ls.get(USER_INFO) : {};
    return state.user.info;
  },
  token: (state) => state.user.token || Vue.ls.get(ACCESS_TOKEN),
  theme: (state) => state.app.theme,
  permissionList: state => state.user.permissionList,
  addRouters: state => state.permission.addRouters,
  systemConfigMap: state => state.app.systemConfigMap,
  configNetConnect: state => state.app.systemConfigMap?.configNetConnect?.setValue === "true",
  configDataFrequency: state => state.app.systemConfigMap?.configDataFrequency?.setValue,
};

function isHasProperty() {
  return !!Vue.ls.get(USER_INFO);
}

export default getters;
