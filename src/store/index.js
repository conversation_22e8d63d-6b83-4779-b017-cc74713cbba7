import Vue from "vue";
// vuex数据存储
import Vuex from 'vuex';
Vue.use(Vuex)
import user from './modules/user';
import app from './modules/app';
import permission from './modules/permission';
import getters from './getters';
export default new Vuex.Store({
    modules: {
        user,
        app,
        permission
    },
    state: {

    },
    mutations: {

    },
    actions: {

    },
    getters
});