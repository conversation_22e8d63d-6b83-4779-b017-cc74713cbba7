import { getConfigValueListApi } from "@/api/common";
import { camelCase } from "lodash";
import { getModelData as getModelDataApi } from "@/api/2dMap/psOverview";
const app = {
  namespaced: true ,
  state: {
    theme: "dark",
    systemConfigMap: {},
    showAlarm: false,
    alarmTotalCount: 0
  },
  mutations: {
    SET_CONFIG_LIST(state, item) {
      state.systemConfigMap = item;
    },
    SET_SHOW_ALARM(state, isShowAlarm) {
      state.showAlarm = isShowAlarm;
    },
    SET_GLOBAL_ALARM_COUNT(state, count){
      state.alarmTotalCount = count;
    },
    DECREASE_GLOBAL_ALARM_COUNT(state, number= 1){
      state.alarmTotalCount = state.alarmTotalCount - number;
    }
  },
  actions: {
    GetSystemConfigList({ commit }) {
      return new Promise((resolve, reject) => {
        getConfigValueListApi()
          .then((res) => {
            const systemConfigMap = (res || []).reduce((acc, cur) => {
              acc[camelCase(cur.configKey)] = cur;
              return acc;
            }, {});
            commit("SET_CONFIG_LIST", systemConfigMap);
            resolve();
          })
          .catch(() => {
            reject();
          });
      });
    },
    GetGlobalRealTimeAlarmCount({ commit }, params) {
      getModelDataApi(params)
          .then(res=>{
            commit("SET_GLOBAL_ALARM_COUNT", res.result_data?.length);
          });
    }
  },
};

export default app;
