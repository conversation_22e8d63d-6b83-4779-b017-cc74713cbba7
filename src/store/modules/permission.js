import {  constantRouterMap } from '@/router/router.config';
// console.log(constantRouterMap)
const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, data) => {
      state.addRouters = data;
      state.routers = constantRouterMap.concat(data);
      // console.log('-----mutations---SET_ROUTERS----', data)
    }
  },
  actions: {
    // 动态添加主界面路由，需要缓存
    UpdateAppRouter ({ commit }, routes) {
      return new Promise(resolve => {
        let routeList = routes.constRoutes;
        commit('SET_ROUTERS', routeList);
        resolve();
      });
    }
  }
};

export default permission;
