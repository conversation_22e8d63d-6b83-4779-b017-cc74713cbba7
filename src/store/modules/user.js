import Vue from "vue";
import { login, logout } from "@/api/login";
import {
  DEFAULT_TOP_DEPART,
  ACCESS_TOKEN,
  USER_NAME,
  USER_INFO,
  USER_LCA,
  USER_DEPT_LCA,
  UI_CACHE_DB_DICT_DATA,
  ALL_PERMISSION_MENU,
  USER_AUTH,
  SYS_BUTTON_AUTH,
  TENANT_ID,
  PSA_INFO,
} from "@/store/mutation-types";
import { jwtApi, queryPermissionsByUserApi } from "@/api/user";
import { toTreeArray } from "xe-utils";
import { message } from "ant-design-vue";

const user = {
  state: {
    token: null,
    info: {},
    isShowMenu: true,
    permissionList: [],
  },

  mutations: {
    SET_FirstMenu: (state, item) => {
      state.firstMenu = item;
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_INFO: (state, info) => {
      state.info = info;
    },
    SET_PERMISSIONLIST: (state, permissionList) => {
      state.permissionList = permissionList;
    },
    SET_TENANT: (state, id) => {
      state.tenantid = id;
    },
  },

  actions: {
    // 跨平台登录
    ValidateLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        jwtApi(userInfo)
          .then((response) => {
            if (response.success) {
              const result = response.result;
              dealResult({ commit }, result);
              resolve(response);
            } else {
              resolve(response);
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取权限
    GetPermissionList({ commit }) {
      return new Promise((resolve, reject) => {
        queryPermissionsByUserApi({}).then((response) => {
          const menuData = response.result.menu;
          const flattenMenu = toTreeArray(menuData).map((item) => item.path);
          const authData = response.result.auth;
          const allAuthData = response.result.allAuth;
          let hasDoctor = menuData.filter((item) => {
            return item.path === "/iSolarDoctor";
          });
          sessionStorage.setItem(
            "ALL_ORIGINAL_PERMISSION_MENU",
            JSON.stringify(menuData)
          );
          sessionStorage.setItem(
            ALL_PERMISSION_MENU,
            JSON.stringify(flattenMenu)
          );
          sessionStorage.setItem(USER_AUTH, JSON.stringify(authData));
          sessionStorage.setItem(SYS_BUTTON_AUTH, JSON.stringify(allAuthData));
          commit(
            "SET_PERMISSIONLIST",
            hasDoctor.length > 0 ? hasDoctor[0].children : []
          );
          if (hasDoctor.length == 0) {
            message.error({
              content: (h) =>
                h("span", {}, [
                  h("span", "暂无权限，请联系管理员！"),
                  h(
                    "span",
                    {
                      style: {
                        paddingLeft: "40px",
                        cursor: "pointer",
                      },
                      on: {
                        click: () => {
                          message.destroy();
                        },
                      },
                    },
                    "X"
                  ),
                ]),
              duration: 5,
              class: "no-platform",
              style: {
                marginTop: "5vh",
              },
            });
            commit("SET_TOKEN", "");
            commit("SET_FirstMenu", "");
            Vue.ls.remove(ACCESS_TOKEN);
            Vue.ls.remove(UI_CACHE_DB_DICT_DATA);
          }

          // Vue.ls.set(PSA_INFO, {
          //   // TODO,后续移动到登录之后
          //   departCode: "D09",
          //   psaList: ["3100"],
          //   psaNameList: ["肥东金阳光伏电站"],
          //   psId: "107353",
          // });
          resolve(hasDoctor.length > 0 ? hasDoctor[0].children : []);
        });
      });
    },
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo)
          .then((response) => {
            if (response.code == "200") {
              const result = response.result;
              dealResult({ commit }, result);
              resolve(response);
            } else {
              if (response.message == "验证码错误") {
                resolve(response);
              } else {
                reject(response);
              }
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 登出
    Logout({ commit, state }) {
      return new Promise((resolve) => {
        let logoutToken = state.token || Vue.ls.get(ACCESS_TOKEN);
        logout(logoutToken)
          .then(() => {
            commit("SET_TOKEN", "");
            commit("SET_PERMISSIONLIST", []);
            commit("SET_FirstMenu", "");
            commit("app/SET_CONFIG_LIST", {});
            commit("app/SET_GLOBAL_ALARM_COUNT", 0);
            Vue.ls.remove(ACCESS_TOKEN);
            Vue.ls.remove(UI_CACHE_DB_DICT_DATA);
            resolve();
          })
          .catch(() => {
            resolve();
          });
      });
    },
    saveTenant({ commit }, id) {
      Vue.ls.set(TENANT_ID, id);
      commit("SET_TENANT", id);
    },
  },
};

function dealResult({ commit }, result) {
  const dep =
    result.hasOwnProperty("departs") &&
    result.departs &&
    result.departs.length > 0
      ? result.departs[0]
      : "";
  result.userInfo.needHandover = dep.hasOwnProperty("needHandover")
    ? dep.needHandover
    : "";
  result.userInfo.hasAllData = dep.hasOwnProperty("hasAllData")
    ? dep.hasAllData
    : "";
  const userInfo = result.userInfo;
  userInfo.depId = dep.hasOwnProperty("id") ? dep.id : "";
  userInfo.dataRoles = result.dataRoles;
  // Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
  Vue.ls.set(ACCESS_TOKEN, result.token);
  Vue.ls.set(USER_NAME, userInfo.username);
  Vue.ls.set(USER_INFO, userInfo);
  Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems);
  Vue.ls.set(USER_LCA, result.lca);
  Vue.ls.set(USER_DEPT_LCA, result.lca4Dept);
  Vue.ls.set(DEFAULT_TOP_DEPART, result.root.orgCode);
  commit("SET_TOKEN", result.token);
  commit("SET_INFO", userInfo);
  Vue.ls.set(PSA_INFO, {...(result.psDetail ?? {}), departCode: result.psDetail?.depCode})
}

export default user;
