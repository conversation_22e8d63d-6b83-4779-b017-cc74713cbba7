// 全局鉴权系统
import Vue from "vue";
import { ACCESS_TOKEN, TENANT_ID,PSA_INFO } from "@/store/mutation-types";
import store from "@/store";
import router from "@/router";
import { getUrlParam } from './util'

const init = (callback) => {
  let token = Vue.ls.get(ACCESS_TOKEN);
  let st = getUrlParam("token");
  if (token && !getUrlParam("token")) {
    callback && callback();
  } else {
    if (st) {
      localStorage.setItem("deviceId", getUrlParam("deviceId"));
      Vue.ls.set(ACCESS_TOKEN, getUrlParam("token"));
      Vue.ls.set(TENANT_ID, getUrlParam("tenant_id"));
      store.commit('SET_TENANT', getUrlParam('tenant_id'));
      store.commit('SET_TOKEN', getUrlParam('token'));
      // // TODO: 后面在根据psa查询信息，目前先写死配置
      // Vue.ls.set(PSA_INFO, {
      //   departCode: "D09",
      //   psaList: ["3100"],
      //   psaNameList: ["肥东金阳光伏电站"],
      //   psId: "107353",
      // });
      validateSt(st, null, callback);
    } else {
      callback && callback();
    }
  }
};

function validateSt(ticket, service, callback) {
  let params = {};
  params = {
    token: ticket,
  };
  store
    .dispatch("ValidateLogin", params)
    .then((res) => {
      if (res.success) {
        callback && callback();
        // jwt完成后将路由指向实时监测
        router.replace("/dashboard");
      } else {
        // 先返回到fat iSolarWork-D登录页
        window.location.href = `https://${process.env.VUE_APP_ENV}.isolareye.com/#/user/login` + '?backToDigital=1&clear=1';
      }
    })
    .catch((err) => {
      console.log(err);
    });
}

const SSO = {
  init: init,
};

export default SSO;
