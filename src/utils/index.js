import { ALL_PERMISSION_MENU } from "@/store/mutation-types"
const getDevice = () => {
    if (checkIsAppleDevice()) {
        return "ios";
    } else if (checkIsHarmonyOS()) {
        return "HarmonyOS";
    } else {
        return "andriod";
    }
}

const getZoom = ()=>{
    var ratio = 0,//浏览器当前缩放比
        screen = window.screen,//获取屏幕
        ua = navigator.userAgent.toLowerCase();//判断登录端是pc还是手机

    if (window.devicePixelRatio !== undefined) {
        ratio = window.devicePixelRatio;
    }
    else if (~ua.indexOf('msie')) {
        if (screen.deviceXDPI && screen.logicalXDPI) {
            ratio = screen.deviceXDPI / screen.logicalXDPI;
        }
    }
    else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
        ratio = window.outerWidth / window.innerWidth;
    }

    return ratio
}

const setStorage = (key,data) =>{
    let Data = JSON.stringify(data)
    window.localStorage.setItem(key,Data)
}

const getStorage = (key) =>{
    let data = window.localStorage.getItem(key)
    return JSON.parse(data)
}

    const getQuery = () =>{
        // return {
        //     "userId": "1376876302930276354",
        //     "orgCode": "A09",
        //     "workNo": "10011111",
        //     "userName": "数据中心管理员",
        //     "hasAllData": "1",
        //     "clintId": "h5test8ef7fa4ae78b3809e905bc4775",
        //     "sysTenantId": "1",
        //     "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************.GYn-9_KRUDDyYvvK9cX0dtgdQQx6Dx_-sAflGaUQD3k",
        //     "env": "-fat",
        //     "pageParams": {
        //         "id": "1291165",
        //         "title": "【光伏组件】三类缺陷",
        //         "activeValue": "1",
        //         "processDefKey": "activiti5873Workflow",
        //         "otherSts": "2",
        //         "psaId": "163653"
        //     },
        //     "lang": "_zh_CN",
        //     "userAccount": "erpgly",
        //     "statusHeight": 0
        // }
        let href = window.location.href;
        let query = href.split('?query=')[1]
				if(query){
					query = query.replace('#/','');
					query = query.replace('Ddimension','');
				}
				let params = {};
				try{
					params = JSON.parse(decodeURI(query))
				}catch(e){
					console.log('e', e)
					console.log('query', query)
				}
        return params
    }

const getClint = () =>{
    if(/Android|webOS|iPhone|ohos|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
        if(navigator.platform == 'Win32'){
            return 'h5'
        }
        return 'app'
    } else {
        return 'h5'
    }
}

const sleep = (time) =>{
    return new Promise(resolve => {
        setTimeout(()=>{
            resolve()
        },time)
    })
}

const randomNum = range =>{
    return Math.floor(Math.random() * range)
}

// 计算两个经纬度之间的距离  单位米
const getDistance = (lat1, lng1, lat2, lng2) =>{
    let radLat1 = lat1*Math.PI / 180.0;
    let radLat2 = lat2*Math.PI / 180.0;
    let a = radLat1 - radLat2;
    let b = lng1*Math.PI / 180.0 - lng2*Math.PI / 180.0;
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) +
        Math.cos(radLat1)*Math.cos(radLat2)*Math.pow(Math.sin(b/2),2)));
    s = s *6378.137 ;// EARTH_RADIUS;
    s = Math.round(s * 10000) / 10000;
    return s * 1000;
}

/**
 * 寻找对应key的label值
 * @param {String | Number} value 值key
 * @param {Array} list 固定value和label key对象数组
 */
const findLabel = (value,list) =>{
    const option = list.find(item=>item.value  == value)
    return option ?  option.label : ''
}

// 指标数字展示 非法值展示-- 其它转换小数位
const exchangeFixed = (value,fixedNum) =>{
    if(value === null || value === '' || value === undefined || isNaN(value)){
        return '--'
    }
    return (Number(value)).toFixed(fixedNum)
}
/*
 * 带权限判断的路由跳转
 * @param {url} 地址
 * @param {that} this
 * @return 跳转成功或失败
 * */
export function permissionRouterGo (url, that, callback) {
    let menuList = JSON.parse(sessionStorage.getItem(ALL_PERMISSION_MENU) || "[]");
    let flag = false;
    for(let i = 0; i < menuList.length; i++){
        const menuItem = menuList[i];
        if (menuItem === url) {
            flag = true;
            break;
        }
    }
    if (flag) {
        that.$router.push(url);
        callback?.()
    } else {
        that.$message.warning("您当前暂无权限，请联系管理员！");
        throw new Error("您当前暂无权限，请联系管理员！")
    }
}
export default {
    setStorage,
    getStorage,
    getQuery,
    getDevice,
    randomNum,
    getZoom,
    sleep,
    getDistance,
    exchangeFixed,
    findLabel,
    getClint,
}

