import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types';
import store from '@/store';

const VueAxios = {
    vm: {},
    // eslint-disable-next-line no-unused-vars
    install (Vue, router = {}, instance) {
        if (this.installed) {
            return;
        }
        this.installed = true;

        if (!instance) {
            // eslint-disable-next-line no-console
            console.error('You have to install axios');
            return;
        }

        Vue.axios = instance;

        Object.defineProperties(Vue.prototype, {
            axios: {
                get: function get () {
                    return instance;
                }
            },
            $http: {
                get: function get () {
                    return instance;
                }
            }
        });
    }
};

function isResponseHeaderToken (response) {
    let headers = response.headers;
    let isToken = headers.hasOwnProperty('x-access-token');
    let token = isToken ? headers['x-access-token'] : '';
    if (isToken && token) {
        store.commit('SET_TOKEN', token);
        Vue.ls.set(ACCESS_TOKEN, token, 7 * 24 * 60 * 60 * 1000);
    }
}
export {
    VueAxios,
    isResponseHeaderToken
};
