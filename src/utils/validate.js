/**
 * 邮箱
 * @param {*} s
 */
export function isEmail (s) {
  return /^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s);
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile (s) {
  return /^1[0-9]{10}$/.test(s);
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone (s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s);
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL (s) {
  return /^http[s]?:\/\/.*/.test(s);
}
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal (path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}
export const validateNumber = (rule, value, callback) => {
  let reg = /(^[0-9]*\.([0-9]{1}\d*)$)|(^[0-9]*$)/;
  if (value && !reg.test(value)) {
    callback(new Error('请输入整数或小数'));
  }
  callback();
};

export const validateDay = (rule, value, callback) => {
  let reg = /^([0-9]{1,2}|100)$/;
  if (value && !reg.test(value)) {
    callback(new Error('请输入0~100的正整数'));
  } else {
    callback();
  }
};
export const validateDay100 = (rule, value, callback) => {
  let reg = /^([1-9]|[1-9]\d|100)$/;
  if (value && !reg.test(value)) {
    callback(new Error('请输入1~100的正整数'));
  } else {
    callback();
  }
};
export const validateNumber20 = (rule, value, callback) => {
  let reg = /^([1-9]{1,2}|20|10)$/;
  if ((value && !reg.test(value)) || Number(value) > 20) {
    callback(new Error('请输入1~20的正整数'));
  } else {
    callback();
  }
};
