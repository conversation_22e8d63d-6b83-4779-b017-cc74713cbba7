import store from "@/store";
import { isURL } from "@/utils/validate";
import moment from "moment";
import { USER_AUTH } from "@/store/mutation-types";
export function timeFix() {
  const time = new Date();
  const hour = time.getHours();
  return hour < 9
    ? "早上好"
    : hour <= 11
    ? "上午好"
    : hour <= 13
    ? "中午好"
    : hour < 20
    ? "下午好"
    : "晚上好";
}

export function welcome() {
  const arr = [
    "休息一会儿吧",
    "准备吃什么呢?",
    "要不要打一把 DOTA",
    "我猜你可能累了",
  ];
  let index = Math.floor(Math.random() * arr.length);
  return arr[index];
}

/**
 * 触发 window.resize
 */
export function triggerWindowResizeEvent() {
  let event = document.createEvent("HTMLEvents");
  event.initEvent("resize", true, true);
  event.eventType = "message";
  window.dispatchEvent(event);
}

/**
 * 过滤对象中为空的属性
 * @param obj
 * @returns {*}
 */
export function filterObj(obj) {
  if (!(typeof obj == "object")) {
    return;
  }

  for (let key in obj) {
    if (
      obj.hasOwnProperty(key) &&
      (obj[key] == null || obj[key] == undefined || obj[key] === "")
    ) {
      delete obj[key];
    }
  }
  return obj;
}

/**
 * 时间格式化
 * @param value
 * @param fmt
 * @returns {*}
 */
export function formatDate(value, fmt = "YYYY/MM/DD") {
  if (value) {
    return moment(value).format(fmt);
  }
  return null;
}
export function getFirstMenu(children) {
  children.forEach((item) => {
    if (
      item.component.indexOf("layouts") === -1 &&
      !store.state.user.firstMenu
    ) {
      store.commit("SET_FirstMenu", item);
    } else if (
      item.children &&
      item.children.length > 0 &&
      !store.state.user.firstMenu
    ) {
      getFirstMenu(item.children);
    }
  });
}
export function dynamicMenu(data) {
  getFirstMenu(data);
}
// 生成首页路由
export function generateIndexRouter(data) {
  dynamicMenu(data);
  let firstMenu = store.state.user.firstMenu;
  let indexRouter = [
    {
      path: "/",
      name: firstMenu.name,
      component: (resolve) =>
        require(["@/components/layouts/TabLayout"], resolve),
      meta: firstMenu.meta,
      redirect: firstMenu.path,
      children: [...generateChildRouters(data)],
    },
    {
      path: "*",
      redirect: "/404",
      hidden: true,
    },
  ];
  return indexRouter;
}

// 生成嵌套路由（子路由）

function generateChildRouters(data, parentLevel = 0) {
  const routers = [];
  for (let item of data) {
    let component = "";
    let isContainLay = item.component.indexOf("layouts") >= 0;
    let isIframePage =
      item.component.indexOf("IframePageView") >= 0 ||
      item.component.indexOf("IframeHkView") >= 0;
    if (isContainLay) {
      component = "components/" + item.component;
    } else {
      component = "views/" + item.component;
    }
    // eslint-disable-next-line
    let URL = (item.meta.url || "").replace(/{{([^}}]+)?}}/g, (s1, s2) =>
      eval(s2)
    ); // URL支持{{ window.xxx }}占位符变量
    if (isURL(URL)) {
      item.meta.url = URL;
    }
    let isHasCHild = item.children && item.children.length > 0;
    let isStartHealth = item.path.startsWith("/health/");
    let isStartOperation = item.path.startsWith("/operations/");
    let icon = "";
    if (isStartHealth) {
      icon = "health-" + item.path.slice("/health/".length, item.path.length);
    }
    if (isStartOperation) {
      icon =
        "operations-" +
        item.path.slice("/operations/".length, item.path.length);
    }
    // 判断level：layouts为1，有父级的IframePageView为2，无父级为1
    let level = 2;
    if (
      (isContainLay || isHasCHild) & !isIframePage &&
      item.path != "/holisticSecurity/door"
    ) {
      level = 1;
    } else if (isIframePage) {
      level = parentLevel != 0 ? 2 : 1;
    } else if (item.path == "/holisticSecurity/door") {
      console.log("item.path", item.path);
      level = 2;
    }
    let menu = {
      path: item.path,
      name: item.path,
      redirect:
        isContainLay && isHasCHild ? item.children[0].path : item.redirect,
      component: () => import("@/" + component + ".vue"),
      hidden: item.hidden,
      svgIcon: icon,
      meta: {
        title: item.meta.title,
        icon: item.meta.icon,
        url: item.meta.url,
        permissionList: item.meta.permissionList,
        keepAlive: item.meta.keepAlive,
        internalOrExternal: item.meta.internalOrExternal,
        componentName: item.meta.componentName,
        level: level,
        customHeader: item.path.indexOf("/dashboard") > -1,
      },
    };
    if (item.alwaysShow) {
      menu.alwaysShow = true;
      menu.redirect = menu.path;
    }
    if (item.alwaysShow && menu.path == "/ecological") {
      menu.redirect = null;
    }
    if (isHasCHild) {
      menu.children = [...generateChildRouters(item.children, level)];
    }
    // --update-begin----author:scott---date:20190320------for:根据后台菜单配置，判断是否路由菜单字段，动态选择是否生成路由（为了支持参数URL菜单）------
    // 判断是否生成路由
    if (item.route && item.route === "0") {
      // console.log(' 不生成路由 item.route：  '+item.route);
      // console.log(' 不生成路由 item.path：  '+item.path);
    } else {
      routers.push(menu);
    }
    // --update-end----author:scott---date:20190320------for:根据后台菜单配置，判断是否路由菜单字段，动态选择是否生成路由（为了支持参数URL菜单）------
  }
  return routers;
}

/**
 * 深度克隆对象、数组
 * @param obj 被克隆的对象
 * @return 克隆后的对象
 */
export function cloneObject(obj) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 随机生成数字
 *
 * 示例：生成长度为 12 的随机数：randomNumber(12)
 * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)
 *
 * @param1 最小值 | 长度
 * @param2 最大值
 * @return int 生成后的数字
 */
export function randomNumber() {
  // 生成 最小值 到 最大值 区间的随机数
  const random = (min, max) => {
    return Math.floor(Math.random() * (max - min + 1) + min);
  };
  if (arguments.length === 1) {
    let [length] = arguments;
    // 生成指定长度的随机数字，首位一定不是 0
    let nums = [...Array(length).keys()].map((i) =>
      i > 0 ? random(0, 9) : random(1, 9)
    );
    return parseInt(nums.join(""));
  } else if (arguments.length >= 2) {
    let [min, max] = arguments;
    return random(min, max);
  } else {
    return Number.NaN;
  }
}

/**
 * 随机生成字符串
 * @param length 字符串的长度
 * @param chats 可选字符串区间（只会生成传入的字符串中的字符）
 * @return string 生成的字符串
 */
export function randomString(length, chats) {
  if (!length) length = 1;
  if (!chats) chats = "0123456789qwertyuioplkjhgfdsazxcvbnm";
  let str = "";
  for (let i = 0; i < length; i++) {
    let num = randomNumber(0, chats.length - 1);
    str += chats[num];
  }
  return str;
}

/**
 * 随机生成uuid
 * @return string 生成的uuid
 */
export function randomUUID() {
  let chats = "0123456789abcdef";
  return randomString(32, chats);
}

/**
 * 下划线转驼峰
 * @param string
 * @returns {*}
 */
export function underLine2CamelCase(string) {
  return string.replace(/_([a-z])/g, function (all, letter) {
    return letter.toUpperCase();
  });
}

/**
 * 判断是否显示办理按钮
 * @param bpmStatus
 * @returns {*}
 */
export function showDealBtn(bpmStatus) {
  if (bpmStatus != "1" && bpmStatus != "3" && bpmStatus != "4") {
    return true;
  }
  return false;
}

/**
 * 增强CSS，可以在页面上输出全局css
 * @param css 要增强的css
 * @param id style标签的id，可以用来清除旧样式
 */
export function cssExpand(css, id) {
  let style = document.createElement("style");
  style.type = "text/css";
  style.innerHTML = `@charset "UTF-8"; ${css}`;
  // 清除旧样式
  if (id) {
    let $style = document.getElementById(id);
    if ($style != null) $style.outerHTML = "";
    style.id = id;
  }
  // 应用新样式
  document.head.appendChild(style);
}

/** 用于js增强事件，运行JS代码，可以传参 */
// options 所需参数：
//    参数名         类型            说明
//    vm             VueComponent    vue实例
//    event          Object          event对象
//    jsCode         String          待执行的js代码
//    errorMessage   String          执行出错后的提示（控制台）
export function jsExpand(options = {}) {
  // 绑定到window上的keyName
  let windowKeyName = "J_CLICK_EVENT_OPTIONS";
  if (typeof window[windowKeyName] != "object") {
    window[windowKeyName] = {};
  }

  // 随机生成JS增强的执行id，防止冲突
  let id = randomString(16, "qwertyuioplkjhgfdsazxcvbnm".toUpperCase());
  // 封装按钮点击事件
  let code = `
    (function (o_${id}) {
      try {
        (function (globalEvent, vm) {
          ${options.jsCode}
        })(o_${id}.event, o_${id}.vm)
      } catch (e) {
        o_${id}.error(e)
      }
      o_${id}.done()
    })(window['${windowKeyName}']['EVENT_${id}'])
  `;
  // 创建script标签
  const script = document.createElement("script");
  // 将需要传递的参数挂载到window对象上
  window[windowKeyName]["EVENT_" + id] = {
    vm: options.vm,
    event: options.event,
    // 当执行完成时，无论如何都会调用的回调事件
    done() {
      // 执行完后删除新增的 script 标签不会撤销执行结果（已产生的结果不会被撤销）
      script.outerHTML = "";
      delete window[windowKeyName]["EVENT_" + id];
    },
    // 当js运行出错的时候调用的事件
    error(e) {
      console.group(
        `${
          options.errorMessage || "用户自定义JS增强代码运行出错"
        }（${new Date()}）`
      );
      console.error(e);
      console.groupEnd();
    },
  };
  // 将事件挂载到document中
  script.innerHTML = code;
  document.body.appendChild(script);
}

/**
 * 根据编码校验规则code，校验传入的值是否合法
 *
 * 使用示例：
 * { validator: (rule, value, callback) => validateCheckRule('common', value, callback) }
 *
 * @param ruleCode 编码校验规则 code
 * @param value 被验证的值
 * @param callback
 */
export function validateCheckRule(ruleCode, value, callback) {
  if (ruleCode && value) {
    value = encodeURIComponent(value);
    api
      .checkRuleByCode({ ruleCode, value })
      .then((res) => {
        res["success"] ? callback() : callback(res["message"]);
      })
      .catch((err) => {
        callback(err.message || err);
      });
  } else {
    callback();
  }
}

/**
 * 如果值不存在就 push 进数组，反之不处理
 * @param array 要操作的数据
 * @param value 要添加的值
 * @param key 可空，如果比较的是对象，可能存在地址不一样但值实际上是一样的情况，可以传此字段判断对象中唯一的字段，例如 id。不传则直接比较实际值
 * @returns {boolean} 成功 push 返回 true，不处理返回 false
 */
export function pushIfNotExist(array, value, key) {
  for (let item of array) {
    if (key && item[key] === value[key]) {
      return false;
    } else if (item === value) {
      return false;
    }
  }
  array.push(value);
  return true;
}

/**
 * 可用于判断是否成功
 * @type {symbol}
 */
export const succeedSymbol = Symbol("用于判断是否成功");
/**
 * 可用于判断是否失败
 * @type {symbol}
 */
export const failedSymbol = Symbol("可用于判断是否失败");

/**
 * 使 promise 无论如何都会 resolve，除非传入的参数不是一个Promise对象或返回Promise对象的方法
 * 一般用在 Promise.all 中
 *
 * @param promise 可传Promise对象或返回Promise对象的方法
 * @returns {Promise<any>}
 */
export function alwaysResolve(promise) {
  return new Promise((resolve, reject) => {
    let p = promise;
    if (typeof promise === "function") {
      p = promise();
    }
    if (p instanceof Promise) {
      p.then((data) => {
        resolve({ type: succeedSymbol, data });
      }).catch((error) => {
        resolve({ type: failedSymbol, error });
      });
    } else {
      reject(
        new Error(
          "alwaysResolve: 传入的参数不是一个Promise对象或返回Promise对象的方法"
        )
      );
    }
  });
}

/**
 * 简单实现防抖方法
 *
 * 防抖(debounce)函数在第一次触发给定的函数时，不立即执行函数，而是给出一个期限值(delay)，比如100ms。
 * 如果100ms内再次执行函数，就重新开始计时，直到计时结束后再真正执行函数。
 * 这样做的好处是如果短时间内大量触发同一事件，只会执行一次函数。
 *
 * @param fn 要防抖的函数
 * @param delay 防抖的毫秒数
 * @returns {Function}
 */
export function simpleDebounce(fn, delay = 100) {
  let timer = null;
  return function () {
    let args = arguments;
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(that, args);
    }, delay);
  };
}

/**
 * 不用正则的方式替换所有值
 * @param text 被替换的字符串
 * @param checker  替换前的内容
 * @param replacer 替换后的内容
 * @returns {String} 替换后的字符串
 */
export function replaceAll(text, checker, replacer) {
  let lastText = text;
  text = text.replace(checker, replacer);
  if (lastText !== text) {
    return replaceAll(text, checker, replacer);
  }
  return text;
}

/**
 * 获取当前日期，格式为：yyyyMMddHHmmss
 * @returns {number}
 */
export function getTodayBegin() {
  var date = new Date();
  var month = date.getMonth() + 1;
  var strDate = date.getDate();
  var hours = date.getHours();
  var minutes = date.getMinutes();
  var seconds = date.getSeconds();

  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  if (hours >= 0 && hours <= 9) {
    hours = "0" + hours;
  }
  if (minutes >= 0 && minutes <= 9) {
    minutes = "0" + minutes;
  }
  if (seconds >= 0 && seconds <= 9) {
    seconds = "0" + seconds;
  }
  var currentdate =
    date.getFullYear() + month + strDate + hours + minutes + seconds;
  return currentdate;
}

/**
 * 获取事件冒泡路径，兼容 IE11，Edge，Chrome，Firefox，Safari
 * 目前使用的地方：EditableTable Span模式
 */
export function getEventPath(event) {
  let target = event.target;
  let path = (event.composedPath && event.composedPath()) || event.path;

  if (path != null) {
    return path.indexOf(window) < 0 ? path.concat(window) : path;
  }

  if (target === window) {
    return [window];
  }

  let getParents = (node, memo) => {
    memo = memo || [];
    const parentNode = node.parentNode;

    if (!parentNode) {
      return memo;
    } else {
      return getParents(parentNode, memo.concat(parentNode));
    }
  };
  return [target].concat(getParents(target), window);
}

/**
 * 根据组件名获取父级
 * @param vm
 * @param name
 * @returns {Vue | null|null|Vue}
 */
export function getVmParentByName(vm, name) {
  let parent = vm.$parent;
  if (parent && parent.$options) {
    if (parent.$options.name === name) {
      return parent;
    } else {
      let res = getVmParentByName(parent, name);
      if (res) {
        return res;
      }
    }
  }
  return null;
}

/**
 * 使一个值永远不会为（null | undefined）
 *
 * @param value 要处理的值
 * @param def 默认值，如果value为（null | undefined）则返回的默认值，可不传，默认为''
 */
export function neverNull(value, def) {
  return value == null ? neverNull(def, "") : value;
}

// 映射数据
export function mappingData(source, target) {
  var obj = {};
  for (var key in target) {
    typeof source[key] !== "undefined" && (obj[key] = source[key]);
  }
  return Object.assign({}, target, obj);
}

// base64下载文件
export function downloadFile(result_data) {
  if (!result_data.strBase64) {
    return;
  }
  const base64ToBlob = function (data, type) {
    let arr = data.split(",");
    // arr[0] = arr[0].replace(/\//g,'');
    // arr[0] = arr[0].replace(/\n/g,"");
    let bstr = arr.length == 2 ? window.atob(arr[1]) : window.atob(arr[0]);
    let l = bstr.length;
    let u8Arr = new Uint8Array(l);
    while (l--) {
      u8Arr[l] = bstr.charCodeAt(l);
    }
    return new Blob([u8Arr], {
      type: type,
    });
  };
  // res   就是后台返回的base64的 文件流
  let URL = base64ToBlob(result_data.strBase64);
  var reader = new FileReader();
  reader.readAsDataURL(URL);
  reader.onload = function (e) {
    // 兼容IE
    if (window.navigator.msSaveOrOpenBlob) {
      var bstr = atob(e.target.result.split(",")[1]);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      var blob = new Blob([u8arr]);
      window.navigator.msSaveOrOpenBlob(blob, result_data.fileName);
    } else {
      // 转换完成，创建一个a标签用于下载
      const a = document.createElement("a");
      a.download = result_data.fileName; // 这里写你的文件名
      a.href = e.target.result;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };
}

// 判断菜单权限
export function hasMenu(menu) {
  if (!menu) {
    return false;
  }
  let menuList = sessionStorage.getItem("menuList");
  menuList = menuList ? JSON.parse(menuList) : [];
  let items = menuList.filter((item) => item.path == menu);
  return items.length == 1;
}

// 判断有无菜单
export function hasMenuByUrl(menu) {
  if (!menu) {
    return false;
  }
  let menuList = sessionStorage.getItem("ALL_PERMISSION_MENU");
  menuList = menuList ? JSON.parse(menuList) : [];
  return menuList.find((item) => item == menu);
}

// 设置表格列宽
export function getWidth(title) {
  if (!title) {
    return null;
  }
  return (title.length / 4) * 56 + 45;
}

// 计算表格高度
export function getMaxHeight(ids) {
  if (ids instanceof Array) {
    let clientHeight = document.body.clientHeight;
    if (!clientHeight) {
      return 457;
    }
    let height = 0;
    ids.forEach((id) => {
      let box = document.getElementById(id);
      if (box) {
        let boxH = box.offsetHeight || box.clientHeight;
        if (boxH) {
          height += Number(boxH);
        }
      }
    });
    return Number(clientHeight - 56 - 40 - 24 - 36.5 - height);
  }
  return 457;
}

/*
  计算表格高度
  parentId：表格所在DOM
  ids：查询区、按钮区。。。DOM
 */
// export function getTableHeight(parentId, ids) {
//     if (!parentId) {
//         return 457;
//     }
//     let parentBox = document.getElementById(parentId);
//     if (!parentBox || !(parentBox.offsetHeight || parentBox.clientHeight)) {
//         return 457;
//     }
//     const box_height = (parentBox.offsetHeight || parentBox.clientHeight);
//     let height = 0;
//     ids.forEach(id => {
//         let box = document.getElementById(id);
//         if (box && (box.offsetHeight || box.clientHeight)) {
//             let boxH = box.offsetHeight || box.clientHeight;
//             height += Number(boxH);
//         }
//     })
//     return Number(box_height - height - 12 - 42.5);
// }
export function getTableHeight(elDom) {
  let fixHeight = window.innerHeight - 224;
  let dom = elDom.getElementsByClassName("solar-eye-search-model");
  let isList = dom && dom.length > 0;
  return dom ? fixHeight - (isList ? dom[0].offsetHeight : 0) : fixHeight;
}

/**
 * 使数字每三位以逗号分开
 */
export function formatNum(value) {
  var b = parseInt(n).toString();
  var len = b.length;
  if (len <= 3) {
    return b;
  }
  var r = len % 3;
  // b.slice(r,len).match(/\d{3}/g).join(",") 每三位数加一个逗号。
  return r > 0
    ? b.slice(0, r) + "," + b.slice(r, len).match(/\d{3}/g).join(",")
    : b.slice(r, len).match(/\d{3}/g).join(",");
}
// 对象数组去重
export function arrUnique(arr, key) {
  let returnArr = [];
  let obj = {};
  returnArr =
    arr &&
    arr.length > 0 &&
    arr.reduce((cur, next) => {
      // eslint-disable-next-line no-unused-expressions
      obj[next[key]] ? "" : (obj[next[key]] = true && cur.push(next));
      return cur;
    }, []);
  return returnArr;
}
// 获取url参数
export function getQueryString(name) {
  var queryUrl =
    location.href.indexOf("?") > -1
      ? location.href.split("?")[1]
      : location.search;
  var params = {};
  if (queryUrl) {
    var strs = queryUrl.split("&");
    var item;
    for (var i = 0; i < strs.length; i++) {
      item = strs[i].split("=");
      params[item[0]] = item[1];
    }
  } else {
    // console.error('Invalid query condition, check the url whether contains ? or not');
    return;
  }
  return name ? params[name] : params;
}
export function getThirdAuth() {
  if (sessionStorage.getItem("LOGIN_USER_BUTTON_AUTH")) {
    return true;
  } else {
    return false;
  }
}

// 获取url参数
// @param {String} paraName url参数名称
// @return {String}
export function getUrlParam(paraName) {
  let url = document.location.toString();
  let arrObj = url.split("?");

  if (arrObj.length > 1) {
    let arrPara = arrObj[1].split("&");
    let arr;

    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split("=");

      if (arr != null && arr[0] == paraName) {
        return arr[1];
      }
    }
    return "";
  } else {
    return "";
  }
}
export function getNewListByAuth(List) {
  let authList = JSON.parse(sessionStorage.getItem(USER_AUTH) || "[]");
  let arr = [];
  authList.forEach((element) => {
    arr.push(element.action);
  });
  return List.filter((item) => {
    return arr.indexOf(item.auth) > -1 || !item.auth;
  });
}

export function generateUuid() {
  var s = [];
  var random = Math.random();
  var hexDigits = random.toString().split("0.")[1];
  for (var i = 0; i < 32; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23];
  var uuid = s.join("");
  return uuid;
}

/**
 * 将树结构数据扁平化
 * @param {Array} arr - 树结构数组
 * @param {String} key - 子节点的键名，默认为 "children"
 * @returns {Array} - 扁平化后的数组
 */
export function flatten(arr, key = "children") {
  return [].concat(...arr.map((v) => (v[key] ? [v, ...flatten(v[key])] : v)));
}
/**
 * 处理路由切换
 * @param {Object} that - Vue实例
 * @param {Object} newRoute - 新的路由对象
 * @param {Object} old - 旧的路由对象
 * @param {Function} callback - 路由切换后的回调函数
 */
export function routeChange(that, newRoute, old, callback) {
  that.rootPath = newRoute.path;
  that.rootName = newRoute.meta.title;
  that.oldPath = old ? old.path : "";
  const children = that.addRouters[0].children || [];
  const {
    fullPath,
    meta: { level },
  } = newRoute;
  if (level == 1) {
    // 筛选出对应子路由 一级路由'/health'
    const findItem = children.find((item) => item.path == fullPath) || {};
    that.secondRouters = !findItem.alwaysShow ? findItem.children || [] : [];
  } else {
    const names = fullPath.split("/");
    //  将所有的路由偏平
    const allRouters = flatten(children);
    // 筛选出对应子路由 二级路由'/health/safe'
    const findItem =
      allRouters.find((item) => item.path == "/" + names[names.length - 2]) ||
      {};
    that.secondRouters = findItem.children || [];
  }
  requestAnimationFrame(() => {
    that.$nextTick(() => {
      callback?.();
    });
  });
}

/**
 * 处理设备状态总数、在线、离线公共方法
 * @param {Array} sensorStatus 接口返回数据
 * @param {Array} deviceConfigs 需要赋值的列表，可以传多个
 */
export function processSensorStatus(sensorStatus, deviceConfigs = []) {
  deviceConfigs.forEach(({ type, list }) => {
    const item = sensorStatus.find((s) => s.deviceType === type);
    const online = item?.onlineNum ?? 0;
    const offline = item?.offlineNum ?? 0;
    const total = online + offline;
    list.forEach((i) => {
      i.num = item?.[i.key] ?? total;
    });
  });
}


/**
 * 设备类型常量枚举
 * @readonly
 * @enum {string}
 * @property {string} CAMERA 摄像头 54
 * @property {string} CAMERA_SUBTYPE 摄像头子类-周界 43
 * @property {string} SENSOR 传感器 53
 * @property {string} SMART_CONTROL 智能门禁 61
 * @property {string} FIRE_SYSTEM 消防系统 62
 * @property {string} WELL_WATER 水浸 63
 * @property {string} AIR_CONDITIONING 空调 64
 * @property {string} ELECTRONIC_FENCE 电子围栏 101
 * @property {String} TEMPERATURE_HUMIDITY 温湿度 100
 */
export const DEVICE_TYPE_ENUM = Object.freeze({
  SENSOR: '53',
  CAMERA: '54',
  SMART_CONTROL: '61',
  FIRE_SYSTEM: '62',
  WELL_WATER: '63',
  AIR_CONDITIONING: '64',
  ELECTRONIC_FENCE: '101',
  TEMPERATURE_HUMIDITY: '100'
});

/**
 * 设备类型常量枚举
 * @readonly
 * @enum {string}
 * @property {string} PHOTOVOLTAIC_AREA 摄像头子类-光伏 50
 * @property {string} CAMERA_SUBTYPE 摄像头子类-周界 51
 * @property {string} ELECTRONIC_FENCE_SUBTYPE 电子围栏子类-防区 46
 * @property {string} WATER_INFLUX 水浸 47
 * @property {string} WATER_POOL 水池 48
 * @property {string} TEMPERATURE_HUMIDITY_SUBTYPE 温湿度子类 50
 * @property {string} FIRE_SUBTYPE 消防子类
 */
export const DEVICE_SUBTYPE_ENUM = Object.freeze({
  CAMERA_FIRE_SUBTYPE: 50,
  CAMERA_SUBTYPE: '51',
  ELECTRONIC_FENCE_SUBTYPE: '46',
  WATER_INFLUX: '47',
  WATER_POOL: '48',
  TEMPERATURE_HUMIDITY_SUBTYPE: '49',
  LIQUID_FIRE: '39',
  GAS_FIRE: '40',
  FIRE_HOST: '41',
});

