/*
* 可自由拖拽窗口
* el:需要绑定拖拽事件的元素dom
* handleId:拖拽把手的dom id
* 需要元素固定定位或者绝对定位
* 统一使用top,left 暂不做transform自动转换
* 注意 position,left,top使用内联样式
* */

export const setElementDraggable = (el,handleId,updatePosition) =>{
    // 只支持绝对定位 或固定定位
    let {style} = el;
    let startTime,endTime
    // if(position != 'fixed' || position!= 'absolute'){
    //     console.error('只支持支绝对定位和固定定位的元素')
    //     return
    // }
   let handleElement = document.getElementById(handleId);
    if(!handleElement){
        console.warn('找不到把手元素,默认将窗口元素当做把手')
        handleElement = el
    }

    //鼠标滑过把手元素时 变换cursor
    handleElement.addEventListener('mouseover',e=>{
        handleElement.style.cursor = 'move'
    })

    // 鼠标按下开始监听
    handleElement.addEventListener('mousedown',e=>{
        startTime = new Date().getTime();
        handleElement.addEventListener('mousemove',mouseMove,false)
        handleElement.addEventListener('mouseup',mouseUp,false)
        window.addEventListener('mouseout',mouseUp,false) // 鼠标移出窗口边界去除监听
        let originMouseX, originMouseY, moveX, moveY; //鼠标xy坐标，鼠标移动多少
        originMouseX = e.clientX;
        originMouseY = e.clientY;

        function mouseMove(event){
            el.setAttribute('dragFlag',true)
            moveX = event.clientX - originMouseX; ///移动
            moveY = event.clientY - originMouseY;
            originMouseX = event.clientX;
            originMouseY = event.clientY;

            let maxX = document.documentElement.clientWidth - el.clientWidth
            let maxY = document.documentElement.clientHeight - el.clientHeight
            let minY = 64

            let diffX = (Number.parseInt(style.left) + moveX)
            let diffY = (Number.parseInt(style.top) + moveY)

            if(style.left.includes('%')){
                diffX = document.documentElement.clientWidth * Number.parseInt(style.left) * .01 + moveX
            }
            if(style.top.includes('%')){
                diffY = document.documentElement.clientHeight * Number.parseInt(style.top) * .01 + moveY
            }

            // 元素不要超出边界
            if(diffX < 0 ){
                diffX = 0
            }
            if(diffX > maxX ){
                diffX = maxX
            }
            if(diffY < 0 ){
                diffY = 0
            }
            if(diffY > maxY ){
                diffY = maxY
            }
            if(diffY < minY ){
                diffY = minY
            }

            style.top = diffY + "px"
            style.left = diffX + "px"
            updatePosition({
                top:diffY + "px",
                left: diffX + "px"
            })

            // 防止选取文字
            event.preventDefault();
            handleElement.onselectstart = function() {
                return false;
            }
        }
        function mouseUp(event) {
            endTime = new Date().getTime();
            handleElement.removeEventListener('mousemove', mouseMove, false)
            handleElement.removeEventListener('mouseup', mouseUp, false)
            window.removeEventListener('mouseout',mouseUp,false)
            if(endTime - startTime > 200){
                el.setAttribute('dragFlag',true)
            }else{
                el.setAttribute('dragFlag',false)
            }
        } // mouseUp end
    },false)
}