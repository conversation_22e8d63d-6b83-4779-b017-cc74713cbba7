/*
  base64Code：图片base64编码
 */
export async function addWaterMarker (base64Code) {
  if (!base64Code) {
    return null;
  }
  // 根据base64Code生成原图
  const base_img = await getBaseImg(base64Code);
  const water_marker_img = await getWaterMarkerImg(base_img);

  let canvas = document.createElement('canvas');
  canvas.width = base_img.width;
  canvas.height = base_img.height;
  let context = canvas.getContext('2d');
  // 绘制
  context.drawImage(base_img, 0, 0, base_img.width, base_img.height);
  context.drawImage(water_marker_img, 0, 0);

  const result_img = new Image();
  const result_img_src = canvas.toDataURL('image/png');
  result_img.onload = function () {
    return result_img_src;
  };
  result_img.src = result_img_src;
  return result_img.onload();
}
/*
  设置水印图片
  base_img：原图
 */
async function getWaterMarkerImg (base_img) {
  let fill_img = await getFillImg();
  if (!fill_img) {
    return null;
  }
  let canvas = document.createElement('canvas'); // 准备空画布
  canvas.id = 'waterMarkerCanvas';
  canvas.width = base_img.width;
  canvas.height = base_img.height;
  canvas.style.visibility = 'hidden';
  document.body.appendChild(canvas);
  let canvas_box = document.getElementById('waterMarkerCanvas');
  if (!canvas_box) {
    return null;
  }
  let ctx = canvas_box.getContext('2d');
  ctx.globalAlpha = 0.2;
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  let pattern = ctx.createPattern(fill_img, 'repeat');
  ctx.rect(0, 0, canvas.width, canvas.height);
  ctx.fillStyle = pattern;
  ctx.fill();
  let water_marker_img = new Image();
  water_marker_img.onload = function () {
    canvas_box.remove();
    return water_marker_img;
  };
  water_marker_img.src = canvas.toDataURL('image/png');
  return water_marker_img.onload();
}

/*
  根据base64Code生成原图
  base64Code：图片base64编码
 */
function getBaseImg (base64Code) {
  if (base64Code.indexOf(';base64,') == -1) {
    base64Code = 'data:image/png;base64,' + base64Code;
  }
  let base_img = new Image();
  base_img.onload = function () {
    return base_img;
  };
  base_img.src = base64Code;
  return base_img.onload();
}

/*
  获取水印图片的填充图片
 */
function getFillImg () {
  let canvas = document.createElement('canvas'); // 准备空画布
  canvas.id = 'fillImg';
  canvas.width = 300;
  canvas.height = 150;
  canvas.style.visibility = 'hidden';
  document.body.appendChild(canvas);
  let box = document.getElementById('fillImg');
  if (!box) {
    return null;
  }
  let ctx = box.getContext('2d');
  ctx.rotate(-30 * Math.PI / 180);
  ctx.font = '14px Verdana'; // canvas字体
  // let gradient = ctx.createLinearGradient(0, 0, 120, 0);
  // gradient.addColorStop("0", "#000");
  // gradient.addColorStop("0.5", "#000");
  // gradient.addColorStop("1.0", "#000");
  ctx.fillStyle = '#000';
  ctx.fillText('阳光电源', 0, 150); // 水印文字及位置
  let fill_img = new Image();
  fill_img.onload = function () {
    box.remove();
    return fill_img;
  };
  fill_img.src = canvas.toDataURL('image/png');
  return fill_img.onload();
}
/**  水印添加方法  */

let setWatermark = (str1, str2) => {
  let id = '1.23452384164.123412415';

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
  }

  let can = document.createElement('canvas');
  // 设置canvas画布大小
  // 控制水印的行数与列数  这个大小指的是单个水印大小
  can.width = 300;
  can.height = 180;

  let cans = can.getContext('2d');
  cans.rotate(-30 * Math.PI / 180); // 水印旋转角度
  cans.font = '14px Vedana';
  cans.fillStyle = '#ccc';
  cans.textAlign = 'center';
  cans.textBaseline = 'Middle';
  cans.fillText(str1, can.width / 2, can.height); // 水印在画布的位置x，y轴
  cans.fillText(str2, can.width / 2, can.height + 22);

  let div = document.createElement('div');
  div.id = id;
  div.style.pointerEvents = 'none';
  div.style.top = '10px';
  div.style.left = '0px';
  div.style.opacity = '0.2';
  div.style.position = 'fixed';
  div.style.zIndex = '100000';
  div.style.width = document.documentElement.clientWidth + 'px';
  div.style.height = document.documentElement.clientHeight + 'px';
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat';
  document.body.appendChild(div);
  return id;
};

// 添加水印方法
export const setWaterMark = (str1, str2) => {
  let id = setWatermark(str1, str2);
  if (document.getElementById(id) === null) {
    id = setWatermark(str1, str2);
  }
};

// 移除水印方法
export const removeWatermark = () => {
  let id = '1.23452384164.123412415';
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
  }
};
