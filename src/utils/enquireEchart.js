import * as echarts from "echarts/core"; // echart 按需加载
import {
  <PERSON>set<PERSON>omponent,
  TitleComponent,
  Tooltip<PERSON>omponent,
  Tool<PERSON><PERSON>omponent,
  LegendComponent,
  Grid<PERSON>omponent,
  DataZoomComponent,
  Mark<PERSON>ointComponent,
  Mark<PERSON><PERSON><PERSON>omponent,
  Mark<PERSON><PERSON><PERSON>omponent,
  PolarComponent,
  Geo<PERSON>omponent,
} from "echarts/components";

import {
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON>hart,
  <PERSON><PERSON>hart,
  Treemap<PERSON>hart,
  <PERSON>ctorialBar<PERSON>hart,
  GaugeChart,
  RadarChart,
  SankeyChart
} from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  PictorialBar<PERSON>hart,
  DatasetComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  GridComponent,
  DataZoom<PERSON>omponent,
  <PERSON><PERSON>oint<PERSON>omponent,
  Mark<PERSON>rea<PERSON>omponent,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ma<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Polar<PERSON>omponent,
  <PERSON>eo<PERSON>om<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
]);

export default echarts;
