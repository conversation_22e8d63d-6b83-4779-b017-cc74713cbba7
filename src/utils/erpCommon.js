import { USER_AUTH } from '@/store/mutation-types';
// 输入框trim
export function $trim (event) {
  return event.target.value.trim();
}

// 获取数据字典名称label
export function getLabel (code, items) {
  code = ((code == null || code == undefined) ? '' : code.toString());
  if (!code) {
    return '--';
  }
  if (items instanceof Array) {
    let label = '';
    for (let item of items) {
      if (code == item.codeValue || code == item.value) {
        label = (item.dispName || item.label);
        break;
      }
    }
    return ((label || label == '0') ? label : '--');
  } else {
    return (code || '--');
  }
}

// 判断是否为空，包括undefined、null、''、[]、{}
export function isEmpty (params) {
  if (params === undefined) {
    return true;
  } else if (params === null) {
    return true;
  } else if (Object.prototype.toString.call(params) === '[object String]' && params === '') {
    return true;
  } else if (Object.prototype.toString.call(params) === '[object Array]' && params.length == 0) {
    return true;
  } else if (Object.prototype.toString.call(params) === '[object Object]' && Object.keys(params).length == 0) {
    return true;
  }
  return false;
}

// 根据状态名去判断颜色
export function getColorByName (statusLabel) {
  switch (statusLabel) {
    case '未提交': case '未分解': case '待分解':
      return 'noSubmit';
    case '未开始': case '已退回':
      return 'waitBegin';
    case '进行中': case '处理中':
      return 'processing';
    case '待指派': case '待审核': case '审核中': case '未下发' : case '退回待分解':
      return 'undo';
    case '验收中':
      return 'check';
    case '已完成': case '已提交': case '已生效': case '已分解':
      return 'finish';
    case '已终止': case '已结束':
      return 'stop';
    case '启用中':
      return 'processing';
    case '已禁用':
      return 'stop';
  }
}

// base64下载文件
export function $downloadFile (result_data, message) {
  if (!result_data.fileBase64Code) {
    return;
  }
  const base64ToBlob = function (data) {
    let arr = data.split(',');
    let bstr = (arr.length == 2 ? window.atob(arr[1]) : window.atob(arr[0]));
    let l = bstr.length;
    let u8Arr = new Uint8Array(l);
    while (l--) {
      u8Arr[l] = bstr.charCodeAt(l);
    }
    return new Blob([u8Arr]);
  };
    // res.data   就是后台返回的base64的 文件流
  let bloburl = base64ToBlob(result_data.fileBase64Code, result_data.fileType);
  // 兼容IE
  if (window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(bloburl, result_data.fileName);
  } else {
    let downloadElement = document.createElement('a');
    downloadElement.href = URL.createObjectURL(bloburl); // 防止bloburl太大超过a标签的href最大值
    downloadElement.download = result_data.fileName; // 下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); // 点击下载
    document.body.removeChild(downloadElement); // 下载完成移除元素
    window.URL.revokeObjectURL(downloadElement.href); // 释放掉blob对象
  }
  setTimeout(() => {
    this.$message.destroy();
    this.$message.success(!message ? '下载成功' : message);
  }, 200);
}

// select下拉框模糊搜索公共方法
export function filterOption (input, option) {
  return (
      option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
  );
}

// 按钮权限控制
export function showHandle (perms) {
  perms = (perms == null ? '' : perms.toString());
  if (!perms) {
    return false;
  }
  const all = JSON.parse(sessionStorage.getItem(USER_AUTH) || '[]');
  const arr = perms.split(',');
  let show = false;
  for (let item of all) {
    if (arr.includes(item.action)) {
      show = true;
      break;
    }
  }
  return show;
}

// 表格数据格式化format
export function tabFormatter ({ cellValue }) {
  cellValue = ((cellValue == null || cellValue == undefined) ? '' : cellValue.toString());
  if (cellValue) {
    return cellValue;
  } else {
    return '--';
  }
}