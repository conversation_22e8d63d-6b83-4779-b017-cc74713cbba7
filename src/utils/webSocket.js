import { ACCESS_TOKEN } from "../store/mutation-types";
import Vue from "vue";
let domain = process.env.VUE_APP_API_DOMAIN;
let domainWithoutScheme = domain.split('//')[1];
// let URL = '10.5.7.204:8088';
function initWsbSocket() {
  // console.log(Vue)
  if (!Vue.ls.get(ACCESS_TOKEN)) {
    return undefined;
  }
  if ("WebSocket" in window) {
    // 检测当前protocol是否https
    const protocol = /^https/.test(domain) ? "wss" : "ws";
    // console.log('您的浏览器支持 WebSocket!');
    let ws = new WebSocket(
      `${protocol}://${domainWithoutScheme}/solareyecare/care-cloud/api/v1/ws?X-Access-Token=` +
        Vue.ls.get(ACCESS_TOKEN)
    );
    ws.onclose = function () {
      // 关闭 websocket
      // console.log('连接已关闭...');
      setTimeout(() => {
        initWsbSocket();
      }, 2000);
    };
    return ws;
  } else {
    // 浏览器不支持 WebSocket
    console.log("您的浏览器不支持 WebSocket!");
    return undefined;
  }
}

export default {
  ws: { onmessage: "" },
  URL: domainWithoutScheme + '/solareyecare',
  doClose: function () {
    if (Object.keys(this.ws).length) {
      //this.ws.close();
    }
  },
  doInit: function () {
    this.ws = initWsbSocket();
  },
};
