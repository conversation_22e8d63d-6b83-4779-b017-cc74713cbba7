// import {erpGetMap} from '@/api/index.js'

/*
* 获取字典选项
* @params dictName:String 字典值 多个时以逗号隔开
* return Object:{dictName:[{label:String,value:String|Number}]}
* */

const getDicts = async (params) => {
    let res = await erpGetMap({
        dictName: params,
    })

    let obj = JSON.parse(JSON.stringify(res.result_data))
    Object.keys(res.result_data).map(item => {
        obj[item] = obj[item].map(el => {
            return {
                value: el.dataValue,
                label: el.dataLable
            }
        })
    })
    return obj
}

/*
* 获取字典选项文本
* @params value:String|Number 字典值
* @params option:Array [{label:String,value:String|Number}] 字典选项
* return String
* */

const getDictValue = (value, options) => {
    if (value === null || value === undefined) return value
    if(!Array.isArray(options)){
        return undefined
    }
    let el = options.find(item => item.value == value)
    return el ? el.label : ''
}

/*
* 获取字典值选项文本集合
* @params value:String|Array 字典值数组或 字典值，拼接的字符串
* @params option:Array [{label:String,value:String|Number}] 字典选项
* @params splitSymbol:String 返回结果分隔符
* return String
* */

const getDictValueList = (val,options,splitSymbol = ',') => {
    let value = val
    if(typeof val == "string"){
        value = val.split(',')
    }
    if(!Array.isArray(options)){
        return undefined
    }
    let flag = value.some(el => options.every(item => item.value!=el))
    if(flag){
        return ''
    }
    return value.map(item=> options.find(el => el.value == item).label).join(splitSymbol)
}

export default {
    getDictValue,
    getDictValueList,
    getDicts
}