import Vue from 'vue'
import * as Antd from 'ant-design-vue';
import App from './App.vue'
import store from './store';
import Storage from 'vue-ls';
import SSO from '@/utils/sso.js';
import {
    $trim,
    getLabel,
    isEmpty,
    getColorByName,
    $downloadFile,
    filterOption,
    showHandle,
    tabFormatter
} from '@/utils/erpCommon.js';
import { VueAxios } from '@/utils/request'
import '@/permission'; // permission control
import 'xe-utils';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
Vue.config.productionTip = false
import util from '@/utils/index.js'
import dict from '@/utils/dict.js'
import '@/assets/icons'
import hasPermission from '@/utils/hasPermission';
import 'leaflet/dist/leaflet.css'
// 引入Leaflet对象 挂载到Vue上，便于全局使用，也可以单独页面中单独引用
import * as L from 'leaflet'
import 'leaflet.pm'
import 'leaflet.pm/dist/leaflet.pm.css'
import '../public/js/leaflet.mapCorrection.js'
import 'leaflet-polylinedecorator'
import router from './router';
import SolarComponents from '@/components/com'
import ws from './utils/webSocket.js';
// 将方法挂载到Vue实例上
Vue.prototype.$util= util;
Vue.prototype.$dict= dict;
Vue.prototype.$trim = $trim;
Vue.prototype.getLabel = getLabel;
Vue.prototype.$isEmpty = isEmpty;
Vue.prototype.$getColorByName = getColorByName;
Vue.prototype.$ws = ws;
Vue.prototype.$downloadFile = $downloadFile;
Vue.prototype.$filterOption = filterOption;
Vue.prototype.showHandle = showHandle;
Vue.prototype.$tabFormatter = tabFormatter;
Vue.use(VueAxios, router);
Vue.use(Antd);
Vue.use(hasPermission);
Vue.use(Storage, {
    namespace: 'pro__', // key prefix
    name: 'ls', // name variable Vue.[ls] or this.[$ls],
    storage: 'local' // storage name session, local, memory
});
Vue.use(VXETable)
Vue.use(SolarComponents)
// 初始化
SSO.init(() => {
    main();
});

function main () {
    new Vue({
        router,
        store,
        render: h => h(App)
    }).$mount('#app')
}
