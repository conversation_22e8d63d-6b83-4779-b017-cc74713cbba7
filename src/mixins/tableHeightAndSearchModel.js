import { getTableHeight } from '@/utils/util.js';
import debounce from 'lodash/debounce';
import store from "@/store";
export default {
  data () {
    return {
      tableHeight: 600,
      toggleSearchStatus: false,
      leftHeight: window.innerHeight - 124
    };
  },
  computed: {
    isShowMenu () {
      return store.state.user.isShowMenu;
    }
  },
  mounted () {
    // 设置表格高度
    this.$nextTick(() => {
      this.tableHeight = getTableHeight(this.$el) - this.isDomExist('right-bar') + (this.isShowMenu ? 0 : 124);
    });
    window.addEventListener('resize', this.heightResize, true);
  },
  deactivated () {
    window.removeEventListener('resize', this.heightResize, true);
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.heightResize, true);
  },
  methods: {
    heightResize: debounce(function () {
      this.tableHeight = getTableHeight(this.$el) - this.isDomExist('right-bar') + (this.isShowMenu ? 0 : 124);
      this.leftHeight = window.innerHeight - 124;
    }, 400),
    isDomExist (dom) {
      let doc = document;
      let domNode = doc.getElementsByClassName(dom);
      if (domNode && domNode.length > 0) {
        return domNode[0].children[0].clientHeight;
      }
      return 0;
    },
    setBodyMinHeight (has, height, isIndex) { // a-table 设置最小高度设配
      let doc = document;
      let tableBody = doc.getElementsByClassName('ant-table-body');
      let num = isIndex ? 1 : 0;
      if (tableBody && tableBody.length > 0) {
        tableBody[num].style.minHeight = has ? this.tableHeight - height + 'px' : 0;
      }
    },
    handleToggleSearch () { // 展开收起
      this.toggleSearchStatus = !this.toggleSearchStatus;
      this.$nextTick(() => {
        this.tableHeight = getTableHeight(this.$el) - this.isDomExist('right-bar');
      });
    }
  }
};
