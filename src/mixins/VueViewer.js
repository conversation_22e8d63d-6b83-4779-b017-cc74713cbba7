import 'viewerjs/dist/viewer.css';
import { api as viewerApi } from 'v-viewer';
import { notification } from 'ant-design-vue';
export default {
  data () {
    return {
      $viewer: null
    };
  },
  methods: {
    /*
      图片预览,少于两张不展示功能按钮
    */
    viewerImage (params) {
      let { images, index, refresh } = params;
      let _this = this;
      if (_this.$viewer && refresh) {
        _this.$viewer.destroy();
        _this.$viewer = null;
      }
      let items = _this.viewerImages = Array.isArray(images) ? JSON.parse(JSON.stringify(images)) : (images ? [images] : []);
      if (!items.length) {
        notification.warning({
          message: '系统提示',
          description: '预览失败，请检查数据',
          duration: 4
        });
        return;
      }
      let show_navbar_toolbar = items.length > 1;
      let viewer = viewerApi({
        images: items.map(item => { return item.path; }),
        options: {
          zIndex:9999999,
          title: false,
          button: true,
          rebuild: true,
          scalable: false,
          initialViewIndex: index || 0,
          navbar: show_navbar_toolbar,
          toolbar: _this.setToolbar(show_navbar_toolbar)
          // 图片链接失效后的处理逻辑，勿删
          // view(e){
          //   _this.update
          //   let image = e.detail.image;
          //   image.onerror = () => {
          //     _this.getImgPath(e.detail, images);
          //   }
          // }
        }
      });
      _this.$viewer = viewer;
    },
    /*
      图片链接失效后重新请求，勿删
    */
    // getImgPath (detail, images) {
    //   notification.warning({
    //     message: '系统提示',
    //     description: '图片链接已失效，正在重新获取...',
    //     duration: 4
    //   });
    //   let index = detail.index;
    //   images[index].path = 'https://isolarerp.oss-cn-hangzhou.aliyuncs.com/fat/9a3b6be459ce4585a59d12dbad7f863a.png?Expires=1658737398&OSSAccessKeyId=LTAI4G5LX8cpsjDKq4KWtZr9&Signature=4ViiYE4iHNjL79ER0EmJzGXv8ho%3D';
    //   this.viewerImage({ 'images': images, 'index': index, refresh: true });
    // },
    /*
      设置操作按钮
    */
    setToolbar (flag) {
      return flag ? { zoomIn: 4, zoomOut: 4, prev: 4, next: 4, rotateLeft: 4, rotateRight: 4 } : { zoomIn: 4, zoomOut: 4, rotateLeft: 4, rotateRight: 4 };
    }
  }
};
