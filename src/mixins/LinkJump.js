import { getAccessCode<PERSON>pi } from "@/api/user";
import { message } from "ant-design-vue";

export default {
  methods: {
    async goToThirdParty(redirectUrl = "https://www.isolareye.com/#/workPortal?tenant_id=1") {
      // 读取网络配置,如果配置为关不可跳转，反之可以
      const netConnectSwitch = this.$store.getters.systemConfigMap.configNetConnect?.setValue;
      if(netConnectSwitch !== "true") return;
      const res = await getAccessCodeApi({redirectUrl});
      const { success, errorDesc, code } = res.result_data || {};
      if (!success) {
        message.error(errorDesc || "系统错误，请联系管理员");
        return;
      }
      window.open(
        "https://openapi.isolareye.com/third/access-token?code=" +
          code +
          "&redirect_uri=" +
          encodeURIComponent(redirectUrl)
      );
    },
  },
};
