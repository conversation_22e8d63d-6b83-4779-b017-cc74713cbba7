// websocket消息监听、重连处理
export default {
  data () {
    return {
      path: ''
    };
  },
  created () {
    this.setWebSocketMessage();
    this.path = this.$route.path;
    window.addEventListener('websocket-close', this.resetWebSocket);
  },
  methods: {
    // 断开重连
    resetWebSocket () {
      console.log('websocket-close');
      if (this.$route.path != this.path) {
        return;
      }
      this.$ws.doInit();
      this.setWebSocketMessage();
    },
    // 消息接收
    setWebSocketMessage () {
      if (!this.$ws.ws) {
        return;
      }
      this.$ws.ws.onmessage = res => {
        this.dealWebsocketMessage(res);
      };
    }
  },
  beforeDestroy () {
    this.$ws.doClose();
    window.removeEventListener('websocket-close', this.resetWebSocket);
  }
};
