<template>
  <a-config-provider :locale="locale">
    <div id="app">
      <transition direction="fade" mode="out-in">
        <router-view/>
      </transition>
    </div>
  </a-config-provider>

</template>

<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import {message, notification} from "ant-design-vue";

moment.locale('zh-cn');
message.config({top: `64px`});
notification.config({top: '64px'});
export default {
  name: 'App',
  data() {
    return {
      locale: zhCN
    };
  },
  created() {
    //  判断主应用是否加载完成，loading.gif移除
    const ele = document.getElementById('solarEyeLoading');
    ele && ele.remove()
    // this.$ws.doInit();
  }
}

</script>

<style lang="less">
@import "assets/common.less";
@import "assets/config.less";

#app {
  width: 100%;
  height: 100%;
}
</style>
