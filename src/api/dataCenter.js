import { postAction,getAction } from '@/api/common';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
const iotUrl = process.env.VUE_APP_IOT_BASE_URL;
// 获取通信协议版本
const getManufacturersList = (params) => postAction(baseUrl + '/versionManagement/queryFactory', params);
const getDeviceModel = (params) => postAction(baseUrl + '/versionManagement/queryModel', params);
// 数据洞察
const insightToolsDeviceTypeList = (params) => postAction(baseUrl + '/insightTools/deviceTypeList', params);
const insightToolsDeviceList = (params) => postAction(baseUrl + '/insightTools/deviceList', params);
const insightToolsMorePoint = (params) => postAction(baseUrl + '/insightTools/morePoint', params);
const insightToolsGetDefaultPs = (params) => postAction(baseUrl + '/insightTools/getDefaultPs', params);
const insightToolsInsightAnalysisData = (params) => postAction(baseUrl + '/insightTools/insightAnalysisData', params);
const getMeteo = (params) => postAction(baseUrl + '/healthPowerStation/getMeteo', params);
const getModuleDataApi = (params) => getAction(baseUrl + '/data/getData', params);

// 洞察工具新
const viewTemplateList = (params) => postAction(baseUrl + '/insightTools/viewTemplate/list', params); // 洞察工具模板列表
const deleteViewTemplate = (params) => postAction(baseUrl + '/insightTools/viewTemplate/delete', params); // 洞察工具删除模板
export const getIndicatorData = (params) => postAction(baseUrl + '/insightTools/getIndexData', params); // 获取指标图表数据
export const getIndexTableApi = (params) => postAction(baseUrl + '/insightTools/getIndexTable', params); // 获取指标表格数据

export const getIndexTypeApi = (params) => postAction(baseUrl + '/insightTools/getIndexType', params); // 获取指标表格数据

export const getIndexPsListApi = (params) => postAction(baseUrl + '/app/index/psList', params); // 获取电站列表
export {
    getManufacturersList,
    getDeviceModel,
    insightToolsDeviceTypeList,
    insightToolsDeviceList,
    insightToolsMorePoint,
    insightToolsGetDefaultPs,
    insightToolsInsightAnalysisData,
    getMeteo,
    viewTemplateList,
    deleteViewTemplate,
    baseUrl,
    getModuleDataApi
};
