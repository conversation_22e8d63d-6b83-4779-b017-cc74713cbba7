import { getAction, postAction } from '@/api/common';
const url = process.env.VUE_APP_Health_BASE_URL;
// 获取电站及设备类型
const getOpenAlgorithmStationList = (params) => postAction(url + '/healthPowerStation/getOpenAlgorithmPowerStation', params);
const getAllPowerStation = (params) => postAction(url + '/healthPowerStation/getAllPowerStation', params);
const getPsTreeMenu = (params) => postAction(url + '/healthPowerStation/getPsTreeMenu', params);
const getOpenAlgorithmPowerStationByInverterTypes = (params) => postAction(url + '/healthPowerStation/getOpenAlgorithmPowerStationByInverterTypes', params);
const queryDeviceListByPsIdAndDeviceType = (params) => postAction(url + '/healthPowerStation/queryDeviceListByPsIdAndDeviceType', params);
const psDeviceTypeList = (params) => postAction(url + '/monitor/psDeviceTypeList', params);

// 组织树
const getRegionalTree = (params) => postAction(url + '/healthPowerStation/getRegionalTree', params);
const selectPsIdAndPsNameByOrgId = (params) => postAction(url + '/healthPowerStation/selectPsIdAndPsNameByOrgId', params);
// 告警事件
const getListOnlineAlarm = (params) => postAction(url + '/healthOnlineAlarm/listOnlineAlarm', params);
const alarmEventExport = (params) => postAction(url + '/healthOnlineAlarm/downloadExcel', params);
const updateHealthOnlineAlarm = (params) => postAction(url + '/healthOnlineAlarm/updateHealthOnlineAlarm', params);
const getPowerStationTopicalDay = (params) => postAction(url + '/healthTopicalDay/getPowerStationTopicalDay', params);
const selectOnlineAlarmDeviceInfo = (params) => postAction(url + '/healthOnlineAlarm/selectOnlineAlarmDeviceInfo', params);
const getWeatherByPsId = (params) => postAction(url + '/healthPsWeather/getWeatherByPsId', params);
const handleHealthOnlineAlarm = (params) => postAction(url + '/healthOnlineAlarm/handleHealthOnlineAlarm', params);
// 数据事件
const getLinkageByPsId = (params) => postAction(url + '/dataEvent/getLinkageByPsId', params);
const getAbnormalStringInfo = (params) => postAction(url + '/dataEvent/getAbnormalStringInfo', params);
const getStringBoxDeviceInfo = (params) => postAction(url + '/dataEvent/getStringBoxDeviceInfo', params);
const getInverterDeviceInfo = (params) => postAction(url + '/dataEvent/getInverterDeviceInfo', params);
// 电站管理
const searchPowerStation = (params) => postAction(url + '/healthPowerStation/listPowerStation', params);
const getPowerStationSystemCodeList = (params) => postAction(url + '/healthPowerStation/getPowerStationSystemCodeList', params);
const oneSync = (params) => postAction(url + '/healthPowerStation/syncByPsId', params);
const deletePowerStation = (params) => postAction(url + '/healthPowerStation/deletePowerStation', params);
const clearPowerStation = (params) => postAction(url + '/healthPowerStation/clearPowerStation', params);
const addAlgorithmPowerStation = (params) => postAction(url + '/healthPowerStation/addAlgorithmPowerStation', params);
const downloadOpenAlgorithmMsg = (params) => postAction(url + '/healthPowerStation/downloadOpenAlgorithmMsg', params);
const stopAlgorithm = (params) => postAction(url + '/healthPowerStation/stopAlgorithmPowerStation', params);
const getExportData = (params) => postAction(url + '/healthPowerStation/export', params);
const getDeviceTypeTree = (params) => postAction(url + '/healthPowerStation/deviceTree', params);
const getMeteoPsList = (params) => postAction(url + '/healthPowerStation/meteoPsList', params);
const selectMeteoPsInfo = (params) => postAction(url + '/healthPowerStation/selectMeteoPsInfo', params);
const getOpenAlgorithm = (params) => postAction(url + '/healthPowerStation/queryPsOpenAlgorithm', params);
const selectDeviceMaker = (params) => postAction(url + '/healthAttributeConfig/selectDeviceMaker', params);
const selectDeviceModel = (params) => postAction(url + '/healthAttributeConfig/selectDeviceModel', params);
const importPowerStation = (params) => postAction(url + '/healthPowerStationNewController/importPowerStation', params);
const addPowerStation = (params) => postAction(url + '/healthPowerStationNewController/addPowerStation', params);
const deleteSelfPowerStation = (params) => postAction(url + '/healthPowerStationNewController/deletePowerStation', params);
const deviceNodeMove = (params) => postAction(url + '/healthAttributeConfig/deviceNodeMove', params);
const addDevice = (params) => postAction(url + '/healthAttributeConfig/addDevice', params);
const deleteDevice = (params) => postAction(url + '/healthAttributeConfig/deleteDevice', params);
const selectAttributeConfigsByPid = (params) => postAction(url + '/healthAttributeConfig/selectAttributeConfigsByPid', params);
const selectConfigInfosByPid = (params) => postAction(url + '/healthAttributeConfig/selectConfigInfosByPid', params);
const realTimeUpdate = (params) => postAction(url + '/healthAttributeConfig/realTimeUpdate', params);
const updateConfigs = (params) => postAction(url + '/healthAttributeConfig/updateConfigs', params);
const exportDetail = (params) => postAction(url + '/healthPowerStationNewController/export', params);
const getPointBySn = (params) => postAction(url + '/healthPowerStationNewController/getPointBySn', params);
const exportSnAndPoint = (params) => postAction(url + '/healthPowerStationNewController/exportSnAndPoint', params);
const removeCollectorBySN = (params) => postAction(url + '/healthAttributeConfig/removeCollectorBySN', params);
const selectDeviceConfig = (params) => postAction(url + '/healthAttributeConfig/selectDeviceConfig', params);

const deleteCollectorBySN = (params) => postAction(url + '/healthAttributeConfig/deleteCollectorBySN', params);
const selectAttributeConfig = (params) => postAction(url + '/healthAttributeConfig/selectAttributeConfig', params);
const queryDeviceListByUserId = (params) => postAction(url + '/healthPowerStation/queryDeviceListByUserId', params);
const updatePsAttribute = (params) => postAction(url + '/healthAttributeConfig/updatePsAttribute', params);
const getAllRegion = (params) => getAction(url + '/healthRegion/getAllRegion', params);

// 参数配置
const listAlgorithmParamByPsId = (params) => postAction(url + '/healthAlgorithmParam/listAlgorithmParamByPsId', params);
const updateAlgorithmParamByPsId = (params) => postAction(url + '/healthAlgorithmParam/updateAlgorithmParamByPsId', params);
// 组串离散率分析
const getDispersionRateLevel = (params) => postAction(url + '/HealthDispersionRate/getDispersionRateLevel', params);
const listDispersionRate = (params) => postAction(url + '/HealthDispersionRate/listDispersionRate', params);
const downloadExcelDispersionRate = (params) => postAction(url + '/HealthDispersionRate/downloadExcel', params);
// 获取电站的设备类型及默认设备类型
const getPowerStationType = (params) => postAction(url + '/HealthDispersionRate/getPowerStationType', params);
// 设备静态指标分析
const indexAnalysisHistory = (params) => postAction(url + '/HealthDispersionRate/indexAnalysisHistory', params);
// 设备动态指标分析
const indexAnalysisDynamic = (params) => postAction(url + '/HealthDispersionRate/indexAnalysisDynamic', params);
// 数据质量分析
const listTopicalDay = (params) => postAction(url + '/healthTopicalDay/listTopicalDay', params);
const topicalDayDownloadExcel = (params) => postAction(url + '/healthTopicalDay/downloadExcel', params);
// 清洁度分析
const queryPsCleanInfo = (params) => postAction(url + '/healthClean/queryPsCleanInfo', params);
const getFuture15DaysWeatherByPsId = (params) => postAction(url + '/healthPsWeather/getFuture15DaysWeatherByPsId', params);
const getAround7DaysWeatherByPsId = (params) => postAction(url + '/healthPsWeather/getAround7DaysWeatherByPsId', params);
const downloadExcelCleanReport = (params) => postAction(url + '/healthClean/downloadExcel', params);
const getCleanReportInfo = (params) => postAction(url + '/healthClean/getReportInfo', params);
const addCleanRecord = (params) => postAction(url + '/healthClean/addCleanRecord', params);
// 数据字典
const getSystemCodeList = (params) => postAction(url + '/systemCode/getSystemCodeListWithChild', params);
const getOnlineAlarmSystemCodeList = (params) => postAction(url + '/healthOnlineAlarm/getOnlineAlarmSystemCodeList', params);

const selectInefficiencyDeviceInfo = (params) => postAction(url + '/healthInefficiencyDiagnosis/selectInefficiencyDeviceInfo', params);
// 测点管理，获取第三方测点名称
const listThirdPointFieldNames = (params) => postAction(url + '/pointManagement/listThirdPointFieldNames', params);
// 测点管理，获取第三方测点名称
const getSystemCodeListWithParentAndChild = (params) => postAction(url + '/systemCode/getSystemCodeListWithParentAndChild', params);
// 门禁控制列表
const getSecurityListApi = (params) => getAction(url + '/security/doorList', params);
// 门禁控制开关
const operateDoorApi = (params) => postAction(url + '/powerEnvironment/operate/door', params);

// 空调控制
const operateAirConditionerApi = (params) => postAction(url + '/powerEnvironment/operate/airConditioner', params);

// 三方开关控制
const thirdDeviceCtrlApi = (params) => postAction(url + '/third/device/ctrl', params);

export {
  getStringBoxDeviceInfo,
  getInverterDeviceInfo,
  handleHealthOnlineAlarm,
  getOpenAlgorithmStationList,
  getOpenAlgorithm,
  getPsTreeMenu,
  getDeviceTypeTree, // 新增 设备树
  getOpenAlgorithmPowerStationByInverterTypes,
  getAllPowerStation,
  queryDeviceListByPsIdAndDeviceType,
  getSystemCodeList,
  getListOnlineAlarm,
  getOnlineAlarmSystemCodeList,
  alarmEventExport,
  updateHealthOnlineAlarm,
  getPowerStationTopicalDay,
  selectOnlineAlarmDeviceInfo,
  getRegionalTree,
  getMeteoPsList,
  selectMeteoPsInfo,
  selectPsIdAndPsNameByOrgId,
  getLinkageByPsId,
  getAbnormalStringInfo,
  searchPowerStation,
  getPowerStationSystemCodeList,
  oneSync,
  getExportData,
  deletePowerStation,
  clearPowerStation,
  addAlgorithmPowerStation,
  downloadOpenAlgorithmMsg,
  stopAlgorithm,
  selectAttributeConfig,
  queryDeviceListByUserId,
  updatePsAttribute,
  getAllRegion,
  listAlgorithmParamByPsId,
  updateAlgorithmParamByPsId,
  getDispersionRateLevel,
  listDispersionRate,
  downloadExcelDispersionRate,
  selectInefficiencyDeviceInfo,
  listTopicalDay,
  topicalDayDownloadExcel,
  queryPsCleanInfo,
  getFuture15DaysWeatherByPsId,
  getAround7DaysWeatherByPsId,
  downloadExcelCleanReport,
  getCleanReportInfo,
  indexAnalysisHistory,
  indexAnalysisDynamic,
  getPowerStationType,
  addCleanRecord,
  getWeatherByPsId,
  psDeviceTypeList,
  listThirdPointFieldNames,
  selectDeviceMaker,
  selectDeviceModel,
  getSystemCodeListWithParentAndChild,
  importPowerStation,
  addPowerStation,
  deleteSelfPowerStation,
  deviceNodeMove,
  addDevice,
  deleteDevice,
  selectAttributeConfigsByPid,
  selectConfigInfosByPid,
  realTimeUpdate,
  updateConfigs,
  deleteCollectorBySN,
  exportDetail,
  getPointBySn,
  exportSnAndPoint,
  removeCollectorBySN,
  selectDeviceConfig,
  getSecurityListApi,
  operateDoorApi,
  operateAirConditionerApi,
  thirdDeviceCtrlApi
};
