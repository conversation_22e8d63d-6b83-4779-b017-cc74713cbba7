import { postAction, getAction } from '@/api/common';
const url = process.env.VUE_APP_Health_BASE_URL;
// const url = 'http://10.5.5.133:8088';
// 获取告警事件列表
export const getAlarmEvents = (params) => postAction(url + '/diagnosis/list', params);

// 导出告警事件
export const exportAlarmEvents = (params) => postAction(url + '/diagnosis/export', params);

// 复核告警事件
export const reviewAlarmEvent = (params) => postAction(url + '/diagnosis/review', params);

// 删除告警事件
export const deleteAlarmEvent = (params) => postAction(url + '/diagnosis/delete', params);

// 关闭告警事件
export const closeAlarmEvent = (params) => postAction(url + '/diagnosis/close', params);

// 隔离告警事件
export const seprarateEvents = (params) => postAction(url + '/diagnosis/isolate', params);

// 派发查看历史选择项目接口
export const distributeHistory = (params) => postAction(url + '/diagnosis/distributeHistory', params);

// 获取时序分析测点曲线
export const faultCurve = (params) => postAction(url + '/diagnosis/faultCurve', params);

// 联动分析接口
export const linkageAnalysis = (params) => postAction(url + '/diagnosis/linkageAnalysis', params);

// 查询电站主环境检测仪
export const getMeteo = (params) => postAction(url + '/healthPowerStation/getMeteo', params);

// 获取待处理、已隔离、已派发总条数
export const getTotal = (params) => postAction(url + '/diagnosis/dataStatistical', params);

// 获取设备故障日历
export const getDeviceAlarmCalendar = (params) => postAction(url + '/diagnosis/getDeviceAlarmCalendar', params);

// 时序分析曲线图取得设备运行状态接口
export const getDeviceStatus = (params) => postAction(url + '/diagnosis/getDeviceStatus', params);

// 时序分析曲线图故障时间段接口
export const getFaultTimeRange = (params) => postAction(url + '/diagnosis/getFaultTimeRange', params);

// 时序分析曲线图故障时间段接口
export const getDefaultDeviceByPsId = (params) => postAction(url + '/diagnosis/getDefaultDeviceByPsId', params);

// 取得设备待处理故障信息
export const getFaultByPsKey = (params) => postAction(url + '/diagnosis/getFaultByPsKey', params);

// 获取health字典
export const getSystemCodeListWithChild = (params) => postAction(url + '/systemCode/getSystemCodeListWithChild', params);

// 查询指定时间段内的天气接口
export const getWeatherByTimeRange = (params) => postAction(url + '/healthPsWeather/getWeatherByTimeRange', params);

// 查询隔离详情
export const getIsolateDetail = (params) => postAction(url + '/diagnosis/isolateDetail', params);

// 取得电站下对应设备类型的测点集接口
export const queryDeviceTypePointByPsId = (params) => postAction(url + '/healthPowerStation/queryDeviceTypePointByPsId', params);

// 取得电站下对应设备类型的测点集接口 (新)
export const queryDeviceTypePointByMultiPsId = (params) => postAction(url + '/healthPowerStation/queryDeviceTypePointByMultiPsId', params);

// 获取故障原因标签字典
export const getDict = (params) => postAction(url + '/dict/getDictForStatus', params);

// 打单个标签
export const tagging = (params) => postAction(url + '/diagnosis/tagging', params);

// 批量打标签
export const taggingList = (params) => postAction(url + '/diagnosis/taggingList', params);

// 洞察工具添加常用测点
export const createViewTemplate = (params) => postAction(url + '/insightTools/viewTemplate/create', params);
// 洞察工具常用测点列表
export const viewTemplateList = (params) => postAction(url + '/insightTools/viewTemplate/list', params);
// 洞察工具删除常用测点
export const deleteViewTemplate = (params) => postAction(url + '/insightTools/viewTemplate/delete', params);

const twoUrl = process.env.VUE_APP_API_SEC_URL;
// 报警类型数据字典
export const getAlarmTypeList = (params) => postAction(url + '/dict/getDictListForGrade', params);
// 批量派发
export const batchDistributeEvent = (params) => postAction(twoUrl + '/default/management/faultDaily/distributeWorkOrderForList', params);
// 诊断中心tab页故障数量
export const dataStatisticalSumForRemarkTab = (params) => postAction(url + '/diagnosis/dataStatisticalSumForRemarkTab', params);
// 诊断中心tab页新增故障数量、环比数据
export const dataStatisticalRateForRemarkTab = (params) => postAction(url + '/diagnosis/dataStatisticalRateForRemarkTab', params);
// 诊断中心获取故障原因下拉选项
export const getReasonByRemarkAndDeviceType = (params) => postAction(url + '/diagnosis/getReasonByRemarkAndDeviceType', params);
// 动态获取alarmRemark
export const getRemarkDynamic = (params) => postAction(url + '/diagnosis/getRemarkAndReasonForGrade', params);

// 故障类型获取 diagnosis/getRemarkAndReasonGradeForSensor
export const getRemarkAndReasonGradeForSensorApi = (params) => {
    return postAction(url + "/diagnosis/getRemarkAndReasonGradeForSensor", params);
};
// 总数统计
export const dataStatisticalSumForSensorApi = (params) => {
    return postAction(url + "/diagnosis/dataStatisticalSumForSensor", params);
};
// 今日新增
export const dataStatisticalRateForSensorApi = (params) => {
    return postAction(url + "/diagnosis/dataStatisticalRateForSensor", params);
};
// 环境分析列表 diagnosis/listForSensor
export const listForSensorApi = (params) => {
    return postAction(url + "/diagnosis/listForSensor", params);
};
// /diagnosis/getDetailForSensor
export const getDetailForSensorApi = (params) => {
    return postAction(url + "/diagnosis/getDetailForSensor", params);
};
// 故障确认 diagnosis/affirmForSensor
export const affirmForSensorApi = (params) => {
    return postAction(url + "/diagnosis/affirmForSensor", params);
}
// 实时视频 /thirdDevice/deviceTree
export const thirdDeviceTreeApi = (params) => {
    return postAction(url + "/thirdDevice/deviceTree", params);
}

// 获取隐患运行新详情
export const getDetailForSensor = (params) => postAction(url + '/diagnosis/getDetailForSensor', params);

// 获取隐患运行新详情
export const getAlarm = (params) => postAction(url + '/diagnosis/getAlarm', params);

// 导出行为分析/环境诊断
export const exportForSensorApi = (params) => postAction(url + '/diagnosis/exportForSensor', params);

// 设备健康-诊断详情
export const diagnosisDetail = (params) => getAction(url + '/diagnosis/detail', params);

// 设备健康-工单详情
export const workOrderDetail = (params) => getAction(url + '/diagnosis/workOrderDetail', params);

export const alarmRemarkAndReasonApi = (params) => getAction(url + '/diagnosis/alarmRemarkAndReason', params);
// 设备健康-统计
export const diagnosisStatistics = (params) => getAction(url + '/diagnosis/statistics', params);
