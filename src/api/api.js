import { getAction, deleteAction, putAction, postAction } from '@/api/manage';
import { UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types';
const erpUrl = process.env.VUE_APP_API_ERP_URL;

// 角色管理
const addRole = (params) => postAction('/sys/role/add', params);
const editRole = (params) => putAction('/sys/role/edit', params);
const queryall = (params) => getAction('/sys/role/queryall', params);

// 用户管理
const addUser = (params) => postAction('/sys/user/add', params);
const editUser = (params) => putAction('/sys/user/edit', params);
const queryUserRole = (params) => getAction('/sys/user/queryUserRole', params);
const queryUserRole2 = (params) => getAction('/sys/user/queryUserRole2', params);
const getUserList = (params) => getAction('/sys/user/list', params);
const frozenBatch = (params) => putAction('/sys/user/frozenBatch', params);
// 验证用户是否存在
const checkOnlyUser = (params) => getAction('/sys/user/checkOnlyUser', params);
// 改变密码
const changePassword = (params) => postAction('/sys/user/changePassword', params);
// 权限管理
const addPermission = (params) => postAction('/sys/permission/add', params);
const editPermission = (params) => putAction('/sys/permission/edit', params);
const getPermissionList = (params) => getAction('/sys/permission/list', params);
const queryTreeList = (params) => getAction('/sys/permission/queryTreeList', params);
const queryTreeListForRole = (params) => getAction('/sys/role/queryTreeList', params);
const queryListAsync = (params) => getAction('/sys/permission/queryListAsync', params);
const queryRolePermission = (params) => getAction('/sys/permission/queryRolePermission', params);
const saveRolePermission = (params) => postAction('/sys/permission/saveRolePermission', params);
const queryPermissionsByUser = (params) => getAction('/sys/permission/getUserPermissionByToken', params);
const getPermissionRuleList = (params) => getAction('/sys/permission/getPermRuleListByPermId', params);
const queryPermissionRule = (params) => getAction('/sys/permission/queryPermissionRule', params);
const deptTree = (params) => getAction('/sys/tree/deptTree', params); // 2023 树改造，查询部门树
// 部门管理
const queryDepartTreeList = (params) => getAction('/sys/sysDepart/queryTreeList', params);
const queryDeptTree = (params) => getAction('/sys/sysDepart/queryDeptTree', params); // 2022-06-15追加
const searchByKeyword = (params) => getAction('/sys/sysDepart/searchByKeyword', params); // 2022-06-15追加
const queryUserDataRole = (params) => getAction('/sys/user/queryUserDataRole', params); // 2022-06-15追加
const queryDepartTreeListByOrgCode = (params) => getAction('/sys/sysDepart/queryDepartTreeListByOrgCode', params);
const deleteByDepartId = (params) => deleteAction('/sys/sysDepart/delete', params);
// 字典标签专用（通过code获取字典数组）
export const ajaxGetDictItems = (code, params) => getAction(`/sys/dict/getDictItems/${code}`, params);
// 从缓存中获取字典配置
function getDictItemsFromCache (dictCode) {
  if (Vue.ls.get(UI_CACHE_DB_DICT_DATA) && Vue.ls.get(UI_CACHE_DB_DICT_DATA)[dictCode]) {
    let dictItems = Vue.ls.get(UI_CACHE_DB_DICT_DATA)[dictCode];
    return dictItems;
  }
}

// 重复校验
const duplicateCheck = (params) => getAction('/sys/duplicate/check', params);
// 加载分类字典
const checkRuleByCode = (params) => getAction('/sys/checkRule/checkByCode', params);

// 同步所有人员信息
const baseUrl = process.env.VUE_APP_DING_BASE_URL;
const getSyncAll = (params) => postAction(baseUrl + '/syncAll', params);

/* 新加-数据角色管理 */
const getDepdetail = (params) => getAction('/sys/sysDepart/detail', params);
const dataRoleAdd = (params) => postAction('/sys/dataRole/add', params);
const dataRoleEdit = (params) => putAction('/sys/dataRole/edit', params);
const getDataRoleTree = (params) => getAction('/sys/dataRole/queryTreeList', params);
const getDataRoleTreeBy = (params) => getAction('/sys/dataRole/searchBy', params);
/* 新数据角色树 */
const myDeptTree = (params) => getAction('/sys/user/myDeptTree', params);
const relPsas = (params) => postAction('/sys/user/relPsas', params);
const currencyPsaList = (params) => getAction(erpUrl + '/system/v1/currency/psa-list/get', params);
const psPage = (params) => getAction(erpUrl + '/power-station/page', params);
const psas = (params) => postAction('/sys/user/psas', params);
const saveResourcePlanApi = (params) => postAction(erpUrl+'/system/v2/resourcePlan/save', params);
const getResourcePlanApi = (params) => postAction(erpUrl+'/system/v2/resourcePlan/query', params);

export {
  addRole,
  editRole,
  addUser,
  editUser,
  queryUserRole,
  queryUserRole2,
  getUserList,
  queryall,
  frozenBatch,
  checkOnlyUser,
  changePassword,
  getPermissionList,
  addPermission,
  editPermission,
  queryTreeList,
  deptTree,
  queryListAsync,
  queryRolePermission,
  saveRolePermission,
  queryPermissionsByUser,
  getPermissionRuleList,
  queryPermissionRule,
  queryDepartTreeList,
  queryDeptTree,
  searchByKeyword,
  deleteByDepartId,
  duplicateCheck,
  queryTreeListForRole,
  checkRuleByCode,
  getDictItemsFromCache,
  queryDepartTreeListByOrgCode,
  getSyncAll,
  getDepdetail,
  dataRoleAdd,
  dataRoleEdit,
  getDataRoleTree,
  getDataRoleTreeBy,
  queryUserDataRole,
  myDeptTree,
  relPsas,
  currencyPsaList,
  psPage,
  psas,
  saveResourcePlanApi,
  getResourcePlanApi
};
