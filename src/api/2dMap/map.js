import { getAction, postAction } from '@/api/common';
const baseUrl = process.env.VUE_APP_TANGO_BASE_URL;

const getPsAirLines = (params, bodyParams) => getAction(baseUrl + 
    `/cloud-wayline/api/v1/workspaces/${params.psaId}/map-info`, bodyParams); // 获取航线信息
const getBindLocation = (params) => postAction(baseUrl + '/model/tag/list/hasBindLocation', params); // 获取标记过位置的所有设备列表
const getBindArea = (params) => postAction(baseUrl + '/model/tag/list/hasBindArea', params); // 获取标记过边界的区域列表
const stationTowerInfo = (params) => postAction(baseUrl + '/insight/stationTowerInfo', params); // 获取电站杆塔位置信息
const geoMap = (params) => postAction(baseUrl + '/model/common/geoMap', params); // 获取电站geo地图信息
const addTask = (params) => postAction(baseUrl + '/task/fixed-point-recheck/add', params); // 新建定点复检任务
const endTask = (params) => postAction(baseUrl + '/task//fixed-point-recheck/end', params); // 结束定点复检任务
const lastTask = (params) => getAction(baseUrl + '/task/fixed-point-recheck/last-task', params); // 查询上次定点复检任务
const getTaginfo = (params) => getAction(baseUrl + `/v2/model/tag/moduleByCoordinate`, params); // 获取航线信息
const getDockList = (params) => getAction(baseUrl + '/cloud-manage/api/v1/devices/devices/bound/dock', params);
const getFaultList = (params) => getAction(baseUrl + '/task/fixed-point-recheck/fault-list', params);
export {
  getPsAirLines,
  getBindLocation,
  getBindArea,
  stationTowerInfo,
  geoMap,
  addTask,
  endTask,
  lastTask,
  getTaginfo,
  getDockList,
  getFaultList
}
