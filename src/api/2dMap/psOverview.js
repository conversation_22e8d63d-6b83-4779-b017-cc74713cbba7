import { getAction,postAction } from "../common";
let HEALTH_URL = process.env.VUE_APP_Health_BASE_URL;
let CARE_URL = process.env.VUE_APP_TANGO_BASE_URL;
const MONITOR_URL = process.env.VUE_APP_Health_BASE_MONITOR_URL

export const getModelData = (param)=>{
    return getAction(MONITOR_URL + '/data/getData',param)
}

// 根据psKey 获取组串或组件经纬度
export const getPositionsByPsKeys = (param)=>{
    return postAction(CARE_URL + '/v2/model/tag/tagPositionsByDeviceId',param)
}

// 处理告警
export const affirmForSensor = (param)=>{
    return postAction(HEALTH_URL + '/diagnosis/affirmForSensor',param)
}

// 处理告警
export const getBoosterRobotStatus = (param)=>{
    return getAction(HEALTH_URL + '/thirdDevice/device-status',param)
}

// 处理告警
export const getPointsData = (param)=>{
    return postAction(HEALTH_URL + '/healthDeviceAttribute/getLatestDataPoint',param)
}

// 处理告警
export const nonRealTimeDataUAV = (param)=>{
    return getAction(CARE_URL + '/insight/device/non-real-time-data',param)
}

// 处理告警
export const nonRealTimeDataNotUAV = (param)=>{
    return postAction(HEALTH_URL + '/query/getDeviceDelayedData',param)
}

// 更新是否展示天气效果
export const updateWeatherEffectSwitch = (param)=>{
    return postAction(HEALTH_URL + '/digitalIntelligencePs/updateWeatherEffectSwitch',param)
}
// 获取是否展示天气效果
export const getWeatherEffectSwitch = (param)=>{
    return getAction(HEALTH_URL + '/digitalIntelligencePs/getWeatherEffectSwitch',param)
}
// 实时检测告警是否有新告警，有新告警则弹出提示音
export const getAlarmHint = (param)=>{
    return getAction(HEALTH_URL + '/diagnosis/getAlarmHint', param)
}
// 光功率预测柜确认 diagnosisEvent/affirm
export const opticalPowerConfirmApi = (param)=>{
    return postAction(MONITOR_URL + '/diagnosisEvent/affirm', param)
}
//机器人执行动作
export const startRobotExecuteApi = (param)=>{
    return postAction(HEALTH_URL + '/thirdDevice/startDeviceToExecuteTask', param)
}