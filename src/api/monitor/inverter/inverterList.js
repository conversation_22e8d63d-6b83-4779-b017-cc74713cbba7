import { postAction } from '@/api/manage';
import { baseUrl } from '../runMonitor';

// 逆变器头部数据统计
const getStationStatisticsByPsId = (params) => postAction(baseUrl + '/monitor/stationStatisticsByPsId', params);
// 逆变器列表查询
const getInverterList = (params) => postAction(baseUrl + '/monitor/inverterList', params);
// 逆变器列表chart
const getInverterPowerChart = (params) => postAction(baseUrl + '/monitor/inverterPowerChart', params);
// 逆变器列表导出接口
const exportInverterList = (params) => postAction(baseUrl + '/monitor/exportInverterList', params);

// 汇流箱列表查询
const getCombinerBoxList = (params) => postAction(baseUrl + '/monitor/combinerBoxList', params);
// 汇流箱chart
const getCombinerBoxChart = (params) => postAction(baseUrl + '/monitor/combinerBoxChart', params);
// 逆变器列表导出接口
const exportCombinerBoxList = (params) => postAction(baseUrl + '/monitor/exportCombinerBoxList', params);

// 等效小时
const getEquivalentHoursChart = (params) => postAction(baseUrl + '/monitor/equivalentHoursChart', params);
const stationStatisticsByPsIdCn = (params) => postAction(baseUrl + '/monitorCn/stationStatisticsByPsIdCn', params);
export {
  getStationStatisticsByPsId,
  getInverterList,
  getInverterPowerChart,
  getEquivalentHoursChart,
  exportInverterList,
  getCombinerBoxList,
  getCombinerBoxChart,
  exportCombinerBoxList,
  stationStatisticsByPsIdCn
};
