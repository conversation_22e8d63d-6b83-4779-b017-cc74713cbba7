import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://10.5.9.126:8085';
const getHeadData = (params) => postAction(baseUrl + '/monitor/monitorHead', params);
// 电站模糊匹配
const findStationByName = (data) => postAction(baseUrl + '/monitor/findStationByName', data);
// 电站列表
const monitorPowerStationList = (data) => postAction(baseUrl + '/monitor/monitorPowerStationList', data);
// 电站折线图数据

const lineChart = (data) => postAction(baseUrl + '/monitor/lineChart', data);
// 获取实体电站树
const projectAndStationTree = (data) => postAction(baseUrl + '/monitor/projectAndStationTree', data);
// 获取上浮弹框列表
const getAlarmListPush = (data) => postAction(baseUrl + '/monitor/alarmListPush', data);
// 获取转隔离/解除隔离
const updateHealthOnlineAlarm = (data) => postAction(baseUrl + '/healthOnlineAlarm/updateHealthOnlineAlarm', data);

// 实体电站id反查电站档案id
const getPsaId = (data) => postAction(baseUrl + '/monitor/getPsaId', data);
const setStatusSign = (params) => postAction(baseUrl + '/healthPowerStation/listing', params);
// 五分钟刷新的问题
const getStationRefresh = (data) => postAction(baseUrl + '/monitor/monitorStationRefresh', data);

// 新增储能接口
const getMonitorHeadCn = (params) => postAction(baseUrl + '/monitorCn/getMonitorHeadCn', params);
const monitorPowerStationCnList = (data) => postAction(baseUrl + '/monitorCn/monitorPowerStationCnList', data);
const lineChartCn = (data) => postAction(baseUrl + '/monitorCn/lineChartCn', data);
const getStationRefreshCn = (data) => postAction(baseUrl + '/monitorCn/monitorStationRefreshCn', data);
export {
  projectAndStationTree,
  lineChart,
  monitorPowerStationList,
  findStationByName,
  getHeadData,
  getAlarmListPush,
  updateHealthOnlineAlarm,
  getPsaId,
  setStatusSign,
  getStationRefresh,
  baseUrl,
  getMonitorHeadCn,
  monitorPowerStationCnList,
  lineChartCn,
  getStationRefreshCn
};
