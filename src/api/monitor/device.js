import { postAction } from '@/api/manage';
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
// const baseUrl = 'http://10.5.9.126:8085';
//
const getFaultDeviceCount = (params) => postAction(baseUrl + '/monitorNew/getFaultDeviceCount', params);
// 逆变器页面
const getInverterAlarmCount = (params) => postAction(baseUrl + '/monitorNew/getInverterAlarmCount', params);// 故障设备数量统计
const getInverterModel = (params) => postAction(baseUrl + '/monitorNew/getInverterModel', params);// 设备型号下拉框
const getInverterAlarmList = (params) => postAction(baseUrl + '/monitorNew/getInverterAlarmList', params);// 设备列表查询
const getPsBaseInfo = (params) => postAction(baseUrl + '/monitorNew/getPsBaseInfo', params);
// 汇流箱页面
const getBoxAlarmList = (params) => postAction(baseUrl + '/monitorNew/getBoxAlarmList', params);
// 箱变页面
const getUnitAlarmCount = (params) => postAction(baseUrl + '/monitorNew/getUnitAlarmCount', params); // 故障设备数量统计
const getUnitAlarmList = (params) => postAction(baseUrl + '/monitorNew/getUnitAlarmList', params); // 箱变列表
// 升压站
const getBoosterStationAlarmList = (params) => postAction(baseUrl + '/monitorNew/getBoosterStationAlarmList', params);
// 判断是否具有光伏或储能tab页
const getPsCategory = (params) => postAction(baseUrl + '/monitorCn/getPsCategory', params);
const getDownDeviceType = (params) => postAction(baseUrl + '/monitorCn/getDownDeviceType', params);
const getCnMonitorDeviceTypePointMapping = (params) => postAction(baseUrl + '/monitorCn/getCnMonitorDeviceTypePointMapping', params); // 储能统计的设备及测点
const monitorInsightAnalysisDataCn = (params) => postAction(baseUrl + '/monitorCn/monitorInsightAnalysisDataCn', params); // 储能统计的曲线图
const getRackBMSDetail = (params) => postAction(baseUrl + '/monitorCn/getRackBMSDetail', params); // 详情页
const getRackBMSTree = (params) => postAction(baseUrl + '/monitorCn/getRackBMSTree', params); // rackBms 树
export {
  getFaultDeviceCount,
  getInverterAlarmCount,
  getInverterModel,
  getInverterAlarmList,
  getPsBaseInfo,
  getBoxAlarmList,
  getUnitAlarmCount,
  getUnitAlarmList,
  getBoosterStationAlarmList,
  getPsCategory,
  getDownDeviceType,
  getCnMonitorDeviceTypePointMapping,
  monitorInsightAnalysisDataCn,
  getRackBMSDetail,
  getRackBMSTree
};
