import { postAction, getAction } from "@/api/common";

const systemUrl = process.env.VUE_APP_API_BASE_URL;
const healthUrl = process.env.VUE_APP_Health_BASE_URL;

export const jwtApi = (params) => {
  return postAction(systemUrl + "/sys/jwt", params);
};

export const queryPermissionsByUserApi = (params) =>
  getAction("/sys/permission/getUserPermissionByToken", params);

export const getAccessCodeApi = (params) =>
  getAction(healthUrl + "/login/access/code", params);
