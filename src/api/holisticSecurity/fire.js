import { getAction, postAction } from "../common";
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
//
const getPannelStatus = (params) =>
  getAction(baseUrl + "/monitorNew/getFaultDeviceCount", params);
const setPannelStatus = (params) =>
  postAction(baseUrl + "/monitorNew/getFaultDeviceCount", params);
const operateFire = (params) =>
  postAction(baseUrl + "/monitorNew/operateFire", params);
export { getPannelStatus, setPannelStatus, operateFire };
