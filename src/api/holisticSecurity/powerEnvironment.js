import { getAction, postAction } from "../common";
const baseUrl = process.env.VUE_APP_Health_BASE_URL;
//

const getWaterMonitor = (params) =>
  postAction(baseUrl + "/monitorNew/getFaultDeviceCount", params);
const getWaterMonitorList = (params) =>
  postAction(baseUrl + "/monitorNew/getFaultDeviceList", params);
const getTempMonitor = (params) =>
  postAction(baseUrl + "/monitorNew/getFaultDeviceCount", params);
const getTempMonitorList = (params) =>
  postAction(baseUrl + "/monitorNew/getFaultDeviceList", params);
export {
  getWaterMonitor,
  getWaterMonitorList,
  getTempMonitor,
  getTempMonitorList,
};
