import { axios } from "@/utils/request";
let apiSecUrl = process.env.VUE_APP_API_SEC_URL;
const mcUrl = process.env.VUE_APP_MC_URL;
const systemUrl = process.env.VUE_APP_API_BASE_URL;
// post
export function postAction(url, parameter) {
  return axios({
    url: url,
    method: "post",
    data: parameter,
  });
}

// get
export function getAction(url, parameter) {
  return axios({
    url: url,
    method: "get",
    params: parameter,
  });
}

// 下载文件
export const exportFile = (params) => {
  return postAction(apiSecUrl + "/sys/oss/file/downLoad", params);
};

// 直播地址
export const pLayLiveApi = (params) => {
  return getAction(mcUrl + "/media/play/live/list", params);
};

// 云镜控制
export const controlLive = (params) => {
  return postAction(mcUrl + "/media/ctrl/camera", params);
};
// 获取系统配置
export const getConfigValueListApi = (params) =>
  getAction(systemUrl + "/business/config/value-list", params);

// 更新系统配置
export const updateConfigValueApi = (params) =>
  postAction(systemUrl + "/business/config/value-set", params);

export const hikvisionToken = (params) =>
  getAction(`${systemUrl}/sys/hikvision/token`, params, {
    credentials: "include",
  });

// 验证码校验 /captcha/check
export const checkCaptcha = (params) => postAction(systemUrl + "/captcha/check", params);
