import Vue from "vue";
import router from "./router";
import store from "./store";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import notification from "ant-design-vue/es/notification";
import { generateIndexRouter, generateUuid } from "@/utils/util";
const whiteList = ["/user/login", "/user/alteration"];

router.beforeEach((to, from, next) => {
  if (!localStorage.getItem("deviceId")) {
    localStorage.setItem("deviceId", generateUuid());
  }
  if (Vue.ls.get(ACCESS_TOKEN)) {
    if (Object.keys(store.getters.systemConfigMap).length === 0) {
      store.dispatch("app/GetSystemConfigList");
    }
    if (store.getters.permissionList.length === 0) {
      store
        .dispatch("GetPermissionList")
        .then((res) => {
          // next();
          const menuData = res;
          if (menuData === null || menuData === "" || menuData === undefined) {
            return;
          }
          let constRoutes = [];
          constRoutes = generateIndexRouter(menuData);
          // 添加主界面路由
          store
            .dispatch("UpdateAppRouter", {
              constRoutes,
            })
            .then(() => {
              // 根据roles权限生成可访问的路由表
              // 动态添加可访问路由表
              // router.addRoute(store.getters.addRouters);
              store.getters.addRouters.forEach((element) => {
                router.addRoute(element);
              });
              const redirect = decodeURIComponent(
                from.query.redirect || to.path
              );
              if (to.path === redirect) {
                router.push({ path: to.path });
                next({
                  ...to,
                  replace: true,
                });
              } else {
                // 跳转到目的路由
                router.push({ path: redirect });
                next();
              }
            });
        })
        .catch((err) => {
          notification.error({
            message: "系统提示",
            description: err || "没有权限，请联系管理员",
          });
        });
    } else {
      if (to.path === "/user/login") {
        next({ path: store.state.user.firstMenu.path });
      }
      next();
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next({
        path: "/user/login",
        query: {
          redirect: to.fullPath,
        },
      });
    }
    //window.location.href = `https://${process.env.VUE_APP_ENV}.isolareye.com/#/user/login` + '?backToDigital=1&clear=1';
  }
});
