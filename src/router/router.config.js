import TabLayout from "@/components/layouts/TabLayout";
import UserLayout from '@/components/layouts/UserLayout';
export const asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/2d',
    children: []
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
];
/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [{
  path: '/user',
  name: "login",
  component: UserLayout,
  redirect: '/user/login',
  hidden: true,
  children: [{
    path: 'login',
    name: 'login',
    component: () =>
                    import(/* webpackChunkName: "user" */ '@/views/user/Login')
  },
  {
    path: 'alteration',
    name: 'alteration',
    component: () =>
                    import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
  }
  ]
}
];

export const routes = [
  // {
  //   path: "/",
  //   component: TabLayout,
  //   redirect: "/dashboard",
  //   children: [
  //     {
  //       path: "/dashboard",
  //       name: "/dashboard",
  //       meta: {
  //         title: "实时监测",
  //         keepAlive: false,
  //         level: 1,
  //         customHeader: true,
  //       },
  //       // component: () => import("@/views/2dMap/index"),
  //       component: RouteView,
  //       redirect: "/dashboard/2d",
  //       children: [
  //         {
  //           path: "/dashboard/3d",
  //           name: "/dashboard/3d",
  //           meta: {
  //             title: "3D模式",
  //             keepAlive: false,
  //             level: 2,
  //             customHeader: true,
  //           },
  //           component: () => import("@/views/dashboard/3d"),
  //         },
  //         {
  //           path: "/dashboard/2d",
  //           name: "/dashboard/2d",
  //           meta: {
  //             title: "2D模式",
  //             keepAlive: false,
  //             level: 2,
  //             customHeader: true,
  //           },
  //           component: () => import("@/views/2dMap/index"),
  //         },
  //         {
  //           path: "/dashboard/diagram",
  //           name: "/dashboard/diagram",
  //           meta: {
  //             title: "主接线图",
  //             keepAlive: false,
  //             level: 2,
  //             customHeader: true,
  //           },
  //           component: () => import("@/views/dashboard/Diagram"),
  //         },
  //         {
  //           path: "/dashboard/equipment",
  //           name: "/dashboard/equipment",
  //           meta: {
  //             title: "设备结构",
  //             keepAlive: false,
  //             level: 2,
  //             customHeader: true,
  //           },
  //           component: () => import("@/views/dashboard/equipment"),
  //         },
  //         {
  //           path: "/dashboard/scenario",
  //           name: "/dashboard/scenario",
  //           meta: {
  //             title: "情景演练",
  //             keepAlive: false,
  //             level: 2,
  //             customHeader: true,
  //           },
  //           component: () => import("@/views/scenarioDrill"),
  //         },
  //       ],
  //     },
  //     {
  //       path: "/health",
  //       name: "/health",
  //       meta: { title: "智能诊断", keepAlive: false, level: 1 },
  //       component: RouteView,
  //       redirect: "/health/overview",
  //       children: [
  //         {
  //           path: "/health/overview",
  //           name: "/health/overview",
  //           meta: { title: "诊断概览", keepAlive: false, level: 2 },
  //           component: () => import("@/views/health/overview/overview"),
  //           svgIcon: "health-overview",
  //         },
  //         {
  //           path: "/health/safe",
  //           name: "/health/safe",
  //           meta: { title: "设备健康", keepAlive: false, level: 2 },
  //           component: () => import("@/views/health/device/DeviceModule"),
  //           svgIcon: "health-device",
  //         },
  //         {
  //           path: "/health/environment",
  //           name: "/health/environment",
  //           meta: { title: "环境诊断", keepAlive: false, level: 2 },
  //           component: () =>
  //             import("@/views/health/environmental/Environmental"),
  //           svgIcon: "health-environment",
  //         },
  //         {
  //           path: "/health/behavioral",
  //           name: "/health/behavioral",
  //           meta: { title: "行为分析", keepAlive: false, level: 2 },
  //           component: () => import("@/views/health/behavioral/Behavioral"),
  //           svgIcon: "health-behavior",
  //         },
  //       ],
  //     },
  //     {
  //       path: "/operations",
  //       name: "/operations",
  //       meta: { title: "运行分析", keepAlive: false, level: 1 },
  //       component: RouteView,
  //       redirect: "/operations/station",
  //       children: [
  //         {
  //           path: "/operations/station",
  //           name: "/operations/station",
  //           meta: { title: "电站分析", keepAlive: false, level: 2 },
  //           component: () => import("@/views/health/inspection/QualityInspection"),
  //           svgIcon: "operations-station",
  //         },
  //         {
  //           path: "/operations/device",
  //           name: "/operations/device",
  //           meta: { title: "设备分析", keepAlive: false, level: 2 },
  //           component: () => import("@/views/operations/device"),
  //           svgIcon: "operations-device",
  //         },
  //         {
  //           path: "/operations/electricity",
  //           name: "/operations/electricity",
  //           meta: { title: "电量分析", keepAlive: false, level: 2 },
  //           component: () => import("@/views/operations/electricity"),
  //           svgIcon: "operations-electricity",
  //         },
  //         {
  //           path: "/operations/graydamage",
  //           name: "/operations/graydamage",
  //           meta: { title: "灰损分析", keepAlive: false, level: 2 },
  //           component: () => import("@/views/operations/graydamage"),
  //           svgIcon: "operations-graydamage",
  //         },
  //       ]
  //     },
  //     {
  //       path: "/ecological",
  //       name: "/ecological",
  //       meta: { title: "智慧生态", keepAlive: false, level: 1 },
  //       component: () => import("@/views/ecological/SmartEcological"),
  //       children: [],
  //     },
      // {
      //   path: "/analysis",
      //   name: "/analysis",
      //   meta: { title: "自主研发", keepAlive: false, level: 1 },
      //   component: RouteView,
      //   redirect: "/analysis/device",
      //   children: [
      //     {
      //       path: "/analysis/device",
      //       name: "/analysis/device",
      //       meta: { title: "设备分析", keepAlive: false, level: 2 },
      //       component: () => import("@/views/analysis/device/DeviceAnalysis"),
      //       svgIcon: "operations-device",
      //     },

      //     {
      //       path: "/analysis/electricity",
      //       name: "/analysis/electricity",
      //       meta: { title: "电量分析", keepAlive: false, level: 2 },
      //       component: () => import("@/views/analysis/electric/ElectricAnalysis"),
      //       svgIcon: "operations-electricity",
      //     },
      //     {
      //       path: "/analysis/ashLoss",
      //       name: "/analysis/ashLoss",
      //       meta: { title: "灰损分析", keepAlive: false, level: 2 },
      //       component: () => import("@/views/analysis/ashLoss/AshLossAnalysis"),
      //       svgIcon: "operations-graydamage",
      //     },
      //   ]
      // }
  //   ],
  // },
];
