import Vue from "vue";
import Router from "vue-router";
import { constantRouterMap } from "./router.config.js";
// console.log(constantRouterMap);
Vue.use(Router);

const originalPush = Router.prototype.push;
const originalReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location, onResolve, onReject) {
  // if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function replace(location, onResolve, onReject) {
  // if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
  return originalReplace.call(this, location).catch((err) => err);
};
export default new Router({
  mode: "hash",
  routes: constantRouterMap,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});
