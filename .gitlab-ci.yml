stages:
  - build
  - docker-build
  - deploy

default:
  tags:
    - runner-2

# 全局变量
variables:
  APP_NAME: solareye-vue-intelligent
  BUCKET_NAME: solareye-vue-artifacts

# 构建 Vue 项目
build_vue:
  stage: build
  image: registry-vpc.cn-hangzhou.aliyuncs.com/zw-public/node:lts-alpine3.12-pnpm
  script:
    - yarn config set registry https://registry.npmmirror.com/
    - echo ">>>_ Start yarn install ..."
    - yarn install
    - echo ">>>_ Start yarn build ..."
    - yarn build --mode box
  artifacts:
    paths:
      - dist
    expire_in: 1 day

docker-build:
  image: registry-vpc.cn-hangzhou.aliyuncs.com/zw-private/gitlab-cicd:mvn3.6-jdk8-jdk11-redis5-mysql5.7-python23-sonar4.4.0-v1.0.2-docker-k8s
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  stage: docker-build
  script:
    - |
      MR_INFO=$(curl --header "PRIVATE-TOKEN: $GITLAB_READ_TOKEN" "https://gitlab.isolareye.com/api/v4/projects/$CI_PROJECT_ID/merge_requests?target_branch=$CI_COMMIT_BRANCH&per_page=1&state=merged")
    - echo $MR_INFO
    - message=$(echo "$MR_INFO" | jq -r '.[0].description')
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ .*hotfix.* ]]; then
        message=$CI_COMMIT_MESSAGE
      fi
    - echo "message=$message" > artifact.txt
    - GROUP_NAME=$(echo $CI_PROJECT_PATH | cut -d '/' -f 1)
    - echo "GROUP_NAME=$GROUP_NAME"
    - echo "IMAGE_NAMESPACE=$IMAGE_NAMESPACE"
    - echo "APP_NAME=$APP_NAME"
    - DOCKER_IMAGE="zw-public/nginx";
    - timestamp=$(date -u +%s)
    - offset="+8"
    - beijing_time=$(date -d "@$((timestamp + offset * 3600))" "+%Y%m%d-%H%M%S")
    - DOCKER_VERSION=$GROUP_NAME-$APP_NAME-$beijing_time-$CI_COMMIT_SHORT_SHA
    - docker login -u $HARBOR_CREDENTIALS_USR -p $HARBOR_CREDENTIALS_PSW $HARBOR_HOST
    - DOCKER_BUILDKIT=0 docker build -t $HARBOR_HOST/$DOCKER_IMAGE:$DOCKER_VERSION .
    - docker push ${HARBOR_HOST}/${DOCKER_IMAGE}:$DOCKER_VERSION
    - docker rmi ${HARBOR_HOST}/${DOCKER_IMAGE}:$DOCKER_VERSION
    - echo "DOCKER_VERSION=$DOCKER_VERSION" >> artifact.txt
  artifacts:
    paths:
      - artifact.txt
  rules:
    - if: '($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /^dev-.*$/ || $CI_COMMIT_BRANCH =~ /^feat-.*$/ || $CI_COMMIT_BRANCH =~ /^hotfix-.*$/)'

deploy_k8s_test:
  stage: deploy
  image: registry.cn-hangzhou.aliyuncs.com/zw-private/gitlab-cicd:mvn3.********************.7-python23-sonar4.4.0-v1.0.2-docker-k8s
  variables:
    K8S_NS: "dev"
  allow_failure: true
  script:
    - echo "deploy-dev---------------------------------deploy-dev"
    - mkdir -p ${PWD}/.gitlab/cd/cd_script
    - DOCKER_IMAGE="zw-public/nginx";
    - cat artifact.txt
    - DOCKER_VERSION=$(cat artifact.txt | grep DOCKER_VERSION= | cut -d'=' -f2)
    - message=$(cat artifact.txt | grep message= | cut -d'=' -f2)
    - mkdir -p ~/.kube
    - cp $K8S_CONFIG_STAGING ~/.kube/config
    - sed -e "s#{DEPLOY_PORT}#${DEPLOY_PORT}#g;s#{IMAGE_URL}#${HARBOR_HOST//-vpc/}/${DOCKER_IMAGE}#g;s#{IMAGE_TAG}#${DOCKER_VERSION}#g; s#{APP_NAME}#${APP_NAME}#g;s#{K8S_NS}#${K8S_NS}#g;s#{REGISTRY_SECRET}#${REGISTRY_SECRET}#g" deployment.tpl > k8s-deployment.yml
    - cat k8s-deployment.yml
    - kubectl apply -f k8s-deployment.yml --namespace=${K8S_NS}
  rules:
    - if: '($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /^dev-.*$/ || $CI_COMMIT_BRANCH =~ /^feat-.*$/ || $CI_COMMIT_BRANCH =~ /^hotfix-.*$/)'
  dependencies:
    - docker-build

deploy_test:
  stage: deploy
  image: registry-vpc.cn-hangzhou.aliyuncs.com/zw-public/node:lts-alpine3.12-pnpm
  script:
    - echo ">>>_ start init OSS config ..."
    - rm -rf ossutil64 && wget http://gosspublic.alicdn.com/ossutil/1.6.14/ossutil64 && chmod 755 ossutil64
    - cp $OSS_CONFIG_VUE /etc/oss_config
    - echo ">>>_ start upload dist to OSS ..."
    - ./ossutil64 rm --config-file /etc/oss_config -rf oss://$BUCKET_NAME/solareye-vue-intelligent/test
    - ./ossutil64 cp --config-file /etc/oss_config -rf dist/ oss://$BUCKET_NAME/solareye-vue-intelligent/test
  rules:
    - if: '($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /^dev-.*$/ || $CI_COMMIT_BRANCH =~ /^feat-.*$/ || $CI_COMMIT_BRANCH =~ /^hotfix-.*$/)'
      when: manual

deploy_pro:
  stage: deploy
  image: registry-vpc.cn-hangzhou.aliyuncs.com/zw-public/node:lts-alpine3.12-pnpm
  script:
    - echo ">>>_ start init OSS config ..."
    - rm -rf ossutil64 && wget http://gosspublic.alicdn.com/ossutil/1.6.14/ossutil64 && chmod 755 ossutil64
    - cp $OSS_CONFIG_VUE /etc/oss_config
    - echo ">>>_ start upload dist to OSS ..."
    - ./ossutil64 rm --config-file /etc/oss_config -rf oss://$BUCKET_NAME/solareye-vue-intelligent/prod
    - ./ossutil64 cp --config-file /etc/oss_config -rf dist/ oss://$BUCKET_NAME/solareye-vue-intelligent/prod
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $IS_DEPLOY_PRO == "true"'
      when: manual
