{"name": "solareye-vue-2dmap", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"ant-design-vue": "1.7.8", "axios": "^0.27.2", "colormap": "2.3.2", "core-js": "^3.37.1", "crypto-js": "^4.2.0", "echarts": "5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "flv.js": "^1.6.2", "gsap": "^3.12.5", "js-audio-recorder": "0.5.6", "leaflet": "^1.8.0", "leaflet-ant-path": "^1.3.0", "leaflet-polylinedecorator": "^1.6.0", "leaflet-rotatedmarker": "^0.2.0", "leaflet.pm": "^2.2.0", "less": "^3.9.0", "less-loader": "^4.1.0", "moment": "^2.30.1", "number-precision": "^1.6.0", "v-viewer": "1.6.4", "viewerjs": "^1.11.6", "vue": "^2.6.11", "vue-count-to": "^1.0.13", "vue-infinite-loading": "^2.4.5", "vue-ls": "^4.2.0", "vue-router": "^3.0.1", "vue-virtual-scroll-list": "^2.3.5", "vue-virtual-scroller": "^1.1.2", "vuex": "3.1.0", "vxe-table": "3.1.0", "wavesurfer.js": "6.6.4", "xe-utils": "3.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.5.4", "@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.0.2", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-require-context": "^0.1.1", "clean-webpack-plugin": "3.0.0", "compression-webpack-plugin": "3.1.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^8.2.0", "style-resources-loader": "^1.5.0", "svg-sprite-loader": "^4.1.6", "uglifyjs-webpack-plugin": "2.2.0", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "license": "UNLICENSED"}