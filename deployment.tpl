---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: dp-{APP_NAME}
  namespace: {K8S_NS}
  labels:
    velero: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {APP_NAME}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: {APP_NAME}
    spec:
      volumes:
        - name: nginx-main-conf
          configMap:
            name: nginx-main-conf
            defaultMode: 420
      containers:
        - name: {APP_NAME}
          image: {IMAGE_URL}:{IMAGE_TAG}
          ports:
            - containerPort: 80
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: nginx-main-conf
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
          livenessProbe:
            httpGet:
              path: /
              port: 80
              scheme: HTTP
            initialDelaySeconds: 15
            timeoutSeconds: 1
            periodSeconds: 20
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 80
              scheme: HTTP
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext:
        fsGroup: 0
      imagePullSecrets:
        - name: {REGISTRY_SECRET}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: nginx-main-conf
  namespace: {K8S_NS}
  labels:
    velero: frontend
data:
  nginx.conf: |
    # For more information on configuration, see:
    #   * Official English Documentation: http://nginx.org/en/docs/
    #   * Official Russian Documentation: http://nginx.org/ru/docs/

    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log;
    pid /run/nginx.pid;

    # Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
    include /usr/share/nginx/modules/*.conf;

    events {
        worker_connections 1024;
    }

    http {
        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';

        access_log  /var/log/nginx/access.log  main;
        gzip                on;
        gzip_comp_level     6;
        gzip_types image/png text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
        gzip_vary           on;
        gzip_min_length     1024;
        gzip_proxied        any;
        
        sendfile            on;
        tcp_nopush          on;
        tcp_nodelay         on;
        keepalive_timeout   650;
        types_hash_max_size 4096;
        client_max_body_size 100M;
        underscores_in_headers on;
        include             /etc/nginx/mime.types;
        default_type        application/octet-stream;
        include /etc/nginx/conf.d/*.conf;

        server {
                listen       80;
                #server_name  model.isolareye.com;  #修改域名
                root         /usr/share/nginx/html;

                #ssl_certificate "/etc/pki/nginx/server.pem";
                #ssl_certificate_key "/etc/pki/nginx/private/server.key";
                #ssl_session_cache shared:SSL:1m;
                #ssl_session_timeout  10m;
                #ssl_ciphers HIGH:!aNULL:!MD5;
                #ssl_prefer_server_ciphers on;
        }
    }
---
kind: Service
apiVersion: v1
metadata:
  name: sv-{APP_NAME}
  namespace: {K8S_NS}
  labels:
    app: sv-{APP_NAME}
    velero: frontend
spec:
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  selector:
    app: {APP_NAME}
  type: NodePort
  sessionAffinity: None
