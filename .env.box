NODE_ENV=production
VUE_APP_ENV=box
# 开启微应用模式
VUE_APP_QIANKUN=true
VUE_APP_API_DOMAIN=http://192.168.1.121
VUE_APP_API_BASE_URL=http://192.168.1.121/solareye-system
VUE_APP_API_ERP_URL=http://192.168.1.121/isolar-erp
VUE_APP_API_MSG_URL=http://192.168.1.121/solareye-msg
VUE_APP_API_CODE_URL=http://app.isolareye.com/fat/app.png
VUE_APP_API_SEC_URL=http://192.168.1.121/solareyetwo
VUE_APP_AIGC_URL=http://aigc-fat.isolareye.com/#/index
VUE_APP_AIGC_API_URL=http://192.168.1.121/solareye-aigc
VUE_APP_Health_BASE_URL=http://192.168.1.121/isolar-health
VUE_APP_Health_BASE_DATA_URL=http://192.168.1.121/solareye-basic-data
VUE_APP_Health_BASE_MONITOR_URL=http://192.168.1.121/isolar-health-monitor
VUE_APP_BI_BASE_URL=http://192.168.1.121/isolarerpbi
VUE_APP_DING_BASE_URL=http://192.168.1.121/dingding
VUE_APP_TANGO_BASE_URL=http://192.168.1.121/solareyecare
VUE_APP_API_SHOP_URL=http://192.168.1.121/solareye-shop
VUE_APP_OSS_FILE_URL=http://192.168.1.121/solareyemc/oss/v1
VUE_APP_DM_BASE_URL=http://192.168.1.121/solareyedm
VUE_APP_IOT_BASE_URL=http://192.168.1.121/solareyeiot
VUE_APP_RDP_URL=http://192.168.1.121/RDP-SERVER
VUE_APP_MC_URL=http://192.168.1.121/solareyemc
# 微应用列表必须VUE_APP_SUB_开头,solarCare为子应用的项目名称,也是子应用的路由父路径
VUE_APP_SUB_solarCare = '/solarCare/'

