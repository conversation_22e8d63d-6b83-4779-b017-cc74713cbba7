module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es2021: true,
  },
  extends: ["plugin:vue/essential", "eslint:recommended"],
  parserOptions: {
    ecmaVersion: 12,
    parser: "@babel/eslint-parser",
    requireConfigFile: false,
  },
  globals: {
    gsap: "readonly",
    $: "readonly",
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "vue/multi-word-component-names": "off",
    "vue/no-mutating-props": "off",
    'indent': [2, 2, { SwitchCase: 1 }], // 缩进风格
    'eqeqeq': [0, 'always'], // 关闭要求使用 === 和 !==
    'semi': [2, 'always'], // 语句强制分号结尾
    'no-undef': 0, // 不能有未定义的变量
    'no-useless-escape': 0, // 禁止不必要的转义字符
    'camelcase': 0, // 变量名使用驼峰,
    'vue/no-parsing-error': 0,
    'vue/no-reserved-keys': 0
  },
};
