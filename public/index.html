<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="expires" content="no-cache">
    <meta http-equiv="cache" content="no-cache">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <link rel="icon" href="<%= BASE_URL %>solareye_logo.svg">
    <script src="/config.js" rel="preload"></script>
    <script src="./js/jQuery/jquery.min.js" rel="preload"></script>
    <style>
        body {
            height: 100%;
            margin: 0px;
            padding: 0px;
            background-size: cover;
            overflow: hidden;
        }

        .loading-container {
            width: 100%;
            /* background: url('./bg.svg') no-repeat; */
            background-size: cover;
            display: inline-block;
            overflow: hidden;
            position: absolute;
            top: 50%;
            left: 50%;
            text-align: center;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            color: #000000;
            height: 100%;
        }

        div.loading-main {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .bg-left {
            display: flex;
            flex-direction: column;
            margin: 3% 0 3% 5%;
            align-items: baseline;
            width: 30%;
        }

        .bg-left-title,
        .bg-left-device-title {
            font-family: Impact;
            font-size: 60px;
            color: #0C3763;
            letter-spacing: 5.6px;
            line-height: 82px;
        }

        .bg-left-title img {
            height: 40px;
            object-fit: cover;
        }

        .bg-left-device-title {
            opacity: 0.8;
            font-family: PingFangSC-Medium;
            letter-spacing: 0;
            margin-top: 4px;
        }

        .bg-right {
            flex: 1;
            width: fit-content;
            margin-right: 5%;

        }

        .bg-right img {
            width: 100%;
            height: 100%;
        }

        .box {
            width: 80%;
            height: 10px;
            background: #D8D8D8;
            border-radius: 10px;
            margin-top: 16px;
            position: relative;
            z-index: -1;
            box-sizing: content-box;
        }

        .logo-pos {
            position: fixed;
            top: 3%;
            left: 4%;
            height: 40px;
            object-fit: cover;
        }

        .loding-title {
            opacity: 0.7;
            font-family: PingFangSC-Medium;
            font-size: 16px;
            color: #999999;
            ;
            letter-spacing: 0;
            margin-top: 16px;
            text-align: left;
        }
        .process-child {
            position: relative;
            height: 10px;
            margin: 0;
            border-radius: inherit;
            background: linear-gradient(270deg, #FF7900 0%, #FF9200 100%);
            width: 0%;
        }

        .process-animate {
            position: absolute;
            left: 0;
            top: 0;
            transform: translateZ(0);
            will-change: transform;
            animation: process 1s linear forwards;
        }

        @keyframes process {
            from {
                transform: scaleX(0);
            }

            to {
                transform: scaleX(1);
            }
        }
    </style>
    <script>
        let width = 0, time = "";
        time = setInterval(() => {
            width = width + 10;
            let url = window.location.href;
            if (width <= 100) {
                if (document.getElementsByClassName("process-child").length > 0) {
                    document.getElementsByClassName("process-child")[0].style.width = width + "%";
                } else {
                    document.body.classList.add('bg')
                    clearInterval(time);
                }
            } else {
                width = 0;
            }
        }, 800);
        if (width >= 100) {
            document.body.classList.add('bg')
            clearInterval(time);
        }
    </script>
</head>

<body>
    <div class="loading-container" id="solarEyeLoading">
        <img src="./login_logo.png" alt="" class="logo-pos isVisible" rel="preload" style="transform: translate3d(0px, 0px, 0px)">
        <div class="loading-main">
            <div class="bg-left">
                <div class="bg-left-title isVisible">
                    <img src="./solareye.png" alt="solareye" rel="preload" style="transform: translate3d(0px, 0px, 0px)">
                </div>
                <div class="box">
                    <div class="process-child">
                        <p class="process-animate"></p>
                    </div>
                    <div class="loding-title">正在加载，请稍后...</div>
                </div>
            </div>
            <div class="bg-right">
                <img src="./loading.gif" class="isVisible" rel="preload" style="transform: translate3d(0px, 0px, 0px)">
            </div>
        </div>
    </div>
    <div id="app"></div>
    <div id="iframe-container"></div>
</body>
 <script src="/js/h5player.min.js"></script>
</html>